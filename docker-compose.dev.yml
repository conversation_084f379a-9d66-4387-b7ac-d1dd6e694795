version: '3.8'

services:
  # 开发环境主应用服务
  magnet-downloader:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: magnet-downloader-dev
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "2345:2345"  # Delve调试端口
    environment:
      - APP_ENV=development
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=magnet_downloader_dev
      - DB_USER=postgres
      - DB_PASSWORD=postgres123
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis123
      - ARIA2_HOST=aria2
      - ARIA2_PORT=6800
      - ARIA2_SECRET=aria2secret
      - JWT_SECRET=dev-jwt-secret-key
      - LOG_LEVEL=debug
      - DEV_RELOAD=true
      - DEV_CORS_ENABLED=true
    volumes:
      - .:/app
      - ./downloads:/app/downloads
      - ./logs:/app/logs
      - go_mod_cache:/go/pkg/mod
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      aria2:
        condition: service_started
    networks:
      - magnet-network-dev
    command: ["air", "-c", ".air.toml"]

  # PostgreSQL数据库（开发环境）
  postgres:
    image: postgres:15-alpine
    container_name: postgres-dev
    restart: unless-stopped
    environment:
      - POSTGRES_DB=magnet_downloader_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres123
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./deployments/docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - magnet-network-dev
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d magnet_downloader_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存（开发环境）
  redis:
    image: redis:7-alpine
    container_name: redis-dev
    restart: unless-stopped
    command: redis-server --requirepass redis123 --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    networks:
      - magnet-network-dev
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Aria2下载引擎（开发环境）
  aria2:
    image: p3terx/aria2-pro:latest
    container_name: aria2-dev
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - RPC_SECRET=aria2secret
      - RPC_PORT=6800
      - LISTEN_PORT=6888
      - DISK_CACHE=64M
      - IPV6_MODE=false
      - UPDATE_TRACKERS=true
      - TZ=Asia/Shanghai
    volumes:
      - ./downloads:/downloads
      - aria2_dev_config:/config
    ports:
      - "6800:6800"
      - "6888:6888"
      - "6888:6888/udp"
    networks:
      - magnet-network-dev

  # 前端开发服务器
  frontend:
    build:
      context: ./web
      dockerfile: Dockerfile.dev
    container_name: frontend-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./web:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - CHOKIDAR_USEPOLLING=true
    networks:
      - magnet-network-dev
    command: ["npm", "start"]

  # 数据库管理工具
  adminer:
    image: adminer:latest
    container_name: adminer-dev
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=postgres
    networks:
      - magnet-network-dev
    depends_on:
      - postgres

  # Redis管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander-dev
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:redis123
    networks:
      - magnet-network-dev
    depends_on:
      - redis

  # 邮件测试服务器
  mailhog:
    image: mailhog/mailhog:latest
    container_name: mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - magnet-network-dev

# 开发环境数据卷
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  aria2_dev_config:
    driver: local
  go_mod_cache:
    driver: local

# 开发环境网络
networks:
  magnet-network-dev:
    driver: bridge
