package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/database"
	"magnet-downloader/pkg/logger"
)

// 演示用的磁力链接
const DEMO_MAGNET_URI = "magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5"

func main() {
	fmt.Println("🚀 启动磁力下载和文件处理演示程序")
	fmt.Println("📋 目标磁力链接:", DEMO_MAGNET_URI)
	fmt.Println()

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger.Init(cfg.Log.Level, cfg.Log.Format)

	// 初始化数据库
	if err := database.Init(&cfg.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 创建服务
	repo := repository.NewRepository(database.GetDB())
	services := service.NewServices(repo, cfg)

	// 创建演示用户
	demoUser, err := createDemoUser(services.User)
	if err != nil {
		log.Fatalf("Failed to create demo user: %v", err)
	}

	fmt.Printf("✅ 创建演示用户: %s (ID: %d)\n", demoUser.Username, demoUser.ID)

	// 创建下载任务
	task, err := createDownloadTask(services.Task, demoUser.ID)
	if err != nil {
		log.Fatalf("Failed to create download task: %v", err)
	}

	fmt.Printf("✅ 创建下载任务: %s (ID: %d)\n", task.TaskName, task.ID)

	// 启动下载任务
	if err := services.Download.StartDownload(task.ID); err != nil {
		log.Fatalf("Failed to start download task: %v", err)
	}

	fmt.Printf("✅ 启动下载任务: %d\n", task.ID)

	// 监控下载进度
	fmt.Println("📊 开始监控下载进度...")
	go monitorDownloadProgress(services.Download, task.ID)

	// 启动文件处理服务
	fmt.Println("🔄 启动文件处理服务...")
	go startFileProcessingService(services.FileProcessing)

	// 启动队列处理
	fmt.Println("📋 启动队列处理服务...")
	go startQueueProcessing(services.Queue)

	// 等待中断信号
	fmt.Println("🎯 系统运行中，按 Ctrl+C 停止...")
	waitForShutdown()

	fmt.Println("👋 演示程序结束")
}

// createDemoUser 创建演示用户
func createDemoUser(userService service.UserService) (*model.User, error) {
	req := &service.CreateUserRequest{
		Username: "demo_user",
		Email:    "<EMAIL>",
		Password: "demo123456",
		Role:     "user",
	}

	// 检查用户是否已存在
	if user, err := userService.GetUserByUsername(req.Username); err == nil {
		return user, nil
	}

	return userService.CreateUser(req)
}

// createDownloadTask 创建下载任务
func createDownloadTask(taskService service.TaskService, userID uint) (*model.DownloadTask, error) {
	req := &service.CreateTaskRequest{
		MagnetURI: DEMO_MAGNET_URI,
		TaskName:  "SSIS-936-C_GG5 演示任务",
		SavePath:  "/tmp/downloads",
		Priority:  2, // 正常优先级
	}

	return taskService.CreateTask(userID, req)
}

// monitorDownloadProgress 监控下载进度
func monitorDownloadProgress(downloadService service.DownloadService, taskID uint) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 同步任务状态
			if err := downloadService.SyncTaskStatus(taskID); err != nil {
				logger.Errorf("Failed to sync task status: %v", err)
				continue
			}

			// 同步任务状态
			if err := downloadService.SyncTaskStatus(taskID); err != nil {
				logger.Errorf("Failed to sync task status: %v", err)
				continue
			}

			fmt.Printf("📊 监控中... 任务ID: %d\n", taskID)

			// 这里可以添加更详细的状态检查
			// 暂时简化处理
		}
	}
}

// startFileProcessingService 启动文件处理服务
func startFileProcessingService(fileProcessingService service.FileProcessingService) {
	// 这里可以添加文件处理服务的启动逻辑
	// 在实际实现中，文件处理会在下载完成后自动触发
	fmt.Println("🔄 文件处理服务已启动，等待下载完成...")
}

// startQueueProcessing 启动队列处理
func startQueueProcessing(queueService service.QueueService) {
	// 启动下载任务队列处理
	go func() {
		if err := queueService.ProcessDownloadTasks(); err != nil {
			logger.Errorf("Download queue processing error: %v", err)
		}
	}()

	// 启动文件处理队列处理
	go func() {
		if err := queueService.ProcessFileProcessingTasks(); err != nil {
			logger.Errorf("File processing queue error: %v", err)
		}
	}()

	// 启动通知队列处理
	go func() {
		if err := queueService.ProcessNotifications(); err != nil {
			logger.Errorf("Notification queue processing error: %v", err)
		}
	}()

	fmt.Println("📋 所有队列处理服务已启动")
}

// waitForShutdown 等待关闭信号
func waitForShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	fmt.Println("\n🛑 接收到关闭信号，正在优雅关闭...")
}
