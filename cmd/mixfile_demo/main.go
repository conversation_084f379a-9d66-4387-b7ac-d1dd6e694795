package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/mixfile"
	"magnet-downloader/pkg/steganography"
)

func main() {
	fmt.Println("🎯 MixFile系统演示程序")
	fmt.Println(strings.Repeat("=", 60))

	// 初始化日志
	logger.Init("info", "text")

	// 创建临时目录
	tempDir := "/tmp/mixfile_demo"
	os.MkdirAll(tempDir, 0755)

	scanner := bufio.NewScanner(os.Stdin)

	for {
		fmt.Println("\n📋 请选择操作:")
		fmt.Println("1. 创建测试文件并生成分享码")
		fmt.Println("2. 从分享码下载文件")
		fmt.Println("3. 隐写术演示")
		fmt.Println("4. 性能测试")
		fmt.Println("5. 系统信息")
		fmt.Println("0. 退出")
		fmt.Print("请输入选项 (0-5): ")

		if !scanner.Scan() {
			break
		}

		choice := strings.TrimSpace(scanner.Text())

		switch choice {
		case "1":
			demoCreateShareCode(tempDir, scanner)
		case "2":
			demoDownloadFromShareCode(tempDir, scanner)
		case "3":
			demoSteganography(tempDir, scanner)
		case "4":
			demoPerformanceTest()
		case "5":
			demoSystemInfo()
		case "0":
			fmt.Println("👋 感谢使用MixFile系统演示程序！")
			return
		default:
			fmt.Println("❌ 无效选项，请重新选择")
		}
	}
}

func demoCreateShareCode(tempDir string, scanner *bufio.Scanner) {
	fmt.Println("\n🔧 创建测试文件并生成分享码")
	fmt.Println(strings.Repeat("-", 40))

	// 获取文件内容
	fmt.Print("请输入文件内容 (或按回车使用默认内容): ")
	scanner.Scan()
	content := strings.TrimSpace(scanner.Text())
	if content == "" {
		content = fmt.Sprintf("Hello, MixFile! 这是一个测试文件，创建时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	}

	// 创建测试文件
	filename := fmt.Sprintf("test_%d.txt", time.Now().Unix())
	filePath := filepath.Join(tempDir, filename)
	
	err := os.WriteFile(filePath, []byte(content), 0644)
	if err != nil {
		fmt.Printf("❌ 创建文件失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 文件创建成功: %s\n", filePath)
	fmt.Printf("📄 文件内容: %s\n", content)
	fmt.Printf("📏 文件大小: %d bytes\n", len(content))

	// 模拟MixFile处理流程
	fmt.Println("\n🔄 开始MixFile处理流程...")

	// 步骤1：隐写术处理
	fmt.Println("1. 应用隐写术...")
	steganographer := steganography.NewSteganographer(steganography.DefaultConfig())
	pngData, err := steganographer.HideDataInPNG([]byte(content))
	if err != nil {
		fmt.Printf("❌ 隐写术处理失败: %v\n", err)
		return
	}
	fmt.Printf("   ✅ 隐写术完成: %d bytes -> %d bytes PNG\n", len(content), len(pngData))

	// 步骤2：创建索引文件
	fmt.Println("2. 创建索引文件...")
	chunks := []mixfile.ChunkInfo{
		{
			Index: 0,
			URL:   "https://i.ibb.co/demo_chunk.png", // 模拟URL
			Hash:  "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
			Size:  int64(len(content)),
		},
	}

	metadata := map[string]interface{}{
		"filename":           filename,
		"chunk_size":         1024,
		"encryption_key":     "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef",
		"original_file_hash": "original_hash_here",
	}

	indexManager := mixfile.NewIndexManager()
	index, err := indexManager.CreateIndex(chunks, metadata)
	if err != nil {
		fmt.Printf("❌ 创建索引失败: %v\n", err)
		return
	}
	fmt.Printf("   ✅ 索引创建完成: %s\n", index.FileName)

	// 步骤3：生成分享码
	fmt.Println("3. 生成分享码...")
	shareCodeProcessor := mixfile.NewShareCodeProcessor(true)
	shareCode, err := shareCodeProcessor.IndexToShareCode(index, "https://i.ibb.co/demo_index.png")
	if err != nil {
		fmt.Printf("❌ 生成分享码失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 分享码生成完成\n")
	fmt.Println("\n🎉 MixFile处理完成！")
	fmt.Printf("🔗 分享码: %s\n", shareCode)
	fmt.Printf("📏 分享码长度: %d 字符\n", len(shareCode))
	
	// 保存分享码到文件
	shareCodeFile := filepath.Join(tempDir, "latest_share_code.txt")
	os.WriteFile(shareCodeFile, []byte(shareCode), 0644)
	fmt.Printf("💾 分享码已保存到: %s\n", shareCodeFile)
}

func demoDownloadFromShareCode(tempDir string, scanner *bufio.Scanner) {
	fmt.Println("\n📥 从分享码下载文件")
	fmt.Println(strings.Repeat("-", 40))

	// 获取分享码
	fmt.Print("请输入分享码 (或按回车使用最新生成的分享码): ")
	scanner.Scan()
	shareCode := strings.TrimSpace(scanner.Text())

	if shareCode == "" {
		// 尝试读取最新的分享码
		shareCodeFile := filepath.Join(tempDir, "latest_share_code.txt")
		data, err := os.ReadFile(shareCodeFile)
		if err != nil {
			fmt.Printf("❌ 未找到分享码文件，请先生成分享码或手动输入\n")
			return
		}
		shareCode = string(data)
		fmt.Printf("📖 使用最新分享码: %s\n", shareCode[:50]+"...")
	}

	// 创建下载器
	config := mixfile.DefaultDownloaderConfig()
	config.TempDir = filepath.Join(tempDir, "downloads")
	downloader := mixfile.NewMixFileDownloader(config)

	// 解析分享码
	fmt.Println("\n🔍 解析分享码...")
	_, err := downloader.ParseShareCode(shareCode)
	if err != nil {
		fmt.Printf("❌ 分享码解析失败: %v\n", err)
		return
	}

	// 获取文件信息
	fileInfo, err := downloader.GetFileInfo(shareCode)
	if err != nil {
		fmt.Printf("❌ 获取文件信息失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 分享码解析成功\n")
	fmt.Printf("📁 文件名: %s\n", fileInfo.FileName)
	fmt.Printf("📏 文件大小: %d bytes\n", fileInfo.FileSize)
	fmt.Printf("📊 分片数量: %d\n", fileInfo.ChunkCount)
	fmt.Printf("🔐 是否加密: %t\n", fileInfo.Encrypted)

	// 模拟下载过程
	fmt.Println("\n📥 开始下载过程...")
	outputPath := filepath.Join(tempDir, "downloaded_"+fileInfo.FileName)

	// 进度回调
	progressCallback := func(progress *mixfile.DownloadProgress) {
		fmt.Printf("\r🔄 %s: %.1f%% (总体: %.1f%%) - %s", 
			progress.Stage, progress.Progress, progress.OverallProgress, progress.Message)
		
		if progress.Stage == "completed" {
			fmt.Println()
		}
	}

	// 注意：这里是模拟下载，实际环境中需要真实的URL
	fmt.Printf("⚠️ 注意: 这是演示模式，实际下载需要真实的分片URL\n")
	fmt.Printf("📍 模拟下载到: %s\n", outputPath)

	// 模拟下载进度
	stages := []string{"parsing_share_code", "downloading_index", "downloading_chunks", "reassembling_file", "verifying_integrity", "completed"}
	for i, stage := range stages {
		progress := &mixfile.DownloadProgress{
			Stage:           stage,
			Progress:        100,
			OverallProgress: float64(i+1) * 100 / float64(len(stages)),
			ChunkIndex:      i + 1,
			ChunkCount:      len(stages),
			Message:         fmt.Sprintf("正在执行: %s", stage),
		}
		progressCallback(progress)
		time.Sleep(500 * time.Millisecond)
	}

	fmt.Println("🎉 下载演示完成！")
}

func demoSteganography(tempDir string, scanner *bufio.Scanner) {
	fmt.Println("\n🖼️ 隐写术演示")
	fmt.Println(strings.Repeat("-", 40))

	// 获取要隐藏的数据
	fmt.Print("请输入要隐藏的数据: ")
	scanner.Scan()
	data := strings.TrimSpace(scanner.Text())
	if data == "" {
		data = "这是一个隐写术测试数据 - MixFile Demo"
	}

	fmt.Printf("📝 原始数据: %s\n", data)
	fmt.Printf("📏 数据大小: %d bytes\n", len(data))

	// 创建隐写术处理器
	config := steganography.DefaultConfig()
	steganographer := steganography.NewSteganographer(config)

	// 计算所需图片大小
	width, height := steganographer.CalculateRequiredImageSize(len(data))
	fmt.Printf("🖼️ 所需图片大小: %dx%d\n", width, height)

	// 隐藏数据
	fmt.Println("\n🔄 正在隐藏数据到PNG...")
	start := time.Now()
	pngData, err := steganographer.HideDataInPNG([]byte(data))
	hideTime := time.Since(start)

	if err != nil {
		fmt.Printf("❌ 隐藏失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 隐藏完成: %v\n", hideTime)
	fmt.Printf("🖼️ PNG大小: %d bytes\n", len(pngData))
	fmt.Printf("📈 大小增长: %.1fx\n", float64(len(pngData))/float64(len(data)))

	// 保存PNG文件
	pngFile := filepath.Join(tempDir, fmt.Sprintf("steganography_%d.png", time.Now().Unix()))
	err = os.WriteFile(pngFile, pngData, 0644)
	if err != nil {
		fmt.Printf("❌ 保存PNG失败: %v\n", err)
		return
	}
	fmt.Printf("💾 PNG已保存: %s\n", pngFile)

	// 提取数据
	fmt.Println("\n🔄 正在从PNG提取数据...")
	start = time.Now()
	extractedData, err := steganographer.ExtractDataFromPNG(pngData)
	extractTime := time.Since(start)

	if err != nil {
		fmt.Printf("❌ 提取失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 提取完成: %v\n", extractTime)
	fmt.Printf("📝 提取数据: %s\n", string(extractedData))
	fmt.Printf("📏 提取大小: %d bytes\n", len(extractedData))

	// 验证数据一致性
	if string(extractedData) == data {
		fmt.Printf("✅ 数据一致性验证通过\n")
	} else {
		fmt.Printf("❌ 数据一致性验证失败\n")
	}

	fmt.Println("\n📊 性能统计:")
	fmt.Printf("  隐藏速度: %.2f KB/s\n", float64(len(data))/hideTime.Seconds()/1024)
	fmt.Printf("  提取速度: %.2f KB/s\n", float64(len(data))/extractTime.Seconds()/1024)
}

func demoPerformanceTest() {
	fmt.Println("\n⚡ 性能测试")
	fmt.Println(strings.Repeat("-", 40))

	// 测试数据大小
	testSizes := []int{100, 1024, 10240} // 100B, 1KB, 10KB

	steganographer := steganography.NewSteganographer(steganography.DefaultConfig())

	fmt.Printf("%-10s %-15s %-15s %-15s %-10s\n", "大小", "隐藏时间", "提取时间", "PNG大小", "压缩比")
	fmt.Println(strings.Repeat("-", 70))

	for _, size := range testSizes {
		// 生成测试数据
		testData := make([]byte, size)
		for i := range testData {
			testData[i] = byte(i % 256)
		}

		// 测试隐藏性能
		start := time.Now()
		pngData, err := steganographer.HideDataInPNG(testData)
		hideTime := time.Since(start)

		if err != nil {
			fmt.Printf("❌ 隐藏失败 (%d bytes): %v\n", size, err)
			continue
		}

		// 测试提取性能
		start = time.Now()
		extractedData, err := steganographer.ExtractDataFromPNG(pngData)
		extractTime := time.Since(start)

		if err != nil {
			fmt.Printf("❌ 提取失败 (%d bytes): %v\n", size, err)
			continue
		}

		// 验证数据
		if len(extractedData) != len(testData) {
			fmt.Printf("❌ 数据长度不匹配 (%d bytes)\n", size)
			continue
		}

		compressionRatio := float64(len(pngData)) / float64(size)

		fmt.Printf("%-10s %-15v %-15v %-15d %.2fx\n", 
			formatSize(size), hideTime, extractTime, len(pngData), compressionRatio)
	}

	fmt.Println("\n📊 性能测试完成")
}

func demoSystemInfo() {
	fmt.Println("\n💻 系统信息")
	fmt.Println(strings.Repeat("-", 40))

	fmt.Println("🎯 MixFile系统组件:")
	fmt.Println("  ✅ 隐写术模块 (steganography)")
	fmt.Println("  ✅ 索引管理模块 (index)")
	fmt.Println("  ✅ 分享码模块 (sharecode)")
	fmt.Println("  ✅ 下载器模块 (downloader)")
	fmt.Println("  ✅ Range请求模块 (range_request)")

	fmt.Println("\n🔧 支持的功能:")
	fmt.Println("  • 端到端加密文件分享")
	fmt.Println("  • 图片隐写术数据隐藏")
	fmt.Println("  • 分片并发上传下载")
	fmt.Println("  • 完整性验证保证")
	fmt.Println("  • 在线视频播放支持")
	fmt.Println("  • 实时进度监控")
	fmt.Println("  • 错误恢复机制")

	fmt.Println("\n📈 性能特性:")
	fmt.Println("  • 并发分片下载 (最大3个)")
	fmt.Println("  • 分片缓存机制")
	fmt.Println("  • 断点续传支持")
	fmt.Println("  • Range请求支持")
	fmt.Println("  • 数据压缩优化")

	fmt.Println("\n🔐 安全特性:")
	fmt.Println("  • AES-GCM-256加密")
	fmt.Println("  • SHA256完整性验证")
	fmt.Println("  • 隐写术数据隐藏")
	fmt.Println("  • 索引文件加密")
	fmt.Println("  • 分享码验证")

	fmt.Println("\n🌐 协议支持:")
	fmt.Println("  • MixFile协议 (mf://)")
	fmt.Println("  • HTTP Range请求")
	fmt.Println("  • RESTful API")
	fmt.Println("  • WebSocket通知")

	// 默认配置信息
	config := mixfile.DefaultDownloaderConfig()
	fmt.Println("\n⚙️ 默认配置:")
	fmt.Printf("  • 最大并发下载: %d\n", config.MaxConcurrentDownloads)
	fmt.Printf("  • 分片超时: %v\n", config.ChunkTimeout)
	fmt.Printf("  • 重试次数: %d\n", config.RetryAttempts)
	fmt.Printf("  • 重试延迟: %v\n", config.RetryDelay)
	fmt.Printf("  • Range请求: %t\n", config.EnableRangeRequests)
	fmt.Printf("  • 临时目录: %s\n", config.TempDir)
}

func formatSize(size int) string {
	if size < 1024 {
		return fmt.Sprintf("%dB", size)
	} else if size < 1024*1024 {
		return fmt.Sprintf("%.1fKB", float64(size)/1024)
	} else {
		return fmt.Sprintf("%.1fMB", float64(size)/1024/1024)
	}
}