package main

import (
	"fmt"
	"net/url"
	"os"

	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/mixfile"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run cmd/test_sharecode.go <分享码>")
		return
	}

	// 初始化日志
	logger.Init("info", "text")

	shareCode := os.Args[1]
	fmt.Printf("🔍 测试分享码: %s\n", shareCode)
	fmt.Printf("📏 分享码长度: %d 字符\n", len(shareCode))

	// URL解码测试
	if decoded, err := url.QueryUnescape(shareCode); err == nil && decoded != shareCode {
		fmt.Printf("🔄 URL解码后: %s\n", decoded)
		shareCode = decoded
	}

	// 创建分享码处理器
	processor := mixfile.NewShareCodeProcessor(true)

	// 验证分享码格式
	fmt.Println("\n📋 验证分享码格式...")
	if err := processor.ValidateShareCodeFormat(shareCode); err != nil {
		fmt.Printf("❌ 格式验证失败: %v\n", err)
		return
	}
	fmt.Println("✅ 分享码格式正确")

	// 尝试解析分享码
	fmt.Println("\n🔨 解析分享码...")
	downloadInfo, err := processor.ShareCodeToDownloadInfo(shareCode)
	if err != nil {
		fmt.Printf("❌ 解析失败: %v\n", err)
		
		// 尝试获取分享码摘要信息
		fmt.Println("\n🔍 尝试获取摘要信息...")
		if summary, err := processor.GetShareCodeInfo(shareCode); err == nil {
			fmt.Printf("📊 摘要信息: %+v\n", summary)
		} else {
			fmt.Printf("❌ 摘要获取失败: %v\n", err)
		}
		return
	}

	// 显示解析结果
	fmt.Println("✅ 分享码解析成功！")
	fmt.Printf("📁 文件名: %s\n", downloadInfo.FileName)
	fmt.Printf("📦 文件大小: %d 字节\n", downloadInfo.FileSize)
	fmt.Printf("🔢 分片数量: %d\n", downloadInfo.ChunkCount)
	fmt.Printf("🔗 索引URL: %s\n", downloadInfo.IndexURL)
	fmt.Printf("🔑 加密算法: %s\n", downloadInfo.Algorithm)
	fmt.Printf("⏰ 创建时间: %v\n", downloadInfo.CreatedAt)
}
