package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/mixfile"
)

func main() {
	// 初始化日志
	logger.Init("info", "text")

	// 设置路由
	http.HandleFunc("/", streamHomeHandler)
	http.HandleFunc("/play", streamPlayHandler)
	http.HandleFunc("/api/stream", streamAPIHandler)
	http.HandleFunc("/health", streamHealthHandler)

	fmt.Println("🚀 MixFile流媒体服务器启动成功！")
	fmt.Println("📱 访问地址: http://localhost:8083")
	fmt.Println("🎬 播放页面: http://localhost:8083/play")
	fmt.Println("⚡ 支持: 边下载边播放、Range请求、即时播放")
	fmt.Println(strings.Repeat("=", 60))

	// 启动服务器
	log.Fatal(http.ListenAndServe(":8083", nil))
}

// 首页处理器
func streamHomeHandler(w http.ResponseWriter, r *http.Request) {
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MixFile流媒体播放器</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 30px; border-radius: 10px; }
        .title { color: #333; text-align: center; margin-bottom: 30px; }
        .input-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-top: 20px; }
        .feature { background: #d4edda; padding: 15px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎬 MixFile流媒体播放器</h1>
        
        <div class="input-group">
            <label for="shareCode">分享码:</label>
            <input type="text" id="shareCode" placeholder="输入MixFile分享码 (mf://...)" />
        </div>
        
        <button onclick="playVideo()">▶️ 即时播放</button>
        
        <div class="feature">
            <h3>⚡ 新特性:</h3>
            <ul>
                <li><strong>即时播放</strong> - 无需等待全部下载</li>
                <li><strong>边下载边播放</strong> - 智能缓冲管理</li>
                <li><strong>Range请求支持</strong> - 支持快进快退</li>
                <li><strong>自适应加载</strong> - 根据播放位置动态下载</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>📋 使用说明:</h3>
            <ul>
                <li>输入MixFile分享码 (以 mf:// 开头)</li>
                <li>点击即时播放，几秒钟后开始播放</li>
                <li>支持拖拽进度条快进快退</li>
                <li>播放过程中会智能下载后续内容</li>
            </ul>
        </div>
    </div>

    <script>
        function playVideo() {
            const shareCode = document.getElementById('shareCode').value.trim();
            if (!shareCode) {
                alert('请输入分享码');
                return;
            }
            
            if (!shareCode.startsWith('mf://')) {
                alert('分享码格式错误，应以 mf:// 开头');
                return;
            }
            
            // 跳转到播放页面
            window.location.href = '/play?code=' + encodeURIComponent(shareCode);
        }
        
        // 回车键播放
        document.getElementById('shareCode').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                playVideo();
            }
        });
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// 播放页面处理器
func streamPlayHandler(w http.ResponseWriter, r *http.Request) {
	shareCode := r.URL.Query().Get("code")
	if shareCode == "" {
		http.Redirect(w, r, "/", http.StatusFound)
		return
	}

	html := fmt.Sprintf(`
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MixFile流媒体播放器 - 播放中</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #000; }
        .container { background: #111; padding: 20px; border-radius: 10px; }
        .title { color: #fff; text-align: center; margin-bottom: 20px; }
        .video-container { text-align: center; margin-bottom: 20px; }
        video { width: 100%%; max-width: 800px; height: auto; }
        .info { background: #222; color: #fff; padding: 15px; border-radius: 5px; margin-top: 20px; }
        .share-code { background: #333; color: #0f0; padding: 10px; border-radius: 5px; font-family: monospace; word-break: break-all; }
        .back-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .status { background: #444; color: #fff; padding: 10px; border-radius: 5px; margin-top: 10px; }
        .loading { color: #ffa500; }
        .ready { color: #00ff00; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎬 MixFile流媒体播放器</h1>
        
        <div class="video-container">
            <video controls preload="none" id="videoPlayer">
                <source src="/api/stream?code=%s" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
        </div>
        
        <div class="info">
            <h3>📋 播放信息:</h3>
            <p><strong>分享码:</strong></p>
            <div class="share-code">%s</div>
            <div class="status" id="status">
                <span class="loading">⏳ 正在初始化流媒体...</span>
            </div>
        </div>
        
        <a href="/" class="back-btn">← 返回首页</a>
    </div>

    <script>
        const video = document.getElementById('videoPlayer');
        const status = document.getElementById('status');
        
        let loadStartTime = Date.now();
        
        video.addEventListener('loadstart', function() {
            console.log('开始加载视频流...');
            status.innerHTML = '<span class="loading">⚡ 正在下载前几个分片...</span>';
        });
        
        video.addEventListener('canplay', function() {
            const loadTime = ((Date.now() - loadStartTime) / 1000).toFixed(1);
            console.log('视频可以播放，加载时间:', loadTime + 's');
            status.innerHTML = '<span class="ready">✅ 流媒体就绪，加载时间: ' + loadTime + 's</span>';
        });
        
        video.addEventListener('waiting', function() {
            status.innerHTML = '<span class="loading">⏳ 正在缓冲更多内容...</span>';
        });
        
        video.addEventListener('playing', function() {
            status.innerHTML = '<span class="ready">▶️ 正在播放 (边下载边播放)</span>';
        });
        
        video.addEventListener('seeking', function() {
            status.innerHTML = '<span class="loading">🔍 正在跳转，下载目标分片...</span>';
        });
        
        video.addEventListener('error', function(e) {
            console.error('视频播放错误:', e);
            status.innerHTML = '<span style="color: #ff0000;">❌ 播放失败，请检查分享码</span>';
        });
        
        // 自动播放（某些浏览器可能阻止）
        video.addEventListener('canplay', function() {
            video.play().catch(e => {
                console.log('自动播放被阻止，需要用户手动点击播放');
            });
        }, { once: true });
    </script>
</body>
</html>`, url.QueryEscape(shareCode), shareCode)

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// 流媒体API处理器 - 实现真实的流式下载和播放
func streamAPIHandler(w http.ResponseWriter, r *http.Request) {
	shareCode := r.URL.Query().Get("code")
	if shareCode == "" {
		http.Error(w, "Missing share code", http.StatusBadRequest)
		return
	}

	log.Printf("🎬 开始处理流媒体请求: %s", shareCode)

	// 解析分享码
	processor := mixfile.NewShareCodeProcessor(true)
	downloadInfo, err := processor.ShareCodeToDownloadInfo(shareCode)
	if err != nil {
		log.Printf("❌ 分享码解析失败: %v", err)
		http.Error(w, fmt.Sprintf("Invalid share code: %v", err), http.StatusBadRequest)
		return
	}

	log.Printf("✅ 分享码解析成功: %s (%.2f MB, %d chunks)",
		downloadInfo.FileName,
		float64(downloadInfo.FileSize)/1024/1024,
		downloadInfo.ChunkCount)

	// 创建下载器配置
	config := &mixfile.DownloaderConfig{
		MaxConcurrentDownloads: 5,
		TempDir:                "/tmp/mixfile_stream",
		EnableRangeRequests:    true,
		KeepTempFiles:          false,
	}
	downloader := mixfile.NewMixFileDownloader(config)

	// 设置流媒体响应头
	w.Header().Set("Content-Type", "video/mp4")
	w.Header().Set("Accept-Ranges", "bytes")
	w.Header().Set("Content-Length", strconv.FormatInt(downloadInfo.FileSize, 10))

	// 解析Range请求
	rangeHeader := r.Header.Get("Range")
	if rangeHeader != "" {
		log.Printf("📱 Range请求: %s", rangeHeader)
		err = handleRangeRequest(w, r, downloader, shareCode, rangeHeader)
	} else {
		log.Printf("📺 完整流媒体请求")
		err = handleFullStreamRequest(w, downloader, shareCode)
	}

	if err != nil {
		log.Printf("❌ 流媒体处理失败: %v", err)
		http.Error(w, fmt.Sprintf("Streaming error: %v", err), http.StatusInternalServerError)
		return
	}

	log.Printf("✅ 流媒体请求处理完成: %s", downloadInfo.FileName)
}

// handleFullStreamRequest 处理完整流媒体请求
func handleFullStreamRequest(w http.ResponseWriter, downloader mixfile.MixFileDownloader, shareCode string) error {
	log.Printf("🚀 开始完整流媒体传输")

	// 使用Range请求处理器来实现流式传输
	rangeHandler := mixfile.NewMixFileRangeHandler(downloader)

	// 创建一个假的请求来模拟完整文件请求
	fakeRequest := &http.Request{
		Method: "GET",
		Header: make(http.Header),
	}

	// 处理完整文件请求
	err := rangeHandler.HandleRangeRequest(w, fakeRequest, shareCode)
	if err != nil {
		return fmt.Errorf("stream download failed: %w", err)
	}

	return nil
}

// handleRangeRequest 处理Range请求
func handleRangeRequest(w http.ResponseWriter, r *http.Request, downloader mixfile.MixFileDownloader, shareCode string, rangeHeader string) error {
	log.Printf("📱 Range请求: %s", rangeHeader)

	// 使用Range请求处理器
	rangeHandler := mixfile.NewMixFileRangeHandler(downloader)

	// 直接使用现有的Range请求处理器
	err := rangeHandler.HandleRangeRequest(w, r, shareCode)
	if err != nil {
		return fmt.Errorf("range stream failed: %w", err)
	}

	return nil
}

// parseRange 解析Range头
func parseRange(rangeHeader string, totalSize int64) (start, end int64, err error) {
	if !strings.HasPrefix(rangeHeader, "bytes=") {
		return 0, 0, fmt.Errorf("invalid range header format")
	}

	rangeSpec := strings.TrimPrefix(rangeHeader, "bytes=")
	parts := strings.Split(rangeSpec, "-")

	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("invalid range specification")
	}

	if parts[0] != "" {
		start, err = strconv.ParseInt(parts[0], 10, 64)
		if err != nil {
			return 0, 0, fmt.Errorf("invalid start range: %w", err)
		}
	}

	if parts[1] != "" {
		end, err = strconv.ParseInt(parts[1], 10, 64)
		if err != nil {
			return 0, 0, fmt.Errorf("invalid end range: %w", err)
		}
	} else {
		end = totalSize - 1
	}

	if start > end || start >= totalSize {
		return 0, 0, fmt.Errorf("invalid range bounds")
	}

	if end >= totalSize {
		end = totalSize - 1
	}

	return start, end, nil
}

// 健康检查处理器
func streamHealthHandler(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"status":  "ok",
		"service": "MixFile Streaming Player",
		"version": "2.0.0",
		"features": []string{
			"streaming_playback",
			"range_requests",
			"instant_play",
			"adaptive_loading",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
