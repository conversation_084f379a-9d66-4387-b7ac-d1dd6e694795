package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/logger"
)

func main() {
	fmt.Println("🎬 本地视频文件处理工具")
	fmt.Println(strings.Repeat("=", 60))

	// 目标视频文件
	videoFile := "/www/wwwroot/JAVAPI.COM/downloads/20 最新流出，推特大奶网黄女神kitty付费作品，夏天要来了玩水水，泳池坏坏跟湿湿的淫水混合/20 最新流出，推特大奶网黄女神kitty付费作品，夏天要来了玩水水，泳池坏坏跟湿湿的淫水混合.mp4"

	fmt.Printf("🔍 检查文件: %s\n", videoFile)

	// 检查文件是否存在
	if _, err := os.Stat(videoFile); os.IsNotExist(err) {
		fmt.Printf("❌ 视频文件不存在: %s\n", videoFile)
		fmt.Printf("❌ 错误详情: %v\n", err)
		return
	}

	fmt.Println("✅ 文件存在，继续处理...")

	// 获取文件信息
	fileInfo, err := os.Stat(videoFile)
	if err != nil {
		fmt.Printf("❌ 获取文件信息失败: %v\n", err)
		return
	}

	fmt.Printf("📁 目标文件: %s\n", filepath.Base(videoFile))
	fmt.Printf("📏 文件大小: %.2f MB\n", float64(fileInfo.Size())/1024/1024)

	// 初始化日志
	logger.Init("info", "text")

	// 步骤1：文件处理（分片和加密）
	fmt.Println("\n🔪 步骤1: 文件分片和加密...")
	workDir := fmt.Sprintf("/tmp/video_upload_%d", time.Now().Unix())

	config := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       1,
		EncryptionEnabled: true,
		KeepOriginal:      true,
		WorkDir:           workDir,
	}

	fmt.Printf("📁 工作目录将保存在: %s\n", workDir)

	processor := fileprocessor.NewProcessor(config)

	result, err := processor.ProcessFile(videoFile, func(stage string, progress float64, message string) {
		fmt.Printf("  📊 %s: %.1f%% - %s\n", stage, progress, message)
	})

	if err != nil {
		fmt.Printf("❌ 文件处理失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 文件处理完成: %d个分片\n", result.ChunkCount)
	fmt.Printf("📁 工作目录: %s\n", result.WorkDir)
	fmt.Printf("🔐 加密密钥: %s\n", result.EncryptionKey)
	fmt.Printf("📋 原文件哈希: %s\n", result.OriginalFileHash)

	// 输出分片文件路径
	fmt.Println("\n📦 分片文件列表:")
	encryptedDir := filepath.Join(result.WorkDir, "encrypted")
	entries, err := os.ReadDir(encryptedDir)
	if err != nil {
		fmt.Printf("❌ 读取加密目录失败: %v\n", err)
		return
	}

	var chunkFiles []string
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".enc") {
			chunkPath := filepath.Join(encryptedDir, entry.Name())
			chunkFiles = append(chunkFiles, chunkPath)
			fmt.Printf("  📄 %s\n", chunkPath)
		}
	}

	fmt.Printf("\n🎉 处理完成！\n")
	fmt.Printf("📊 统计信息:\n")
	fmt.Printf("  📁 原文件: %s (%.2f MB)\n", filepath.Base(videoFile), float64(fileInfo.Size())/1024/1024)
	fmt.Printf("  📦 分片数量: %d\n", result.ChunkCount)
	fmt.Printf("  📁 工作目录: %s\n", result.WorkDir)
	fmt.Printf("  🔐 加密密钥: %s\n", result.EncryptionKey)

	fmt.Printf("\n💡 下一步操作:\n")
	fmt.Printf("  1. 使用智能上传工具上传分片:\n")
	fmt.Printf("     go run cmd/smart_upload.go %s 10\n", result.WorkDir)
	fmt.Printf("  2. 或手动上传分片文件到ImgBB\n")
	fmt.Printf("  3. 生成MixFile分享码用于播放\n")
}
