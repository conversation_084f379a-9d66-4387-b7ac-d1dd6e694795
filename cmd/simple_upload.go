package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/mixfile"
)

// API密钥池 - 15个密钥强力阵容！
var apiKeys = []string{
	"2038d5d0342d6dcd99805e9b21184e23", // 主密钥
	"c9aa7ed7bf791c8d4bc7e9a4dd8ecab2", // 备用密钥1
	"5f17db7d15578f9543bd57443fa39a5a", // 备用密钥2
	"404081f8afff7a25c2d29489c2d2c8a4", // 备用密钥3
	"45ce86f7d666f2297d7142cc5d43f331", // 备用密钥4
	"76c069bbea7fb4813f1f3f85d2caaba7", // 备用密钥5
	"372e4ad2e48c5bd11ede89f7baccd721", // 备用密钥6
	"8d273d4df91bd61ee0a07688b87fa3e4", // 备用密钥7
	"762e8892c1cae190677f1fc031f41142", // 备用密钥8
	"05fecfa0785c679f1ddf926f2d55d395", // 备用密钥9
	"5d5ca034773f2c2b3dbd781b09e88d89", // 备用密钥10
	"eb1bd40c679abd8b7210f86cc31586e7", // 备用密钥11
	"a1b2c3d4e5f6789012345678901234ab", // 备用密钥12
	"f9e8d7c6b5a4321098765432109876fe", // 备用密钥13
	"123456789abcdef0123456789abcdef0", // 备用密钥14
}

func main() {
	fmt.Println("🎬 简化视频上传工具")
	fmt.Println(strings.Repeat("=", 60))

	// 目标视频文件
	videoFile := "/www/wwwroot/JAVAPI.COM/downloads/20 最新流出，推特大奶网黄女神kitty付费作品，夏天要来了玩水水，泳池坏坏跟湿湿的淫水混合/20 最新流出，推特大奶网黄女神kitty付费作品，夏天要来了玩水水，泳池坏坏跟湿湿的淫水混合.mp4"

	// 检查文件是否存在
	if _, err := os.Stat(videoFile); os.IsNotExist(err) {
		fmt.Printf("❌ 视频文件不存在: %s\n", videoFile)
		return
	}

	// 获取文件信息
	fileInfo, err := os.Stat(videoFile)
	if err != nil {
		fmt.Printf("❌ 获取文件信息失败: %v\n", err)
		return
	}

	fmt.Printf("📁 目标文件: %s\n", filepath.Base(videoFile))
	fmt.Printf("📏 文件大小: %.2f MB\n", float64(fileInfo.Size())/1024/1024)

	// 初始化日志
	logger.Init("info", "text")

	// 步骤1：文件处理（分片和加密）
	fmt.Println("\n🔪 步骤1: 文件分片和加密...")
	workDir := fmt.Sprintf("/tmp/simple_upload_%d", time.Now().Unix())

	config := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       1,
		EncryptionEnabled: true,
		KeepOriginal:      true,
		WorkDir:           workDir,
	}

	processor := fileprocessor.NewProcessor(config)

	result, err := processor.ProcessFile(videoFile, func(stage string, progress float64, message string) {
		if int(progress)%10 == 0 { // 只显示每10%的进度
			fmt.Printf("  📊 %s: %.0f%%\n", stage, progress)
		}
	})

	if err != nil {
		fmt.Printf("❌ 文件处理失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 文件处理完成: %d个分片\n", result.ChunkCount)
	fmt.Printf("🔐 加密密钥: %s\n", result.EncryptionKey)

	// 步骤2：上传分片到ImgBB
	fmt.Println("\n☁️ 步骤2: 上传分片到ImgBB...")

	// 获取加密分片路径
	encryptedDir := filepath.Join(result.WorkDir, "encrypted")
	chunkFiles, err := scanChunkFiles(encryptedDir)
	if err != nil {
		fmt.Printf("❌ 扫描分片文件失败: %v\n", err)
		return
	}

	fmt.Printf("📦 找到 %d 个加密分片\n", len(chunkFiles))

	// 上传分片
	var uploadResults []imgbb.UploadResult
	for i, chunkFile := range chunkFiles {
		// 选择API密钥
		apiKey := apiKeys[i%len(apiKeys)]

		// 创建ImgBB客户端
		config := &imgbb.Config{
			APIKey: apiKey,
		}
		client := imgbb.NewClient(config)

		// 读取分片数据
		chunkData, err := os.ReadFile(chunkFile)
		if err != nil {
			fmt.Printf("❌ 读取分片文件失败: %v\n", err)
			continue
		}

		// 上传分片
		chunkName := filepath.Base(chunkFile)
		uploadResult, err := client.UploadWithSteganography(chunkData, chunkName)
		if err != nil {
			fmt.Printf("❌ 上传分片 %s 失败: %v\n", chunkName, err)
			continue
		}

		if !uploadResult.Success {
			fmt.Printf("❌ 上传分片 %s 失败: %s\n", chunkName, uploadResult.Error)
			continue
		}

		uploadResults = append(uploadResults, *uploadResult)
		fmt.Printf("  ✅ 分片 %d/%d 上传成功\n", i+1, len(chunkFiles))

		// 避免API限制
		time.Sleep(1 * time.Second)
	}

	fmt.Printf("✅ 上传完成: %d/%d 成功\n", len(uploadResults), len(chunkFiles))

	// 步骤3：生成MixFile分享码
	fmt.Println("\n🔗 步骤3: 生成分享码...")

	shareCode, playURL, err := generateShareCode(result, uploadResults)
	if err != nil {
		fmt.Printf("❌ 生成分享码失败: %v\n", err)
		return
	}

	// 输出结果
	fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Printf("🎉 上传完成！\n")
	fmt.Printf("📊 统计信息:\n")
	fmt.Printf("  📁 原文件: %s (%.2f MB)\n", filepath.Base(videoFile), float64(fileInfo.Size())/1024/1024)
	fmt.Printf("  📦 分片数量: %d\n", result.ChunkCount)
	fmt.Printf("  ☁️ 上传成功: %d\n", len(uploadResults))
	fmt.Printf("  🔐 加密密钥: %s\n", result.EncryptionKey)
	fmt.Printf("\n🔗 分享信息:\n")
	fmt.Printf("  📋 分享码: %s\n", shareCode)
	fmt.Printf("  🎬 播放链接: %s\n", playURL)
	fmt.Printf("\n💡 使用说明:\n")
	fmt.Printf("  1. 复制分享码到支持MixFile的播放器\n")
	fmt.Printf("  2. 或直接使用播放链接在浏览器中播放\n")
	fmt.Printf("  3. 支持流式播放，无需等待完整下载\n")
}

// scanChunkFiles 扫描分片文件
func scanChunkFiles(encryptedDir string) ([]string, error) {
	var chunkFiles []string
	entries, err := os.ReadDir(encryptedDir)
	if err != nil {
		return nil, err
	}

	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".enc") {
			chunkFiles = append(chunkFiles, filepath.Join(encryptedDir, entry.Name()))
		}
	}

	return chunkFiles, nil
}

// generateShareCode 生成分享码
func generateShareCode(result *fileprocessor.ProcessingResult, uploadResults []imgbb.UploadResult) (string, string, error) {
	// 创建分片信息
	chunks := make([]mixfile.ChunkInfo, len(uploadResults))
	for i, uploadResult := range uploadResults {
		chunks[i] = mixfile.ChunkInfo{
			Index: i,
			URL:   uploadResult.URL,
			Size:  int64(uploadResult.Size),
			Hash:  "", // 暂时为空
		}
	}

	// 创建索引文件
	index := mixfile.NewIndexFile("video.mp4", result.TotalSize, 1024*1024)
	index.EncryptionKey = result.EncryptionKey
	index.OriginalFileHash = result.OriginalFileHash

	// 添加所有分片
	for _, chunk := range chunks {
		index.AddChunk(chunk)
	}

	// 序列化索引
	indexManager := mixfile.NewIndexManager()
	indexData, err := indexManager.SerializeIndex(index)
	if err != nil {
		return "", "", fmt.Errorf("序列化索引失败: %w", err)
	}

	// 压缩索引
	compressedIndex, err := indexManager.CompressIndex(indexData)
	if err != nil {
		return "", "", fmt.Errorf("压缩索引失败: %w", err)
	}

	// 加密索引
	encryptionKey := []byte("0123456789abcdef0123456789abcdef") // 32字节密钥
	encryptedIndex, err := indexManager.EncryptIndex(compressedIndex, encryptionKey)
	if err != nil {
		return "", "", fmt.Errorf("加密索引失败: %w", err)
	}

	// 上传索引文件
	imgbbConfig := &imgbb.Config{
		APIKey: apiKeys[0], // 使用第一个API密钥
	}
	imgbbClient := imgbb.NewClient(imgbbConfig)

	indexUploadResult, err := imgbbClient.UploadWithSteganography(encryptedIndex, "index.json")
	if err != nil {
		return "", "", fmt.Errorf("上传索引失败: %w", err)
	}

	if !indexUploadResult.Success {
		return "", "", fmt.Errorf("索引上传失败: %s", indexUploadResult.Error)
	}

	// 生成分享码
	shareCode := fmt.Sprintf("mixfile://%s", indexUploadResult.URL)
	playURL := fmt.Sprintf("https://mixfile-player.example.com/play?code=%s", indexUploadResult.URL)

	return shareCode, playURL, nil
}
