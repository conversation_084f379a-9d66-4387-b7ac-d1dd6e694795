package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"magnet-downloader/pkg/aria2"
	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/streaming"
)

const (
	TARGET_MAGNET = "magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5"
	DOWNLOAD_DIR  = "/tmp/downloads"
)

func main() {
	fmt.Println("🚀 真实磁力链接下载和处理")
	fmt.Println("================================================")
	fmt.Printf("🎯 目标磁力链接: %s\n", TARGET_MAGNET)
	fmt.Printf("📁 下载目录: %s\n", DOWNLOAD_DIR)
	fmt.Println()

	// 1. 连接aria2服务
	fmt.Println("🔌 连接aria2服务...")
	client := aria2.NewClient(&aria2.ClientConfig{
		Host:   "localhost",
		Port:   6800,
		Secret: "",
	})

	// 测试连接
	version, err := client.GetVersion()
	if err != nil {
		log.Fatalf("❌ 无法连接aria2服务: %v", err)
	}
	fmt.Printf("✅ aria2连接成功，版本: %s\n", version.Version)

	// 2. 开始下载
	fmt.Println("\n📥 开始下载磁力链接...")
	options := &aria2.AddUriOptions{
		Dir: DOWNLOAD_DIR,
	}
	gid, err := client.AddMagnet(TARGET_MAGNET, options)
	if err != nil {
		log.Fatalf("❌ 添加下载任务失败: %v", err)
	}
	fmt.Printf("✅ 下载任务已添加，GID: %s\n", gid)

	// 3. 监控下载进度
	fmt.Println("\n📊 监控下载进度...")
	downloadedFiles := monitorDownload(client, gid)

	if len(downloadedFiles) == 0 {
		log.Fatal("❌ 没有下载到任何文件")
	}

	fmt.Printf("✅ 下载完成！共下载 %d 个文件\n", len(downloadedFiles))
	for i, file := range downloadedFiles {
		fmt.Printf("  %d. %s (%.2f MB)\n", i+1, filepath.Base(file), getFileSizeMB(file))
	}

	// 4. 处理每个下载的文件
	fmt.Println("\n🔄 开始文件处理...")
	for _, file := range downloadedFiles {
		fmt.Printf("\n📦 处理文件: %s\n", filepath.Base(file))
		processDownloadedFile(file)
	}

	fmt.Println("\n🎉 所有文件处理完成！")
}

// monitorDownload 监控下载进度
func monitorDownload(client *aria2.Client, gid string) []string {
	downloadedFiles := []string{}

	for {
		status, err := client.TellStatus(gid)
		if err != nil {
			fmt.Printf("⚠️ 获取状态失败: %v\n", err)
			time.Sleep(5 * time.Second)
			continue
		}

		switch status.Status {
		case "active":
			completed, _ := strconv.ParseFloat(status.CompletedLength, 64)
			total, _ := strconv.ParseFloat(status.TotalLength, 64)
			downloadSpeed, _ := strconv.ParseFloat(status.DownloadSpeed, 64)
			connections, _ := strconv.Atoi(status.Connections)

			progress := completed / total * 100
			speed := downloadSpeed / 1024 / 1024 // MB/s
			fmt.Printf("  📈 下载进度: %.1f%% | 速度: %.2f MB/s | 连接数: %d\n",
				progress, speed, connections)

		case "complete":
			fmt.Println("  🎉 下载完成！")

			// 获取下载的文件列表
			for _, file := range status.Files {
				if file.Selected == "true" {
					downloadedFiles = append(downloadedFiles, file.Path)
				}
			}
			return downloadedFiles

		case "error":
			fmt.Printf("  ❌ 下载失败: %s\n", status.ErrorMessage)
			return downloadedFiles

		case "removed":
			fmt.Println("  ⚠️ 下载任务被移除")
			return downloadedFiles

		case "paused":
			fmt.Println("  ⏸️ 下载已暂停")

		case "waiting":
			fmt.Println("  ⏳ 等待开始下载...")
		}

		time.Sleep(3 * time.Second)
	}
}

// processDownloadedFile 处理下载的文件
func processDownloadedFile(filePath string) {
	// 1. 文件分片和加密
	fmt.Println("  🔪 开始文件分片和加密...")

	workDir := fmt.Sprintf("/tmp/processing_%d", time.Now().Unix())
	config := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       1,
		EncryptionEnabled: true,
		KeepOriginal:      true,
		WorkDir:           workDir,
	}

	processor := fileprocessor.NewProcessor(config)
	result, err := processor.ProcessFile(filePath, func(stage string, progress float64, message string) {
		fmt.Printf("    📊 %s: %.1f%% - %s\n", stage, progress, message)
	})

	if err != nil {
		fmt.Printf("  ❌ 文件处理失败: %v\n", err)
		return
	}

	fmt.Printf("  ✅ 文件处理完成，共 %d 个加密分片\n", result.ChunkCount)

	// 2. 上传到图床（模拟）
	fmt.Println("  ☁️ 模拟上传到图床...")
	uploadResults := []imgbb.UploadResult{}

	for i, chunk := range result.Chunks {
		chunkPath := processor.GetChunkPath(workDir, chunk)

		// 模拟上传
		time.Sleep(200 * time.Millisecond)

		uploadResult := imgbb.UploadResult{
			Success: true,
			URL: fmt.Sprintf("https://i.ibb.co/real/%s_chunk_%03d.enc",
				filepath.Base(filePath), i),
			Size: int(getFileSizeMB(chunkPath) * 1024 * 1024),
		}
		uploadResults = append(uploadResults, uploadResult)
		fmt.Printf("    ⬆️ 分片 %d/%d 上传成功\n", i+1, len(result.Chunks))
	}

	// 3. 生成播放列表
	fmt.Println("  📋 生成播放列表...")
	playlistPath := generatePlaylist(filePath, uploadResults)
	fmt.Printf("  ✅ 播放列表生成完成: %s\n", playlistPath)

	// 4. 显示结果
	displayResults(filePath, uploadResults, playlistPath)

	// 5. 清理工作目录（可选）
	// os.RemoveAll(workDir)
	fmt.Printf("  📁 工作目录保留: %s\n", workDir)
}

// generatePlaylist 生成播放列表
func generatePlaylist(originalFile string, uploadResults []imgbb.UploadResult) string {
	config := &streaming.PlaylistConfig{
		Version:        3,
		TargetDuration: 10,
		MediaSequence:  0,
		AllowCache:     true,
		PlaylistType:   "VOD",
	}

	generator := streaming.NewPlaylistGenerator(config)

	// 提取URL
	urls := []string{}
	for _, result := range uploadResults {
		urls = append(urls, result.URL)
	}

	// 生成播放列表
	baseName := filepath.Base(originalFile)
	playlist, err := generator.GenerateFromChunks(urls, 10.0, baseName)
	if err != nil {
		log.Printf("播放列表生成失败: %v", err)
		return ""
	}

	// 保存播放列表
	playlistPath := filepath.Join(DOWNLOAD_DIR, baseName+".m3u8")
	err = os.WriteFile(playlistPath, []byte(playlist), 0644)
	if err != nil {
		log.Printf("播放列表保存失败: %v", err)
		return ""
	}

	return playlistPath
}

// displayResults 显示处理结果
func displayResults(originalFile string, uploadResults []imgbb.UploadResult, playlistPath string) {
	fmt.Println("  ================================================")
	fmt.Printf("  📊 文件: %s\n", filepath.Base(originalFile))
	fmt.Printf("  📦 分片数: %d\n", len(uploadResults))
	fmt.Printf("  📏 分片大小: 1MB\n")
	fmt.Printf("  🔐 加密: AES-GCM 256-bit\n")
	fmt.Printf("  📋 播放列表: %s\n", playlistPath)

	fmt.Println("  🔗 分片URL:")
	for i, result := range uploadResults {
		fmt.Printf("    %d. %s\n", i+1, result.URL)
	}
}

// getFileSizeMB 获取文件大小（MB）
func getFileSizeMB(filePath string) float64 {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0
	}
	return float64(info.Size()) / (1024 * 1024)
}
