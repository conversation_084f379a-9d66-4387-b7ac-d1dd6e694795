package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strings"

	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/mixfile"
)

// 简单的MixFile播放服务器
func main() {
	// 初始化日志
	logger.Init("info", "text")

	// 设置路由
	http.HandleFunc("/", homeHandler)
	http.HandleFunc("/play", playHandler)
	http.HandleFunc("/api/play", apiPlayHandler)
	http.HandleFunc("/health", healthHandler)

	fmt.Println("🚀 MixFile播放服务器启动成功！")
	fmt.Println("📱 访问地址: http://localhost:8081")
	fmt.Println("🎬 播放页面: http://localhost:8081/play")
	fmt.Println("💡 使用方法: 在播放页面输入分享码或直接访问播放链接")
	fmt.Println(strings.Repeat("=", 60))

	// 启动服务器
	log.Fatal(http.ListenAndServe(":8081", nil))
}

// 首页处理器
func homeHandler(w http.ResponseWriter, r *http.Request) {
	html := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MixFile播放器</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 30px; border-radius: 10px; }
        .title { color: #333; text-align: center; margin-bottom: 30px; }
        .input-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎬 MixFile播放器</h1>
        
        <div class="input-group">
            <label for="shareCode">分享码:</label>
            <input type="text" id="shareCode" placeholder="输入MixFile分享码 (mf://...)" />
        </div>
        
        <button onclick="playVideo()">▶️ 播放视频</button>
        
        <div class="info">
            <h3>📋 使用说明:</h3>
            <ul>
                <li>输入MixFile分享码 (以 mf:// 开头)</li>
                <li>点击播放按钮开始观看</li>
                <li>支持在线流媒体播放</li>
                <li>支持Range请求，可快进快退</li>
            </ul>
        </div>
    </div>

    <script>
        function playVideo() {
            const shareCode = document.getElementById('shareCode').value.trim();
            if (!shareCode) {
                alert('请输入分享码');
                return;
            }
            
            if (!shareCode.startsWith('mf://')) {
                alert('分享码格式错误，应以 mf:// 开头');
                return;
            }
            
            // 跳转到播放页面
            window.location.href = '/play?code=' + encodeURIComponent(shareCode);
        }
        
        // 回车键播放
        document.getElementById('shareCode').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                playVideo();
            }
        });
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// 播放页面处理器
func playHandler(w http.ResponseWriter, r *http.Request) {
	shareCode := r.URL.Query().Get("code")
	if shareCode == "" {
		http.Redirect(w, r, "/", http.StatusFound)
		return
	}

	html := fmt.Sprintf(`
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MixFile播放器 - 播放中</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; background: #000; }
        .container { background: #111; padding: 20px; border-radius: 10px; }
        .title { color: #fff; text-align: center; margin-bottom: 20px; }
        .video-container { text-align: center; margin-bottom: 20px; }
        video { width: 100%%; max-width: 800px; height: auto; }
        .info { background: #222; color: #fff; padding: 15px; border-radius: 5px; margin-top: 20px; }
        .share-code { background: #333; color: #0f0; padding: 10px; border-radius: 5px; font-family: monospace; word-break: break-all; }
        .back-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎬 MixFile播放器</h1>
        
        <div class="video-container">
            <video controls preload="metadata">
                <source src="/api/play?code=%s" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
        </div>
        
        <div class="info">
            <h3>📋 播放信息:</h3>
            <p><strong>分享码:</strong></p>
            <div class="share-code">%s</div>
            <p><strong>状态:</strong> 正在解析MixFile...</p>
        </div>
        
        <a href="/" class="back-btn">← 返回首页</a>
    </div>

    <script>
        const video = document.querySelector('video');
        
        video.addEventListener('loadstart', function() {
            console.log('开始加载视频...');
        });
        
        video.addEventListener('canplay', function() {
            console.log('视频可以播放');
        });
        
        video.addEventListener('error', function(e) {
            console.error('视频播放错误:', e);
            alert('视频播放失败，请检查分享码是否正确');
        });
    </script>
</body>
</html>`, url.QueryEscape(shareCode), shareCode)

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// API播放处理器
func apiPlayHandler(w http.ResponseWriter, r *http.Request) {
	shareCode := r.URL.Query().Get("code")
	if shareCode == "" {
		http.Error(w, "Missing share code", http.StatusBadRequest)
		return
	}

	// 解析分享码
	processor := mixfile.NewShareCodeProcessor(true)
	downloadInfo, err := processor.ShareCodeToDownloadInfo(shareCode)
	if err != nil {
		log.Printf("Failed to parse share code: %v", err)
		http.Error(w, fmt.Sprintf("Invalid share code: %v", err), http.StatusBadRequest)
		return
	}

	// 创建MixFile下载器配置
	config := &mixfile.DownloaderConfig{
		MaxConcurrentDownloads: 5,
		TempDir:                "/tmp/mixfile_cache",
		EnableRangeRequests:    true,
		KeepTempFiles:          false,
	}
	downloader := mixfile.NewMixFileDownloader(config)

	// 创建Range请求处理器
	rangeHandler := mixfile.NewMixFileRangeHandler(downloader)

	// 处理Range请求
	err = rangeHandler.HandleRangeRequest(w, r, shareCode)
	if err != nil {
		log.Printf("Failed to handle range request: %v", err)
		http.Error(w, fmt.Sprintf("Playback error: %v", err), http.StatusInternalServerError)
		return
	}

	log.Printf("Successfully handled playback request for: %s", downloadInfo.FileName)
}

// 健康检查处理器
func healthHandler(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"status":  "ok",
		"service": "MixFile Player",
		"version": "1.0.0",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
