package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/fileprocessor"
)

func main() {
	fmt.Println("🧪 自动上传功能测试")
	fmt.Println("==================")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	// 创建视频选择器
	videoSelector := fileprocessor.NewVideoSelector()

	// 测试下载目录
	downloadDir := "/downloads" // 默认下载目录
	
	// 如果有环境变量设置，使用环境变量
	if envDir := os.Getenv("DOWNLOAD_DIR"); envDir != "" {
		downloadDir = envDir
	}

	fmt.Printf("📁 扫描下载目录: %s\n", downloadDir)

	// 检查目录是否存在
	if _, err := os.Stat(downloadDir); os.IsNotExist(err) {
		fmt.Printf("⚠️  下载目录不存在，创建测试目录: %s\n", downloadDir)
		if err := createTestDirectory(downloadDir); err != nil {
			fmt.Printf("❌ 创建测试目录失败: %v\n", err)
			return
		}
	}

	// 扫描视频文件
	videos, err := videoSelector.SelectVideosFromDirectory(downloadDir)
	if err != nil {
		fmt.Printf("❌ 扫描视频文件失败: %v\n", err)
		return
	}

	if len(videos) == 0 {
		fmt.Println("📂 没有找到视频文件")
		fmt.Println("💡 提示：请将视频文件放入下载目录的子文件夹中")
		fmt.Printf("   例如：%s/movie1/video.mp4\n", downloadDir)
		fmt.Printf("   例如：%s/series1/episode.part1.mp4\n", downloadDir)
		return
	}

	// 显示扫描结果
	fmt.Printf("\n🎬 找到 %d 个视频文件:\n", len(videos))
	fmt.Println(strings.Repeat("=", 60))

	var totalSize int64
	for i, video := range videos {
		fmt.Printf("%d. %s\n", i+1, video.Name)
		fmt.Printf("   路径: %s\n", video.Path)
		fmt.Printf("   大小: %.2f MB\n", float64(video.Size)/(1024*1024))
		if video.IsSegment {
			fmt.Printf("   类型: 分段文件 (索引: %d)\n", video.SegmentIndex)
		} else {
			fmt.Printf("   类型: 单个文件\n")
		}
		fmt.Println()
		totalSize += video.Size
	}

	// 显示选择摘要
	fmt.Println("📊 选择摘要:")
	summary := videoSelector.GetVideoSelectionSummary(videos)
	fmt.Println(summary)

	// 验证文件
	fmt.Println("🔍 验证视频文件:")
	validCount := 0
	for _, video := range videos {
		if err := videoSelector.ValidateVideoFile(video.Path); err != nil {
			fmt.Printf("   ❌ %s: %v\n", video.Name, err)
		} else {
			fmt.Printf("   ✅ %s: 验证通过\n", video.Name)
			validCount++
		}
	}

	fmt.Printf("\n📈 验证结果: %d/%d 文件通过验证\n", validCount, len(videos))

	// 显示配置信息
	fmt.Println("\n⚙️ 自动上传配置:")
	fmt.Printf("   启用状态: %t\n", cfg.FileProcessing.AutoUpload.Enabled)
	fmt.Printf("   扫描间隔: %d 分钟\n", cfg.FileProcessing.AutoUpload.ScanInterval)
	fmt.Printf("   最大并发: %d\n", cfg.FileProcessing.AutoUpload.MaxConcurrentUploads)
	fmt.Printf("   最小文件大小: %.2f MB\n", float64(cfg.FileProcessing.AutoUpload.MinFileSize)/(1024*1024))
	fmt.Printf("   上传提供商: %s\n", cfg.FileProcessing.UploadProvider)

	if cfg.FileProcessing.UploadProvider == "doodstream" {
		fmt.Println("\n🎬 DoodStream配置:")
		fmt.Printf("   API地址: %s\n", cfg.FileProcessing.DoodStream.BaseURL)
		fmt.Printf("   超时时间: %d 秒\n", cfg.FileProcessing.DoodStream.Timeout)
		fmt.Printf("   最大重试: %d 次\n", cfg.FileProcessing.DoodStream.MaxRetries)
	}

	fmt.Println("\n✅ 自动上传功能测试完成！")
	fmt.Println("💡 要启动自动上传服务，请运行主程序并启用自动上传功能")
}

// createTestDirectory 创建测试目录结构
func createTestDirectory(downloadDir string) error {
	// 创建主目录
	if err := os.MkdirAll(downloadDir, 0755); err != nil {
		return err
	}

	// 创建示例子目录
	testDirs := []string{
		"movie1",
		"movie2", 
		"series1",
	}

	for _, dir := range testDirs {
		dirPath := filepath.Join(downloadDir, dir)
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			return err
		}
	}

	// 创建示例说明文件
	readmePath := filepath.Join(downloadDir, "README.txt")
	readmeContent := `自动上传测试目录
==================

请将下载的视频文件放入子目录中，例如：

movie1/
  ├── video.mp4          # 单个视频文件
  └── subtitle.srt       # 字幕文件（会被忽略）

movie2/
  ├── movie.part1.mp4    # 分段视频文件
  ├── movie.part2.mp4    # 分段视频文件
  └── movie.part3.mp4    # 分段视频文件

series1/
  ├── episode01.mp4      # 系列视频文件
  ├── episode02.mp4      # 系列视频文件
  └── episode03.mp4      # 系列视频文件

自动上传功能会：
1. 扫描所有子目录
2. 识别视频文件
3. 选择最大的单个文件或所有分段文件
4. 自动上传到DoodStream
5. 保存播放链接到数据库

支持的视频格式：
.mp4, .avi, .mkv, .mov, .wmv, .flv, .webm, .m4v, .3gp, .ts, .m2ts, .vob
`

	return os.WriteFile(readmePath, []byte(readmeContent), 0644)
}