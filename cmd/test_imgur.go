package main

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"time"
)

func main() {
	fmt.Println("🧪 测试Imgur API")
	
	// 创建一个小的测试图片（1x1像素的PNG）
	testPNG := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
		0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
		0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
		0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
		0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x49,
		0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82,
	}
	
	// 编码为base64
	base64Data := base64.StdEncoding.EncodeToString(testPNG)
	
	// 创建表单数据
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)
	
	writer.WriteField("image", base64Data)
	writer.WriteField("type", "base64")
	writer.WriteField("title", "test")
	writer.Close()
	
	// 创建请求
	req, err := http.NewRequest("POST", "https://api.imgur.com/3/image", &buf)
	if err != nil {
		fmt.Printf("❌ 创建请求失败: %v\n", err)
		return
	}
	
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Client-ID 200c0920d14d97a")
	req.Header.Set("User-Agent", "magnet-downloader/1.0")
	
	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ 请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return
	}
	
	fmt.Printf("📊 响应状态: %d\n", resp.StatusCode)
	fmt.Printf("📄 响应内容: %s\n", string(body))
	
	// 尝试解析JSON
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("❌ JSON解析失败: %v\n", err)
		fmt.Printf("📄 原始响应: %s\n", string(body))
	} else {
		fmt.Printf("✅ JSON解析成功\n")
		if data, ok := result["data"].(map[string]interface{}); ok {
			if link, ok := data["link"].(string); ok {
				fmt.Printf("🔗 上传成功: %s\n", link)
			}
		}
	}
}