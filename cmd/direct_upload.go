package main

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
)

// VideoUploadResult 视频上传结果
type VideoUploadResult struct {
	VideoPath  string    `json:"video_path"`
	VideoName  string    `json:"video_name"`
	VideoURL   string    `json:"video_url"`
	PlayURL    string    `json:"play_url"`
	UploadTime time.Time `json:"upload_time"`
	FileSize   int64     `json:"file_size"`
	Status     string    `json:"status"` // "uploading", "completed", "failed"
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run cmd/direct_upload.go <视频文件夹路径> [并发数]")
		fmt.Println("例如: go run cmd/direct_upload.go \"/www/wwwroot/JAVAPI.COM/downloads/20 最新流出，推特大奶网黄女神kitty付费作品，夏天要来了玩水水，泳池坏坏跟湿湿的淫水混合\" 3")
		return
	}

	videoDir := os.Args[1]
	concurrency := 3 // 默认3个并发
	if len(os.Args) > 2 {
		if c, err := fmt.Sscanf(os.Args[2], "%d", &concurrency); err != nil || c != 1 {
			fmt.Printf("⚠️ 无效的并发数，使用默认值: %d\n", concurrency)
		}
	}

	// 限制最大并发数
	if concurrency > 5 {
		concurrency = 5
		fmt.Printf("⚠️ 并发数过高，自动调整为: %d\n", concurrency)
	}

	fmt.Printf("🎬 视频直接上传到本地Telegraph图床\n")
	fmt.Printf("📁 视频目录: %s\n", videoDir)
	fmt.Printf("⚡ 并发线程: %d\n", concurrency)
	fmt.Printf("🌐 图床地址: http://localhost:3000\n")

	// 初始化日志
	logger.Init("info", "text")

	// 检查视频目录
	if _, err := os.Stat(videoDir); os.IsNotExist(err) {
		fmt.Printf("❌ 视频目录不存在: %s\n", videoDir)
		return
	}

	// 扫描视频文件
	fmt.Println("📋 扫描视频文件...")
	videoFiles, err := scanVideoFiles(videoDir)
	if err != nil {
		fmt.Printf("❌ 扫描视频失败: %v\n", err)
		return
	}

	if len(videoFiles) == 0 {
		fmt.Println("❌ 未找到视频文件")
		return
	}

	fmt.Printf("🎬 找到 %d 个视频文件\n", len(videoFiles))
	for i, video := range videoFiles {
		fmt.Printf("   %d. %s (%.2f MB)\n", i+1, filepath.Base(video), getFileSize(video))
	}

	// 创建本地图床客户端
	imgbbConfig := &imgbb.Config{
		APIKey:     "",  // 本地图床不需要API密钥
		BaseURL:    "http://localhost:3000",
		Timeout:    300 * time.Second, // 5分钟超时，适合大文件
		MaxRetries: 3,
	}
	imgbbClient := imgbb.NewClient(imgbbConfig)

	// 测试图床连接
	fmt.Println("📡 测试图床连接...")
	if err := imgbbClient.TestConnection(); err != nil {
		fmt.Printf("❌ 图床连接失败: %v\n", err)
		fmt.Println("💡 请确保Telegraph-Image Express服务正在运行 (http://localhost:3000)")
		return
	}
	fmt.Println("✅ 图床连接成功")

	fmt.Printf("🚀 开始直接上传 %d 个视频...\n", len(videoFiles))

	// 上传视频
	results := uploadVideos(videoFiles, concurrency, imgbbClient)

	// 显示结果
	displayResults(results)
}

// scanVideoFiles 扫描视频文件
func scanVideoFiles(videoDir string) ([]string, error) {
	var videoFiles []string
	videoExtensions := []string{".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v"}

	err := filepath.Walk(videoDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			ext := strings.ToLower(filepath.Ext(path))
			for _, videoExt := range videoExtensions {
				if ext == videoExt {
					videoFiles = append(videoFiles, path)
					break
				}
			}
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 按文件大小排序，优先处理小文件
	sort.Slice(videoFiles, func(i, j int) bool {
		info1, _ := os.Stat(videoFiles[i])
		info2, _ := os.Stat(videoFiles[j])
		return info1.Size() < info2.Size()
	})

	return videoFiles, nil
}

// getFileSize 获取文件大小(MB)
func getFileSize(filePath string) float64 {
	if info, err := os.Stat(filePath); err == nil {
		return float64(info.Size()) / (1024 * 1024)
	}
	return 0
}

// uploadVideos 上传视频
func uploadVideos(videoFiles []string, concurrency int, imgbbClient *imgbb.Client) []*VideoUploadResult {
	// 创建任务通道
	videoChan := make(chan string, concurrency)
	resultsChan := make(chan *VideoUploadResult, len(videoFiles))

	// 发送所有视频到通道
	go func() {
		for _, videoFile := range videoFiles {
			videoChan <- videoFile
		}
		close(videoChan)
	}()

	// 创建等待组
	var wg sync.WaitGroup

	// 启动工作协程
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for videoFile := range videoChan {
				result := uploadSingleVideo(workerID, videoFile, imgbbClient)
				resultsChan <- result
			}
		}(i)
	}

	// 等待所有工作完成
	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// 收集结果
	var results []*VideoUploadResult
	for result := range resultsChan {
		results = append(results, result)
	}

	return results
}

// uploadSingleVideo 上传单个视频
func uploadSingleVideo(workerID int, videoFile string, imgbbClient *imgbb.Client) *VideoUploadResult {
	videoName := filepath.Base(videoFile)
	fmt.Printf("🎬 Worker-%d: 开始上传视频 %s\n", workerID, videoName)

	// 获取文件信息
	fileInfo, err := os.Stat(videoFile)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 无法获取文件信息: %v\n", workerID, err)
		return &VideoUploadResult{
			VideoPath:  videoFile,
			VideoName:  videoName,
			Status:     "failed",
			UploadTime: time.Now(),
		}
	}

	// 创建视频结果记录
	result := &VideoUploadResult{
		VideoPath:  videoFile,
		VideoName:  videoName,
		FileSize:   fileInfo.Size(),
		UploadTime: time.Now(),
		Status:     "uploading",
	}

	// 读取视频文件
	fmt.Printf("📖 Worker-%d: 读取视频文件 (%.2f MB)...\n", workerID, float64(fileInfo.Size())/(1024*1024))
	videoData, err := os.ReadFile(videoFile)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 读取视频文件失败: %v\n", workerID, err)
		result.Status = "failed"
		return result
	}

	// 上传视频到图床
	fmt.Printf("⬆️ Worker-%d: 上传视频到图床...\n", workerID)
	uploadResult, err := imgbbClient.UploadData(videoData, videoName)
	if err != nil || !uploadResult.Success {
		fmt.Printf("❌ Worker-%d: 视频上传失败: %v\n", workerID, err)
		result.Status = "failed"
		return result
	}

	result.VideoURL = uploadResult.URL
	result.PlayURL = uploadResult.URL // 直接使用图床URL作为播放链接
	result.Status = "completed"

	fmt.Printf("🎉 Worker-%d: 视频 %s 上传完成！\n", workerID, videoName)
	fmt.Printf("   📎 播放链接: %s\n", result.PlayURL)
	fmt.Printf("   📏 文件大小: %.2f MB\n", float64(result.FileSize)/(1024*1024))

	return result
}

// displayResults 显示上传结果
func displayResults(results []*VideoUploadResult) {
	fmt.Printf("\n🎬 视频上传结果汇总:\n")
	fmt.Printf("=" + strings.Repeat("=", 80) + "\n")

	completedCount := 0
	failedCount := 0

	for _, result := range results {
		if result.Status == "completed" {
			completedCount++
			fmt.Printf("✅ %s\n", result.VideoName)
			fmt.Printf("   📎 播放链接: %s\n", result.PlayURL)
			fmt.Printf("   📏 文件大小: %.2f MB\n", float64(result.FileSize)/(1024*1024))
			fmt.Printf("   ⏱️ 上传时间: %s\n", result.UploadTime.Format("2006-01-02 15:04:05"))
			fmt.Println()
		} else if result.Status == "failed" {
			failedCount++
			fmt.Printf("❌ %s (失败)\n", result.VideoName)
		}
	}

	fmt.Printf("📊 总结: 成功 %d 个，失败 %d 个\n", completedCount, failedCount)
	
	if completedCount > 0 {
		fmt.Printf("\n💡 使用说明:\n")
		fmt.Printf("   - 复制播放链接到浏览器即可直接观看\n")
		fmt.Printf("   - 视频直接存储在本地Telegraph图床\n")
		fmt.Printf("   - 支持在线播放，无需下载\n")
	}
}