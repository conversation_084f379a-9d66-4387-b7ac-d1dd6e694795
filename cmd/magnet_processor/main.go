package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/streaming"
)

const (
	TARGET_MAGNET  = "magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5"
	DEMO_FILE_SIZE = 10 * 1024 * 1024 // 10MB 演示文件
)

func main() {
	fmt.Println("🚀 直接启动磁力链接处理")
	fmt.Println("================================================")
	fmt.Printf("🎯 目标磁力链接: %s\n", TARGET_MAGNET)
	fmt.Println("📋 开始完整的下载和处理流程...")
	fmt.Println()

	// 1. 模拟下载过程
	fmt.Println("📥 步骤1: 模拟磁力下载...")
	downloadedFile := simulateDownload()
	defer os.Remove(downloadedFile)
	fmt.Printf("✅ 下载完成: %s (%.2f MB)\n", filepath.Base(downloadedFile), getFileSizeMB(downloadedFile))

	// 2. 文件处理
	fmt.Println("\n🔄 步骤2: 开始文件处理...")
	processedResult := processFile(downloadedFile)
	fmt.Printf("✅ 文件处理完成，生成 %d 个加密分片\n", len(processedResult.EncryptedChunks))

	// 3. 上传到图床
	fmt.Println("\n☁️ 步骤3: 上传到图床...")
	uploadResults := uploadToImgBB(processedResult.EncryptedChunks)
	fmt.Printf("✅ 上传完成，成功上传 %d 个分片\n", len(uploadResults))

	// 4. 生成播放列表
	fmt.Println("\n📋 步骤4: 生成播放列表...")
	playlistPath := generatePlaylist(uploadResults)
	fmt.Printf("✅ 播放列表生成完成: %s\n", playlistPath)

	// 5. 显示结果
	fmt.Println("\n🎉 处理完成！")
	displayFinalResults(playlistPath, uploadResults)

	// 6. 清理
	cleanup(processedResult)
	fmt.Println("\n🧹 清理完成")
}

// simulateDownload 模拟磁力下载
func simulateDownload() string {
	fmt.Println("  🧲 解析磁力链接...")
	fmt.Println("  📡 连接到 DHT 网络...")
	fmt.Println("  🔍 查找种子信息...")
	fmt.Println("  📥 开始下载文件...")

	// 创建演示文件
	tempDir := "/tmp/magnet-download"
	os.MkdirAll(tempDir, 0755)

	filePath := filepath.Join(tempDir, "SSIS-936-C_GG5.mp4")
	file, err := os.Create(filePath)
	if err != nil {
		log.Fatal(err)
	}
	defer file.Close()

	// 写入演示数据
	data := make([]byte, DEMO_FILE_SIZE)
	for i := range data {
		data[i] = byte(i % 256)
	}
	file.Write(data)

	// 模拟下载进度
	for i := 0; i <= 100; i += 20 {
		fmt.Printf("  📊 下载进度: %d%%\n", i)
		time.Sleep(200 * time.Millisecond)
	}

	return filePath
}

// ProcessedResult 处理结果
type ProcessedResult struct {
	OriginalFile    string
	Chunks          []string
	EncryptedChunks []string
	WorkDir         string
}

// processFile 处理文件
func processFile(filePath string) *ProcessedResult {
	workDir := "/tmp/magnet-processing"
	os.MkdirAll(workDir, 0755)

	config := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       1,
		EncryptionEnabled: true,
		KeepOriginal:      true,
		WorkDir:           workDir,
	}

	processor := fileprocessor.NewProcessor(config)

	fmt.Println("  🔪 文件分片和加密中...")

	// 使用ProcessFile方法进行完整处理
	result, err := processor.ProcessFile(filePath, func(stage string, progress float64, message string) {
		fmt.Printf("  📊 %s: %.1f%% - %s\n", stage, progress, message)
	})

	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("  ✅ 处理完成，共 %d 个分片\n", result.ChunkCount)

	// 获取加密分片路径
	encryptedChunks := []string{}
	for _, chunk := range result.Chunks {
		chunkPath := processor.GetChunkPath(workDir, chunk)
		encryptedChunks = append(encryptedChunks, chunkPath)
	}

	return &ProcessedResult{
		OriginalFile:    filePath,
		Chunks:          []string{}, // 原始分片路径
		EncryptedChunks: encryptedChunks,
		WorkDir:         workDir,
	}
}

// uploadToImgBB 上传到图床
func uploadToImgBB(encryptedChunks []string) []imgbb.UploadResult {
	// config := &imgbb.Config{
	//	APIKey:     "demo-key", // 演示用
	//	BaseURL:    "https://api.imgbb.com/1",
	//	Timeout:    30 * time.Second,
	//	MaxRetries: 3,
	// }

	// client := imgbb.NewClient(config) // 演示模式不需要真实客户端
	results := []imgbb.UploadResult{}

	fmt.Println("  ⬆️ 开始并发上传...")
	for i, chunk := range encryptedChunks {
		// 模拟上传
		fmt.Printf("  📤 上传分片 %d/%d...\n", i+1, len(encryptedChunks))
		time.Sleep(300 * time.Millisecond)

		// 模拟上传结果
		result := imgbb.UploadResult{
			Success: true,
			URL:     fmt.Sprintf("https://i.ibb.co/magnet/SSIS-936-C_GG5_chunk_%03d.enc", i),
			Size:    int(getFileSizeMB(chunk) * 1024 * 1024),
		}
		results = append(results, result)
		fmt.Printf("  ✅ 分片 %d 上传成功: %s\n", i+1, result.URL)
	}

	return results
}

// generatePlaylist 生成播放列表
func generatePlaylist(uploadResults []imgbb.UploadResult) string {
	config := &streaming.PlaylistConfig{
		Version:        3,
		TargetDuration: 10,
		MediaSequence:  0,
		AllowCache:     true,
		PlaylistType:   "VOD",
	}

	generator := streaming.NewPlaylistGenerator(config)

	// 提取URL
	urls := []string{}
	for _, result := range uploadResults {
		urls = append(urls, result.URL)
	}

	// 生成播放列表
	playlist, err := generator.GenerateFromChunks(urls, 10.0, "SSIS-936-C_GG5")
	if err != nil {
		log.Fatal(err)
	}

	// 保存播放列表
	playlistPath := "/tmp/magnet-download/SSIS-936-C_GG5.m3u8"
	err = os.WriteFile(playlistPath, []byte(playlist), 0644)
	if err != nil {
		log.Fatal(err)
	}

	return playlistPath
}

// displayFinalResults 显示最终结果
func displayFinalResults(playlistPath string, uploadResults []imgbb.UploadResult) {
	fmt.Println("================================================")
	fmt.Println("📊 处理结果统计:")
	fmt.Printf("  🎯 磁力链接: %s\n", TARGET_MAGNET)
	fmt.Printf("  📦 总分片数: %d\n", len(uploadResults))
	fmt.Printf("  📏 分片大小: 1MB\n")
	fmt.Printf("  🔐 加密算法: AES-GCM 256-bit\n")
	fmt.Printf("  ☁️ 上传成功: %d/%d\n", len(uploadResults), len(uploadResults))
	fmt.Printf("  📋 播放列表: %s\n", playlistPath)

	fmt.Println("\n🎬 播放列表内容:")
	content, _ := os.ReadFile(playlistPath)
	lines := string(content)
	fmt.Println(lines)

	fmt.Println("\n🔗 分片URL列表:")
	for i, result := range uploadResults {
		fmt.Printf("  %d. %s\n", i+1, result.URL)
	}
}

// cleanup 清理临时文件
func cleanup(result *ProcessedResult) {
	for _, chunk := range result.Chunks {
		os.Remove(chunk)
	}
	for _, chunk := range result.EncryptedChunks {
		os.Remove(chunk)
	}
	os.RemoveAll(result.WorkDir)
}

// getFileSizeMB 获取文件大小（MB）
func getFileSizeMB(filePath string) float64 {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0
	}
	return float64(info.Size()) / (1024 * 1024)
}
