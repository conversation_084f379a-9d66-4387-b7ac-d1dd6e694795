package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/doodstream"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/logger"
)

// 全局速率限制器
var (
	lastUploadTime = time.Now()
	uploadMutex    sync.Mutex
)

// smartRateLimit 智能速率限制
func smartRateLimit(workerID int) {
	uploadMutex.Lock()
	defer uploadMutex.Unlock()
	
	timeSinceLastUpload := time.Since(lastUploadTime)
	minInterval := 500 * time.Millisecond // 最小间隔0.5秒，充分利用带宽
	
	if timeSinceLastUpload < minInterval {
		waitTime := minInterval - timeSinceLastUpload
		time.Sleep(waitTime)
	}
	
	lastUploadTime = time.Now()
}

// VideoUploadResult 视频上传结果
type VideoUploadResult struct {
	VideoPath    string    `json:"video_path"`
	VideoName    string    `json:"video_name"`
	ShareCode    string    `json:"share_code"`
	PlayURL      string    `json:"play_url"`
	UploadTime   time.Time `json:"upload_time"`
	FileSize     int64     `json:"file_size"`
	Status       string    `json:"status"` // "uploading", "completed", "failed"
}

// UploadProgress 上传进度记录
type UploadProgress struct {
	TotalVideos     int                          `json:"total_videos"`
	CompletedVideos int64                        `json:"completed_videos"`
	VideoResults    map[string]*VideoUploadResult `json:"video_results"`
	LastUpdated     time.Time                    `json:"last_updated"`
	mutex           sync.RWMutex                 `json:"-"`
}

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	if len(os.Args) < 2 {
		fmt.Println("用法: go run cmd/smart_upload.go <视频文件夹路径> [并发数]")
		fmt.Println("例如: go run cmd/smart_upload.go \"/www/wwwroot/JAVAPI.COM/downloads/20 最新流出，推特大奶网黄女神kitty付费作品，夏天要来了玩水水，泳池坏坏跟湿湿的淫水混合\" 2")
		return
	}

	videoDir := os.Args[1]
	concurrency := 3 // 默认3个并发，充分利用带宽
	if len(os.Args) > 2 {
		if c, err := fmt.Sscanf(os.Args[2], "%d", &concurrency); err != nil || c != 1 {
			fmt.Printf("⚠️ 无效的并发数，使用默认值: %d\n", concurrency)
		}
	}

	// 限制最大并发数
	if concurrency > 5 {
		concurrency = 5
		fmt.Printf("⚠️ 并发数过高，自动调整为: %d\n", concurrency)
	}

	// 显示上传提供商信息
	uploadProvider := cfg.FileProcessing.UploadProvider
	if uploadProvider == "doodstream" {
		fmt.Printf("🎬 视频直接上传到DoodStream\n")
		fmt.Printf("📁 视频目录: %s\n", videoDir)
		fmt.Printf("⚡ 并发线程: %d\n", concurrency)
		fmt.Printf("🌐 DoodStream API: %s\n", cfg.FileProcessing.DoodStream.BaseURL)
	} else {
		fmt.Printf("🎬 视频上传到本地Telegraph图床\n")
		fmt.Printf("📁 视频目录: %s\n", videoDir)
		fmt.Printf("⚡ 并发线程: %d\n", concurrency)
		fmt.Printf("🌐 图床地址: %s\n", cfg.FileProcessing.ImgBB.BaseURL)
	}

	// 初始化日志
	logger.Init("info", "text")

	// 检查视频目录
	if _, err := os.Stat(videoDir); os.IsNotExist(err) {
		fmt.Printf("❌ 视频目录不存在: %s\n", videoDir)
		return
	}

	// 扫描视频文件
	fmt.Println("📋 扫描视频文件...")
	videoFiles, err := scanVideoFiles(videoDir)
	if err != nil {
		fmt.Printf("❌ 扫描视频失败: %v\n", err)
		return
	}

	if len(videoFiles) == 0 {
		fmt.Println("❌ 未找到视频文件")
		return
	}

	fmt.Printf("🎬 找到 %d 个视频文件\n", len(videoFiles))
	for i, video := range videoFiles {
		fmt.Printf("   %d. %s (%.2f MB)\n", i+1, filepath.Base(video), getFileSize(video))
	}

	// 根据配置创建上传客户端
	var imgbbClient *imgbb.Client
	var doodStreamClient *doodstream.Client
	
	if uploadProvider == "doodstream" {
		// 创建DoodStream客户端
		doodStreamConfig := &doodstream.Config{
			APIKey:     cfg.FileProcessing.DoodStream.APIKey,
			BaseURL:    cfg.FileProcessing.DoodStream.BaseURL,
			Timeout:    time.Duration(cfg.FileProcessing.DoodStream.Timeout) * time.Second,
			MaxRetries: cfg.FileProcessing.DoodStream.MaxRetries,
		}
		doodStreamClient = doodstream.NewClient(doodStreamConfig)

		// 测试DoodStream连接
		fmt.Println("📡 测试DoodStream连接...")
		if err := doodStreamClient.TestConnection(); err != nil {
			fmt.Printf("❌ DoodStream连接失败: %v\n", err)
			fmt.Println("💡 请检查API密钥和网络连接")
			return
		}
		fmt.Println("✅ DoodStream连接成功")
	} else {
		// 创建本地图床客户端
		imgbbConfig := &imgbb.Config{
			APIKey:     cfg.FileProcessing.ImgBB.APIKey,
			BaseURL:    cfg.FileProcessing.ImgBB.BaseURL,
			Timeout:    time.Duration(cfg.FileProcessing.ImgBB.Timeout) * time.Second,
			MaxRetries: cfg.FileProcessing.ImgBB.MaxRetries,
		}
		imgbbClient = imgbb.NewClient(imgbbConfig)

		// 测试图床连接
		fmt.Println("📡 测试图床连接...")
		if err := imgbbClient.TestConnection(); err != nil {
			fmt.Printf("❌ 图床连接失败: %v\n", err)
			fmt.Println("💡 请确保Telegraph-Image Express服务正在运行")
			return
		}
		fmt.Println("✅ 图床连接成功")
	}

	// 加载或创建进度文件
	progressFile := filepath.Join(videoDir, "upload_progress.json")
	progress := loadProgress(progressFile, len(videoFiles))

	fmt.Printf("📊 当前进度: %d/%d 视频已完成\n",
		atomic.LoadInt64(&progress.CompletedVideos), progress.TotalVideos)

	if int(atomic.LoadInt64(&progress.CompletedVideos)) >= progress.TotalVideos {
		fmt.Println("✅ 所有视频已上传完成")
		displayResults(progress)
		return
	}

	// 准备未上传的视频
	var pendingVideos []string
	for _, videoFile := range videoFiles {
		videoName := filepath.Base(videoFile)
		
		progress.mutex.RLock()
		result, exists := progress.VideoResults[videoName]
		progress.mutex.RUnlock()

		if !exists || result.Status != "completed" {
			pendingVideos = append(pendingVideos, videoFile)
		}
	}

	fmt.Printf("🚀 开始上传 %d 个视频...\n", len(pendingVideos))

	// 上传视频
	uploadVideos(pendingVideos, progress, progressFile, concurrency, uploadProvider, imgbbClient, doodStreamClient)

	// 保存最终进度
	saveProgress(progressFile, progress)

	// 显示结果
	displayResults(progress)
}

// scanVideoFiles 扫描视频文件
func scanVideoFiles(videoDir string) ([]string, error) {
	var videoFiles []string
	videoExtensions := []string{".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v"}

	err := filepath.Walk(videoDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			ext := strings.ToLower(filepath.Ext(path))
			for _, videoExt := range videoExtensions {
				if ext == videoExt {
					videoFiles = append(videoFiles, path)
					break
				}
			}
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 按文件大小排序，优先处理大文件
	sort.Slice(videoFiles, func(i, j int) bool {
		info1, _ := os.Stat(videoFiles[i])
		info2, _ := os.Stat(videoFiles[j])
		return info1.Size() > info2.Size()
	})

	return videoFiles, nil
}

// getFileSize 获取文件大小(MB)
func getFileSize(filePath string) float64 {
	if info, err := os.Stat(filePath); err == nil {
		return float64(info.Size()) / (1024 * 1024)
	}
	return 0
}

// uploadVideos 上传视频
func uploadVideos(videoFiles []string, progress *UploadProgress, progressFile string, concurrency int, uploadProvider string, imgbbClient *imgbb.Client, doodStreamClient *doodstream.Client) {
	// 创建任务通道
	videoChan := make(chan string, concurrency)

	// 发送所有视频到通道
	go func() {
		for _, videoFile := range videoFiles {
			videoChan <- videoFile
		}
		close(videoChan)
	}()

	// 创建等待组
	var wg sync.WaitGroup

	// 启动工作协程
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for videoFile := range videoChan {
				uploadSingleVideo(workerID, videoFile, uploadProvider, imgbbClient, doodStreamClient, progress)
				
				// 工作间隔，避免过载
				time.Sleep(3 * time.Second)
			}
		}(i)
	}

	// 启动进度保存协程
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			current := atomic.LoadInt64(&progress.CompletedVideos)
			total := int64(progress.TotalVideos)

			if current >= total {
				break
			}

			saveProgress(progressFile, progress)
			fmt.Printf("📊 进度更新: %d/%d 视频已完成\n", current, total)
		}
	}()

	// 等待所有工作完成
	wg.Wait()
}

// uploadSingleVideo 上传单个视频
func uploadSingleVideo(workerID int, videoFile string, uploadProvider string, imgbbClient *imgbb.Client, doodStreamClient *doodstream.Client, progress *UploadProgress) {
	videoName := filepath.Base(videoFile)
	fmt.Printf("🎬 Worker-%d: 开始处理视频 %s\n", workerID, videoName)

	// 获取文件信息
	fileInfo, err := os.Stat(videoFile)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 无法获取文件信息: %v\n", workerID, err)
		return
	}

	// 创建视频结果记录
	result := &VideoUploadResult{
		VideoPath:  videoFile,
		VideoName:  videoName,
		FileSize:   fileInfo.Size(),
		UploadTime: time.Now(),
		Status:     "uploading",
	}

	progress.mutex.Lock()
	progress.VideoResults[videoName] = result
	progress.mutex.Unlock()

	// 根据上传提供商选择处理方式
	if uploadProvider == "doodstream" {
		// DoodStream直接上传完整视频文件
		uploadVideoDoodStream(workerID, videoFile, videoName, doodStreamClient, result, progress)
	} else {
		// 简化的ImgBB上传（不再使用分片）
		uploadVideoSimple(workerID, videoFile, videoName, imgbbClient, result, progress)
	}
}

// uploadVideoDoodStream DoodStream直接上传
func uploadVideoDoodStream(workerID int, videoFile, videoName string, client *doodstream.Client, result *VideoUploadResult, progress *UploadProgress) {
	fmt.Printf("📤 Worker-%d: 直接上传到DoodStream...\n", workerID)
	
	// 直接上传完整视频文件
	uploadResult, err := client.UploadFile(videoFile)
	if err != nil {
		fmt.Printf("❌ Worker-%d: DoodStream上传失败: %v\n", workerID, err)
		result.Status = "failed"
		return
	}

	if !uploadResult.Success {
		fmt.Printf("❌ Worker-%d: DoodStream上传失败: %s\n", workerID, uploadResult.Error)
		result.Status = "failed"
		return
	}

	// 更新结果
	result.PlayURL = uploadResult.PlayURL
	result.ShareCode = uploadResult.FileCode
	result.Status = "completed"
	atomic.AddInt64(&progress.CompletedVideos, 1)

	fmt.Printf("🎉 Worker-%d: 视频 %s 上传完成！\n", workerID, videoName)
	fmt.Printf("   📎 播放链接: %s\n", result.PlayURL)
	fmt.Printf("   🔑 文件代码: %s\n", result.ShareCode)
	fmt.Printf("   📏 文件大小: %.2f MB\n", float64(uploadResult.Size)/(1024*1024))
}

// uploadVideoSimple 简化的视频上传（不使用分片）
func uploadVideoSimple(workerID int, videoFile, videoName string, client *imgbb.Client, result *VideoUploadResult, progress *UploadProgress) {
	fmt.Printf("📤 Worker-%d: 简化上传到图床...\n", workerID)
	
	// 读取视频文件
	videoData, err := os.ReadFile(videoFile)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 读取视频文件失败: %v\n", workerID, err)
		result.Status = "failed"
		return
	}

	// 直接上传视频文件
	uploadResult, err := client.UploadData(videoData, videoName)
	if err != nil {
		fmt.Printf("❌ Worker-%d: 图床上传失败: %v\n", workerID, err)
		result.Status = "failed"
		return
	}

	if !uploadResult.Success {
		fmt.Printf("❌ Worker-%d: 图床上传失败: %s\n", workerID, uploadResult.Error)
		result.Status = "failed"
		return
	}

	// 更新结果
	result.PlayURL = uploadResult.URL
	result.ShareCode = fmt.Sprintf("simple_%d", time.Now().Unix())
	result.Status = "completed"
	atomic.AddInt64(&progress.CompletedVideos, 1)

	fmt.Printf("🎉 Worker-%d: 视频 %s 上传完成！\n", workerID, videoName)
	fmt.Printf("   📎 文件链接: %s\n", result.PlayURL)
	fmt.Printf("   🔑 分享码: %s\n", result.ShareCode)
	fmt.Printf("   📏 文件大小: %.2f MB\n", float64(len(videoData))/(1024*1024))
}



// loadProgress 加载上传进度
func loadProgress(progressFile string, totalVideos int) *UploadProgress {
	progress := &UploadProgress{
		TotalVideos:     totalVideos,
		CompletedVideos: 0,
		VideoResults:    make(map[string]*VideoUploadResult),
		LastUpdated:     time.Now(),
	}
	
	if data, err := os.ReadFile(progressFile); err == nil {
		json.Unmarshal(data, progress)
		progress.TotalVideos = totalVideos
		
		// 统计已完成的视频
		completed := int64(0)
		for _, result := range progress.VideoResults {
			if result.Status == "completed" {
				completed++
			}
		}
		atomic.StoreInt64(&progress.CompletedVideos, completed)
	}
	
	return progress
}

// saveProgress 保存上传进度
func saveProgress(progressFile string, progress *UploadProgress) {
	progress.mutex.RLock()
	defer progress.mutex.RUnlock()
	
	progress.LastUpdated = time.Now()
	data, _ := json.MarshalIndent(progress, "", "  ")
	os.WriteFile(progressFile, data, 0644)
}

// displayResults 显示上传结果
func displayResults(progress *UploadProgress) {
	fmt.Printf("\n🎬 视频上传结果汇总:\n")
	fmt.Printf("=" + strings.Repeat("=", 80) + "\n")

	progress.mutex.RLock()
	defer progress.mutex.RUnlock()

	completedCount := 0
	failedCount := 0

	for _, result := range progress.VideoResults {
		if result.Status == "completed" {
			completedCount++
			fmt.Printf("✅ %s\n", result.VideoName)
			fmt.Printf("   📎 播放链接: %s\n", result.PlayURL)
			fmt.Printf("   🔑 分享码: %s\n", result.ShareCode)
			fmt.Printf("   📏 文件大小: %.2f MB\n", float64(result.FileSize)/(1024*1024))
			fmt.Printf("   ⏱️ 上传时间: %s\n", result.UploadTime.Format("2006-01-02 15:04:05"))
			fmt.Println()
		} else if result.Status == "failed" {
			failedCount++
			fmt.Printf("❌ %s (失败)\n", result.VideoName)
		}
	}

	fmt.Printf("📊 总结: 成功 %d 个，失败 %d 个\n", completedCount, failedCount)
	
	if completedCount > 0 {
		fmt.Printf("\n💡 使用说明:\n")
		fmt.Printf("   - 复制播放链接到浏览器即可观看\n")
		fmt.Printf("   - 分享码可用于快速访问视频\n")
		fmt.Printf("   - 视频已成功上传到云端存储\n")
	}
}