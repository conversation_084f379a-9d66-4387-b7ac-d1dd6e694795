package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"magnet-downloader/pkg/imgur"
)

// ChunkUploadTask 分片上传任务
type ChunkUploadTask struct {
	Index    int
	FilePath string
	FileName string
}

// UploadProgress 上传进度
type UploadProgress struct {
	Total     int64
	Completed int64
	Failed    int64
}

// uploadChunksConcurrently 并发上传分片
func uploadChunksConcurrently(imgurClient *imgur.Client, chunkFiles []string, concurrency int) []imgur.UploadResult {
	var results []imgur.UploadResult
	var resultsMutex sync.Mutex
	var progress UploadProgress
	progress.Total = int64(len(chunkFiles))

	// 创建任务通道
	taskChan := make(chan ChunkUploadTask, len(chunkFiles))
	var wg sync.WaitGroup

	// 启动进度监控
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
		
		for range ticker.C {
			completed := atomic.LoadInt64(&progress.Completed)
			failed := atomic.LoadInt64(&progress.Failed)
			total := progress.Total
			
			if completed+failed >= total {
				break
			}
			
			fmt.Printf("📊 上传进度: %d/%d 完成, %d 失败 (%.1f%%)\n", 
				completed, total, failed, float64(completed)/float64(total)*100)
		}
	}()

	// 启动工作协程
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			
			for task := range taskChan {
				// 读取分片数据
				chunkData, err := os.ReadFile(task.FilePath)
				if err != nil {
					fmt.Printf("❌ Worker-%d: 读取分片 %d 失败: %v\n", workerID, task.Index+1, err)
					atomic.AddInt64(&progress.Failed, 1)
					continue
				}
				
				// 上传分片（使用隐写术）
				uploadResult, err := imgurClient.UploadWithSteganography(chunkData, task.FileName)
				if err != nil {
					fmt.Printf("❌ Worker-%d: 上传分片 %d 失败: %v\n", workerID, task.Index+1, err)
					atomic.AddInt64(&progress.Failed, 1)
					continue
				}
				
				if !uploadResult.Success {
					fmt.Printf("❌ Worker-%d: 上传分片 %d 失败: %s\n", workerID, task.Index+1, uploadResult.Error)
					atomic.AddInt64(&progress.Failed, 1)
					continue
				}
				
				// 保存结果
				resultsMutex.Lock()
				results = append(results, *uploadResult)
				resultsMutex.Unlock()
				
				atomic.AddInt64(&progress.Completed, 1)
				fmt.Printf("✅ Worker-%d: 分片 %d/%d 上传成功\n", workerID, task.Index+1, len(chunkFiles))
				
				// 避免API限制 - 每个worker间隔上传
				time.Sleep(1 * time.Second)
			}
		}(i)
	}

	// 发送任务
	for i, chunkFile := range chunkFiles {
		task := ChunkUploadTask{
			Index:    i,
			FilePath: chunkFile,
			FileName: filepath.Base(chunkFile),
		}
		taskChan <- task
	}
	close(taskChan)

	// 等待所有任务完成
	wg.Wait()

	completed := atomic.LoadInt64(&progress.Completed)
	failed := atomic.LoadInt64(&progress.Failed)
	fmt.Printf("🎉 上传完成: %d/%d 成功, %d 失败\n", completed, progress.Total, failed)

	return results
}