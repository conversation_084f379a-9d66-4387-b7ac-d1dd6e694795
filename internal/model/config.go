package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// ConfigType 配置类型枚举
type ConfigType string

const (
	ConfigTypeString ConfigType = "string" // 字符串
	ConfigTypeInt    ConfigType = "int"    // 整数
	ConfigTypeFloat  ConfigType = "float"  // 浮点数
	ConfigTypeBool   ConfigType = "bool"   // 布尔值
	ConfigTypeJSON   ConfigType = "json"   // JSON对象
	ConfigTypeArray  ConfigType = "array"  // 数组
)

// SystemConfig 系统配置模型
type SystemConfig struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Key         string         `json:"key" gorm:"type:varchar(100);uniqueIndex;not null;comment:配置键"`
	Value       string         `json:"value" gorm:"type:text;comment:配置值"`
	Type        ConfigType     `json:"type" gorm:"type:varchar(20);default:'string';comment:配置类型"`
	Description string         `json:"description" gorm:"type:varchar(500);comment:配置描述"`
	Category    string         `json:"category" gorm:"type:varchar(50);comment:配置分类"`
	IsPublic    bool           `json:"is_public" gorm:"type:boolean;default:false;comment:是否公开"`
	IsEditable  bool           `json:"is_editable" gorm:"type:boolean;default:true;comment:是否可编辑"`
	CreatedAt   time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt   time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:删除时间"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_configs"
}

// GetStringValue 获取字符串值
func (c *SystemConfig) GetStringValue() string {
	return c.Value
}

// GetIntValue 获取整数值
func (c *SystemConfig) GetIntValue() int {
	var value int
	if err := json.Unmarshal([]byte(c.Value), &value); err != nil {
		return 0
	}
	return value
}

// GetFloatValue 获取浮点数值
func (c *SystemConfig) GetFloatValue() float64 {
	var value float64
	if err := json.Unmarshal([]byte(c.Value), &value); err != nil {
		return 0.0
	}
	return value
}

// GetBoolValue 获取布尔值
func (c *SystemConfig) GetBoolValue() bool {
	var value bool
	if err := json.Unmarshal([]byte(c.Value), &value); err != nil {
		return false
	}
	return value
}

// GetJSONValue 获取JSON值
func (c *SystemConfig) GetJSONValue(v interface{}) error {
	return json.Unmarshal([]byte(c.Value), v)
}

// SetValue 设置值（自动序列化）
func (c *SystemConfig) SetValue(value interface{}) error {
	switch v := value.(type) {
	case string:
		c.Value = v
		c.Type = ConfigTypeString
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		data, err := json.Marshal(v)
		if err != nil {
			return err
		}
		c.Value = string(data)
		c.Type = ConfigTypeInt
	case float32, float64:
		data, err := json.Marshal(v)
		if err != nil {
			return err
		}
		c.Value = string(data)
		c.Type = ConfigTypeFloat
	case bool:
		data, err := json.Marshal(v)
		if err != nil {
			return err
		}
		c.Value = string(data)
		c.Type = ConfigTypeBool
	default:
		data, err := json.Marshal(v)
		if err != nil {
			return err
		}
		c.Value = string(data)
		c.Type = ConfigTypeJSON
	}
	return nil
}

// BeforeCreate GORM钩子：创建前
func (c *SystemConfig) BeforeCreate(tx *gorm.DB) error {
	// 设置默认值
	if c.Type == "" {
		c.Type = ConfigTypeString
	}
	if c.Category == "" {
		c.Category = "general"
	}
	return nil
}

// ConfigRequest 配置请求
type ConfigRequest struct {
	Key         string      `json:"key" binding:"required"`
	Value       interface{} `json:"value" binding:"required"`
	Description string      `json:"description"`
	Category    string      `json:"category"`
	IsPublic    bool        `json:"is_public"`
	IsEditable  bool        `json:"is_editable"`
}

// ConfigResponse 配置响应
type ConfigResponse struct {
	ID          uint        `json:"id"`
	Key         string      `json:"key"`
	Value       interface{} `json:"value"`
	Type        ConfigType  `json:"type"`
	Description string      `json:"description"`
	Category    string      `json:"category"`
	IsPublic    bool        `json:"is_public"`
	IsEditable  bool        `json:"is_editable"`
	CreatedAt   time.Time   `json:"created_at"`
	UpdatedAt   time.Time   `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (c *SystemConfig) ToResponse() *ConfigResponse {
	var value interface{}

	switch c.Type {
	case ConfigTypeString:
		value = c.GetStringValue()
	case ConfigTypeInt:
		value = c.GetIntValue()
	case ConfigTypeFloat:
		value = c.GetFloatValue()
	case ConfigTypeBool:
		value = c.GetBoolValue()
	case ConfigTypeJSON, ConfigTypeArray:
		var jsonValue interface{}
		if err := c.GetJSONValue(&jsonValue); err == nil {
			value = jsonValue
		} else {
			value = c.Value
		}
	default:
		value = c.Value
	}

	return &ConfigResponse{
		ID:          c.ID,
		Key:         c.Key,
		Value:       value,
		Type:        c.Type,
		Description: c.Description,
		Category:    c.Category,
		IsPublic:    c.IsPublic,
		IsEditable:  c.IsEditable,
		CreatedAt:   c.CreatedAt,
		UpdatedAt:   c.UpdatedAt,
	}
}

// 预定义的系统配置键
const (
	ConfigKeyMaxConcurrentDownloads = "max_concurrent_downloads" // 最大并发下载数
	ConfigKeyDefaultDownloadPath    = "default_download_path"    // 默认下载路径
	ConfigKeyMaxRetries             = "max_retries"              // 最大重试次数
	ConfigKeyDownloadSpeedLimit     = "download_speed_limit"     // 下载速度限制
	ConfigKeyUploadSpeedLimit       = "upload_speed_limit"       // 上传速度限制
	ConfigKeyAutoCleanCompleted     = "auto_clean_completed"     // 自动清理已完成任务
	ConfigKeyCleanAfterDays         = "clean_after_days"         // 清理天数
	ConfigKeyEnableNotifications    = "enable_notifications"     // 启用通知
	ConfigKeySystemMaintenance      = "system_maintenance"       // 系统维护模式
)

// DefaultConfigs 默认配置
var DefaultConfigs = []SystemConfig{
	{
		Key:         ConfigKeyMaxConcurrentDownloads,
		Value:       "5",
		Type:        ConfigTypeInt,
		Description: "最大并发下载任务数",
		Category:    "download",
		IsPublic:    true,
		IsEditable:  true,
	},
	{
		Key:         ConfigKeyDefaultDownloadPath,
		Value:       "/downloads",
		Type:        ConfigTypeString,
		Description: "默认下载保存路径",
		Category:    "download",
		IsPublic:    true,
		IsEditable:  true,
	},
	{
		Key:         ConfigKeyMaxRetries,
		Value:       "3",
		Type:        ConfigTypeInt,
		Description: "下载失败最大重试次数",
		Category:    "download",
		IsPublic:    true,
		IsEditable:  true,
	},
	{
		Key:         ConfigKeyDownloadSpeedLimit,
		Value:       "0",
		Type:        ConfigTypeInt,
		Description: "下载速度限制(KB/s，0表示无限制)",
		Category:    "download",
		IsPublic:    true,
		IsEditable:  true,
	},
	{
		Key:         ConfigKeyAutoCleanCompleted,
		Value:       "false",
		Type:        ConfigTypeBool,
		Description: "自动清理已完成的下载任务",
		Category:    "system",
		IsPublic:    true,
		IsEditable:  true,
	},
	{
		Key:         ConfigKeyCleanAfterDays,
		Value:       "30",
		Type:        ConfigTypeInt,
		Description: "已完成任务保留天数",
		Category:    "system",
		IsPublic:    true,
		IsEditable:  true,
	},
	{
		Key:         ConfigKeyEnableNotifications,
		Value:       "true",
		Type:        ConfigTypeBool,
		Description: "启用系统通知",
		Category:    "system",
		IsPublic:    true,
		IsEditable:  true,
	},
	{
		Key:         ConfigKeySystemMaintenance,
		Value:       "false",
		Type:        ConfigTypeBool,
		Description: "系统维护模式",
		Category:    "system",
		IsPublic:    false,
		IsEditable:  true,
	},
}
