package model

import (
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserRole 用户角色枚举
type UserRole string

const (
	UserRoleAdmin UserRole = "admin" // 管理员
	UserRoleUser  UserRole = "user"  // 普通用户
	UserRoleGuest UserRole = "guest" // 访客
)

// UserStatus 用户状态枚举
type UserStatus string

const (
	UserStatusActive   UserStatus = "active"   // 活跃
	UserStatusInactive UserStatus = "inactive" // 非活跃
	UserStatusBanned   UserStatus = "banned"   // 被禁用
)

// User 用户模型
type User struct {
	ID           uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Username     string         `json:"username" gorm:"type:varchar(100);uniqueIndex;not null;comment:用户名"`
	Email        string         `json:"email" gorm:"type:varchar(255);uniqueIndex;comment:邮箱"`
	PasswordHash string         `json:"-" gorm:"type:varchar(255);not null;comment:密码哈希"`
	Role         UserRole       `json:"role" gorm:"type:varchar(20);default:'user';comment:用户角色"`
	Status       UserStatus     `json:"status" gorm:"type:varchar(20);default:'active';comment:用户状态"`
	Avatar       string         `json:"avatar" gorm:"type:varchar(500);comment:头像URL"`
	LastLoginAt  *time.Time     `json:"last_login_at" gorm:"comment:最后登录时间"`
	LoginCount   int            `json:"login_count" gorm:"type:int;default:0;comment:登录次数"`
	CreatedAt    time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt    time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:删除时间"`

	// 关联关系
	DownloadTasks []DownloadTask `json:"download_tasks,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// SetPassword 设置密码（加密存储）
func (u *User) SetPassword(password string) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.PasswordHash = string(hashedPassword)
	return nil
}

// CheckPassword 验证密码
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
	return err == nil
}

// IsAdmin 检查是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == UserRoleAdmin
}

// IsActive 检查用户是否活跃
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsBanned 检查用户是否被禁用
func (u *User) IsBanned() bool {
	return u.Status == UserStatusBanned
}

// CanDownload 检查用户是否可以下载
func (u *User) CanDownload() bool {
	return u.IsActive() && !u.IsBanned()
}

// UpdateLoginInfo 更新登录信息
func (u *User) UpdateLoginInfo() {
	now := time.Now()
	u.LastLoginAt = &now
	u.LoginCount++
}

// BeforeCreate GORM钩子：创建前
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// 设置默认值
	if u.Role == "" {
		u.Role = UserRoleUser
	}
	if u.Status == "" {
		u.Status = UserStatusActive
	}
	return nil
}

// UserProfile 用户资料（用于API响应，不包含敏感信息）
type UserProfile struct {
	ID          uint       `json:"id"`
	Username    string     `json:"username"`
	Email       string     `json:"email"`
	Role        UserRole   `json:"role"`
	Status      UserStatus `json:"status"`
	Avatar      string     `json:"avatar"`
	LastLoginAt *time.Time `json:"last_login_at"`
	LoginCount  int        `json:"login_count"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// ToProfile 转换为用户资料
func (u *User) ToProfile() *UserProfile {
	return &UserProfile{
		ID:          u.ID,
		Username:    u.Username,
		Email:       u.Email,
		Role:        u.Role,
		Status:      u.Status,
		Avatar:      u.Avatar,
		LastLoginAt: u.LastLoginAt,
		LoginCount:  u.LoginCount,
		CreatedAt:   u.CreatedAt,
		UpdatedAt:   u.UpdatedAt,
	}
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string   `json:"username" binding:"required,min=3,max=50"`
	Email    string   `json:"email" binding:"required,email"`
	Password string   `json:"password" binding:"required,min=6"`
	Role     UserRole `json:"role"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Email  string     `json:"email" binding:"omitempty,email"`
	Role   UserRole   `json:"role"`
	Status UserStatus `json:"status"`
	Avatar string     `json:"avatar"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	User  *UserProfile `json:"user"`
	Token string       `json:"token"`
}
