package websocket

import (
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/logger"

	"gorm.io/gorm"
)

// Service WebSocket服务
type Service struct {
	hub *Hub
	db  *gorm.DB
}

// NewService 创建WebSocket服务
func NewService(db *gorm.DB) *Service {
	hub := NewHub()
	service := &Service{
		hub: hub,
		db:  db,
	}

	// 启动Hub
	go hub.Run()

	return service
}

// GetHub 获取Hub
func (s *Service) GetHub() *Hub {
	return s.hub
}

// NotifyTaskCreated 通知任务创建
func (s *Service) NotifyTaskCreated(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskCreated, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task created: TaskID=%d, UserID=%d", task.ID, task.UserID)
}

// NotifyTaskStarted 通知任务开始
func (s *Service) NotifyTaskStarted(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskStarted, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task started: TaskID=%d, UserID=%d", task.ID, task.UserID)
}

// NotifyTaskProgress 通知任务进度更新
func (s *Service) NotifyTaskProgress(task *model.DownloadTask) {
	msg := NewTaskProgressMessage(task)
	s.hub.SendToUser(task.UserID, msg)

	logger.Debugf("Notified task progress: TaskID=%d, Progress=%.2f%%", task.ID, task.Progress)
}

// NotifyTaskPaused 通知任务暂停
func (s *Service) NotifyTaskPaused(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskPaused, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task paused: TaskID=%d, UserID=%d", task.ID, task.UserID)
}

// NotifyTaskResumed 通知任务恢复
func (s *Service) NotifyTaskResumed(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskResumed, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task resumed: TaskID=%d, UserID=%d", task.ID, task.UserID)
}

// NotifyTaskCompleted 通知任务完成
func (s *Service) NotifyTaskCompleted(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskCompleted, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task completed: TaskID=%d, UserID=%d", task.ID, task.UserID)
}

// NotifyTaskFailed 通知任务失败
func (s *Service) NotifyTaskFailed(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskFailed, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task failed: TaskID=%d, UserID=%d, Error=%s",
		task.ID, task.UserID, task.ErrorMessage)
}

// NotifyTaskCancelled 通知任务取消
func (s *Service) NotifyTaskCancelled(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskCancelled, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task cancelled: TaskID=%d, UserID=%d", task.ID, task.UserID)
}

// NotifyTaskDeleted 通知任务删除
func (s *Service) NotifyTaskDeleted(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskDeleted, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task deleted: TaskID=%d, UserID=%d", task.ID, task.UserID)
}

// NotifyUserLogin 通知用户登录
func (s *Service) NotifyUserLogin(userID uint, username, ip string) {
	msg := NewUserEventMessage(MessageTypeUserLogin, userID, username, "login", ip)

	// 发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified user login: UserID=%d, Username=%s, IP=%s", userID, username, ip)
}

// NotifyUserLogout 通知用户登出
func (s *Service) NotifyUserLogout(userID uint, username, ip string) {
	msg := NewUserEventMessage(MessageTypeUserLogout, userID, username, "logout", ip)

	// 发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified user logout: UserID=%d, Username=%s, IP=%s", userID, username, ip)
}

// BroadcastSystemNotification 广播系统通知
func (s *Service) BroadcastSystemNotification(title, message, level string) {
	msg := NewSystemNotificationMessage(title, message, level)
	s.hub.Broadcast(msg)

	logger.Infof("Broadcast system notification: Title=%s, Level=%s", title, level)
}

// BroadcastSystemMaintenance 广播系统维护通知
func (s *Service) BroadcastSystemMaintenance(title, message string, startTime, endTime int64) {
	data := map[string]interface{}{
		"title":      title,
		"message":    message,
		"start_time": startTime,
		"end_time":   endTime,
	}

	msg := NewMessage(MessageTypeSystemMaintenance, data)
	s.hub.Broadcast(msg)

	logger.Infof("Broadcast system maintenance: Title=%s", title)
}

// BroadcastSystemAlert 广播系统告警
func (s *Service) BroadcastSystemAlert(alertID, title, message, severity, component string) {
	msg := NewSystemAlertMessage(alertID, title, message, severity, component)
	s.hub.Broadcast(msg)

	logger.Infof("Broadcast system alert: AlertID=%s, Severity=%s, Component=%s",
		alertID, severity, component)
}

// SendToUser 发送消息给指定用户
func (s *Service) SendToUser(userID uint, msgType MessageType, data interface{}) {
	msg := NewUserMessage(msgType, userID, data)
	s.hub.SendToUser(userID, msg)

	logger.Debugf("Sent message to user %d: Type=%s", userID, msgType)
}

// SendToChannel 发送消息给指定频道
func (s *Service) SendToChannel(channel string, msgType MessageType, data interface{}) {
	msg := NewChannelMessage(msgType, channel, data)
	s.hub.SendToChannel(channel, msg)

	logger.Debugf("Sent message to channel %s: Type=%s", channel, msgType)
}

// NotifyTaskProcessing 通知任务开始处理
func (s *Service) NotifyTaskProcessing(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskProcessing, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task processing: TaskID=%d, UserID=%d", task.ID, task.UserID)
}

// NotifyTaskUploading 通知任务开始上传
func (s *Service) NotifyTaskUploading(task *model.DownloadTask) {
	msg := NewTaskEventMessage(MessageTypeTaskUploading, task)
	s.hub.SendToUser(task.UserID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified task uploading: TaskID=%d, UserID=%d", task.ID, task.UserID)
}

// NotifyFileProcessingProgress 通知文件处理进度
func (s *Service) NotifyFileProcessingProgress(userID uint, data *FileProcessingProgressData) {
	msg := NewFileProcessingProgressMessage(userID, data)
	s.hub.SendToUser(userID, msg)

	logger.Debugf("Notified processing progress: TaskID=%d, Stage=%s, Progress=%.2f%%",
		data.TaskID, data.Stage, data.Progress)
}

// NotifyChunkUploaded 通知分片上传完成
func (s *Service) NotifyChunkUploaded(userID uint, data *ChunkUploadData) {
	msg := NewChunkUploadMessage(userID, data)
	s.hub.SendToUser(userID, msg)

	logger.Debugf("Notified chunk uploaded: TaskID=%d, Chunk=%d/%d",
		data.TaskID, data.ChunkIndex+1, data.ChunkCount)
}

// NotifyPlaylistReady 通知播放列表就绪
func (s *Service) NotifyPlaylistReady(userID uint, data *PlaylistReadyData) {
	msg := NewPlaylistReadyMessage(userID, data)
	s.hub.SendToUser(userID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified playlist ready: TaskID=%d, UserID=%d, URL=%s",
		data.TaskID, userID, data.PlaylistURL)
}

// NotifyProcessingError 通知处理错误
func (s *Service) NotifyProcessingError(userID uint, taskID uint, taskName, errorMsg string) {
	msg := NewProcessingErrorMessage(userID, taskID, taskName, errorMsg)
	s.hub.SendToUser(userID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Errorf("Notified processing error: TaskID=%d, UserID=%d, Error=%s",
		taskID, userID, errorMsg)
}

// IsUserOnline 检查用户是否在线
func (s *Service) IsUserOnline(userID uint) bool {
	return s.hub.IsUserOnline(userID)
}

// GetOnlineUserCount 获取在线用户数量
func (s *Service) GetOnlineUserCount() int {
	return s.hub.GetOnlineUserCount()
}

// GetStats 获取统计信息
func (s *Service) GetStats() *HubStats {
	return s.hub.GetStats()
}

// GetClients 获取所有客户端信息
func (s *Service) GetClients() []map[string]interface{} {
	return s.hub.GetClients()
}

// notifyAdmins 通知所有管理员
func (s *Service) notifyAdmins(msg *Message) {
	clients := s.hub.GetClients()
	for _, clientInfo := range clients {
		if role, ok := clientInfo["role"].(model.UserRole); ok && role == model.UserRoleAdmin {
			if userID, ok := clientInfo["user_id"].(uint); ok {
				s.hub.SendToUser(userID, msg)
			}
		}
	}
}

// NotifyProcessingProgress 通知处理进度
func (s *Service) NotifyProcessingProgress(taskID uint, status string, message string) {
	msg := &Message{
		Type: "processing_progress",
		Data: map[string]interface{}{
			"task_id": taskID,
			"status":  status,
			"message": message,
			"timestamp": time.Now().Unix(),
		},
	}

	// 获取任务信息以确定用户ID
	var task model.DownloadTask
	if err := s.db.First(&task, taskID).Error; err != nil {
		logger.Errorf("Failed to get task for progress notification: %v", err)
		return
	}

	s.hub.SendToUser(task.UserID, msg)
	s.notifyAdmins(msg)

	logger.WithFields(map[string]interface{}{
		"task_id": taskID,
		"user_id": task.UserID,
		"status":  status,
		"message": message,
	}).Debug("Notified processing progress")
}



// NotifyDetailedProgress 通知详细进度信息
func (s *Service) NotifyDetailedProgress(userID uint, taskID uint, stage string, step string, details map[string]interface{}) {
	msg := &Message{
		Type: "detailed_progress",
		Data: map[string]interface{}{
			"task_id":   taskID,
			"stage":     stage,
			"step":      step,
			"details":   details,
			"timestamp": time.Now().Unix(),
		},
	}

	s.hub.SendToUser(userID, msg)

	logger.WithFields(map[string]interface{}{
		"task_id": taskID,
		"user_id": userID,
		"stage":   stage,
		"step":    step,
	}).Debug("Notified detailed progress")
}

// Close 关闭服务
func (s *Service) Close() {
	// 这里可以添加清理逻辑
	logger.Info("WebSocket service closed")
}
// NotifyMixFileCompleted 通知MixFile处理完成
func (s *Service) NotifyMixFileCompleted(userID uint, data *MixFileCompletedData) {
	msg := NewMixFileCompletedMessage(userID, data)
	s.hub.SendToUser(userID, msg)

	// 同时发送给管理员
	s.notifyAdmins(msg)

	logger.Infof("Notified MixFile completed: TaskID=%d, UserID=%d, ShareCode=%s", 
		data.TaskID, userID, data.ShareCode[:20]+"...")
}

// NotifyMixFileProgress 通知MixFile处理进度
func (s *Service) NotifyMixFileProgress(userID uint, data *MixFileProgressData) {
	msg := NewMixFileProgressMessage(userID, data)
	s.hub.SendToUser(userID, msg)

	logger.Debugf("Notified MixFile progress: TaskID=%d, Stage=%s, Progress=%.2f%%", 
		data.TaskID, data.Stage, data.Progress)
}