package websocket

import (
	"encoding/json"
	"fmt"
	"time"

	"magnet-downloader/internal/model"
)

// MessageType 消息类型
type MessageType string

const (
	// 任务相关消息
	MessageTypeTaskCreated   MessageType = "task_created"   // 任务创建
	MessageTypeTaskStarted   MessageType = "task_started"   // 任务开始
	MessageTypeTaskProgress  MessageType = "task_progress"  // 任务进度更新
	MessageTypeTaskPaused    MessageType = "task_paused"    // 任务暂停
	MessageTypeTaskResumed   MessageType = "task_resumed"   // 任务恢复
	MessageTypeTaskCompleted MessageType = "task_completed" // 任务完成
	MessageTypeTaskFailed    MessageType = "task_failed"    // 任务失败
	MessageTypeTaskCancelled MessageType = "task_cancelled" // 任务取消
	MessageTypeTaskDeleted   MessageType = "task_deleted"   // 任务删除

	// 文件处理相关消息
	MessageTypeTaskProcessing  MessageType = "task_processing"  // 文件处理中
	MessageTypeTaskUploading   MessageType = "task_uploading"   // 分片上传中
	MessageTypeChunkUploaded   MessageType = "chunk_uploaded"   // 分片上传完成
	MessageTypePlaylistReady   MessageType = "playlist_ready"   // 播放列表就绪
	MessageTypeProcessingError MessageType = "processing_error" // 处理错误

	// MixFile相关消息
	MessageTypeMixFileCompleted MessageType = "mixfile_completed" // MixFile处理完成
	MessageTypeMixFileProgress  MessageType = "mixfile_progress"  // MixFile处理进度

	// 系统相关消息
	MessageTypeSystemNotification MessageType = "system_notification" // 系统通知
	MessageTypeSystemMaintenance  MessageType = "system_maintenance"  // 系统维护
	MessageTypeSystemAlert        MessageType = "system_alert"        // 系统告警

	// 用户相关消息
	MessageTypeUserLogin  MessageType = "user_login"  // 用户登录
	MessageTypeUserLogout MessageType = "user_logout" // 用户登出

	// 连接相关消息
	MessageTypeConnected    MessageType = "connected"    // 连接成功
	MessageTypeDisconnected MessageType = "disconnected" // 连接断开
	MessageTypePing         MessageType = "ping"         // 心跳检测
	MessageTypePong         MessageType = "pong"         // 心跳响应
	MessageTypeError        MessageType = "error"        // 错误消息
)

// Message WebSocket消息
type Message struct {
	ID        string      `json:"id"`        // 消息ID
	Type      MessageType `json:"type"`      // 消息类型
	Data      interface{} `json:"data"`      // 消息数据
	UserID    uint        `json:"user_id"`   // 目标用户ID（0表示广播）
	Timestamp int64       `json:"timestamp"` // 时间戳
	Channel   string      `json:"channel"`   // 频道（可选）
}

// TaskProgressData 任务进度数据
type TaskProgressData struct {
	TaskID         uint    `json:"task_id"`
	TaskName       string  `json:"task_name"`
	Status         string  `json:"status"`
	Progress       float64 `json:"progress"`
	DownloadSpeed  int64   `json:"download_speed"`
	UploadSpeed    int64   `json:"upload_speed"`
	TotalSize      int64   `json:"total_size"`
	DownloadedSize int64   `json:"downloaded_size"`
	ETA            int64   `json:"eta"` // 预计剩余时间（秒）
	ErrorMessage   string  `json:"error_message,omitempty"`

	// 文件处理相关字段
	ProcessingStatus   string  `json:"processing_status,omitempty"`   // 处理状态
	ProcessingProgress float64 `json:"processing_progress,omitempty"` // 处理进度
	ChunkCount         int     `json:"chunk_count,omitempty"`         // 分片总数
	UploadedChunks     int     `json:"uploaded_chunks,omitempty"`     // 已上传分片数
	PlaylistURL        string  `json:"playlist_url,omitempty"`        // 播放列表URL
}

// TaskEventData 任务事件数据
type TaskEventData struct {
	TaskID       uint               `json:"task_id"`
	TaskName     string             `json:"task_name"`
	Status       model.TaskStatus   `json:"status"`
	Priority     model.TaskPriority `json:"priority"`
	UserID       uint               `json:"user_id"`
	MagnetURI    string             `json:"magnet_uri,omitempty"`
	SavePath     string             `json:"save_path,omitempty"`
	CreatedAt    time.Time          `json:"created_at,omitempty"`
	StartedAt    *time.Time         `json:"started_at,omitempty"`
	CompletedAt  *time.Time         `json:"completed_at,omitempty"`
	ErrorMessage string             `json:"error_message,omitempty"`
}

// SystemNotificationData 系统通知数据
type SystemNotificationData struct {
	Title    string `json:"title"`
	Message  string `json:"message"`
	Level    string `json:"level"`              // info, warning, error
	Duration int    `json:"duration,omitempty"` // 显示时长（毫秒）
}

// SystemAlertData 系统告警数据
type SystemAlertData struct {
	AlertID    string     `json:"alert_id"`
	Title      string     `json:"title"`
	Message    string     `json:"message"`
	Severity   string     `json:"severity"` // low, medium, high, critical
	Component  string     `json:"component"`
	Timestamp  time.Time  `json:"timestamp"`
	Resolved   bool       `json:"resolved"`
	ResolvedAt *time.Time `json:"resolved_at,omitempty"`
}

// UserEventData 用户事件数据
type UserEventData struct {
	UserID    uint      `json:"user_id"`
	Username  string    `json:"username"`
	Action    string    `json:"action"`
	Timestamp time.Time `json:"timestamp"`
	IP        string    `json:"ip,omitempty"`
}

// ErrorData 错误数据
type ErrorData struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// FileProcessingProgressData 文件处理进度数据
type FileProcessingProgressData struct {
	TaskID             uint    `json:"task_id"`
	TaskName           string  `json:"task_name"`
	Stage              string  `json:"stage"`               // 处理阶段: chunking, encrypting, uploading
	Progress           float64 `json:"progress"`            // 当前阶段进度
	OverallProgress    float64 `json:"overall_progress"`    // 总体进度
	ChunkCount         int     `json:"chunk_count"`         // 分片总数
	ProcessedChunks    int     `json:"processed_chunks"`    // 已处理分片数
	UploadedChunks     int     `json:"uploaded_chunks"`     // 已上传分片数
	CurrentChunk       int     `json:"current_chunk"`       // 当前处理分片
	Message            string  `json:"message"`             // 进度消息
	EstimatedRemaining int64   `json:"estimated_remaining"` // 预计剩余时间(秒)
}

// ChunkUploadData 分片上传数据
type ChunkUploadData struct {
	TaskID     uint    `json:"task_id"`
	TaskName   string  `json:"task_name"`
	ChunkIndex int     `json:"chunk_index"` // 分片索引
	ChunkCount int     `json:"chunk_count"` // 总分片数
	ChunkURL   string  `json:"chunk_url"`   // 分片URL
	UploadSize int64   `json:"upload_size"` // 上传大小
	Progress   float64 `json:"progress"`    // 上传进度
}

// PlaylistReadyData 播放列表就绪数据
type PlaylistReadyData struct {
	TaskID      uint    `json:"task_id"`
	TaskName    string  `json:"task_name"`
	PlaylistURL string  `json:"playlist_url"` // 播放列表URL
	ChunkCount  int     `json:"chunk_count"`  // 分片数量
	TotalSize   int64   `json:"total_size"`   // 总大小
	Duration    float64 `json:"duration"`     // 播放时长(秒)
	Encrypted   bool    `json:"encrypted"`    // 是否加密
}

// MixFileCompletedData MixFile处理完成数据
type MixFileCompletedData struct {
	TaskID      uint   `json:"task_id"`
	TaskName    string `json:"task_name"`
	ShareCode   string `json:"share_code"`   // 分享码
	IndexURL    string `json:"index_url"`    // 索引文件URL
	ChunkCount  int    `json:"chunk_count"`  // 分片数量
	TotalSize   int64  `json:"total_size"`   // 总大小
	Encrypted   bool   `json:"encrypted"`    // 是否加密
	CompletedAt int64  `json:"completed_at"` // 完成时间戳
}

// MixFileProgressData MixFile处理进度数据
type MixFileProgressData struct {
	TaskID          uint    `json:"task_id"`
	TaskName        string  `json:"task_name"`
	Stage           string  `json:"stage"`            // 处理阶段: generating_index, uploading_index, generating_share
	Progress        float64 `json:"progress"`         // 当前阶段进度
	OverallProgress float64 `json:"overall_progress"` // 总体进度
	Message         string  `json:"message"`          // 进度消息
	IndexGenerated  bool    `json:"index_generated"`  // 索引是否已生成
	IndexUploaded   bool    `json:"index_uploaded"`   // 索引是否已上传
	ShareGenerated  bool    `json:"share_generated"`  // 分享码是否已生成
}

// NewMessage 创建新消息
func NewMessage(msgType MessageType, data interface{}) *Message {
	return &Message{
		ID:        generateMessageID(),
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// NewUserMessage 创建用户消息
func NewUserMessage(msgType MessageType, userID uint, data interface{}) *Message {
	msg := NewMessage(msgType, data)
	msg.UserID = userID
	return msg
}

// NewChannelMessage 创建频道消息
func NewChannelMessage(msgType MessageType, channel string, data interface{}) *Message {
	msg := NewMessage(msgType, data)
	msg.Channel = channel
	return msg
}

// ToJSON 转换为JSON
func (m *Message) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// FromJSON 从JSON解析
func (m *Message) FromJSON(data []byte) error {
	return json.Unmarshal(data, m)
}

// IsBroadcast 是否为广播消息
func (m *Message) IsBroadcast() bool {
	return m.UserID == 0
}

// IsUserMessage 是否为用户消息
func (m *Message) IsUserMessage() bool {
	return m.UserID > 0
}

// IsChannelMessage 是否为频道消息
func (m *Message) IsChannelMessage() bool {
	return m.Channel != ""
}

// generateMessageID 生成消息ID
func generateMessageID() string {
	// 简单的消息ID生成，实际项目中可以使用UUID
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// TaskProgressFromModel 从模型创建任务进度数据
func TaskProgressFromModel(task *model.DownloadTask) *TaskProgressData {
	eta := int64(0)
	if task.DownloadSpeed > 0 && task.TotalSize > 0 {
		remaining := task.TotalSize - task.DownloadedSize
		eta = remaining / task.DownloadSpeed
	}

	return &TaskProgressData{
		TaskID:             task.ID,
		TaskName:           task.TaskName,
		Status:             string(task.Status),
		Progress:           task.Progress,
		DownloadSpeed:      task.DownloadSpeed,
		UploadSpeed:        task.UploadSpeed,
		TotalSize:          task.TotalSize,
		DownloadedSize:     task.DownloadedSize,
		ETA:                eta,
		ErrorMessage:       task.ErrorMessage,
		ProcessingStatus:   task.ProcessingStatus,
		ProcessingProgress: task.ProcessingProgress,
		ChunkCount:         task.ChunkCount,
		UploadedChunks:     task.UploadedChunks,
		PlaylistURL:        task.PlaylistURL,
	}
}

// TaskEventFromModel 从模型创建任务事件数据
func TaskEventFromModel(task *model.DownloadTask) *TaskEventData {
	return &TaskEventData{
		TaskID:       task.ID,
		TaskName:     task.TaskName,
		Status:       task.Status,
		Priority:     task.Priority,
		UserID:       task.UserID,
		MagnetURI:    task.MagnetURI,
		SavePath:     task.SavePath,
		CreatedAt:    task.CreatedAt,
		StartedAt:    task.StartedAt,
		CompletedAt:  task.CompletedAt,
		ErrorMessage: task.ErrorMessage,
	}
}

// NewTaskProgressMessage 创建任务进度消息
func NewTaskProgressMessage(task *model.DownloadTask) *Message {
	data := TaskProgressFromModel(task)
	return NewUserMessage(MessageTypeTaskProgress, task.UserID, data)
}

// NewTaskEventMessage 创建任务事件消息
func NewTaskEventMessage(msgType MessageType, task *model.DownloadTask) *Message {
	data := TaskEventFromModel(task)
	return NewUserMessage(msgType, task.UserID, data)
}

// NewSystemNotificationMessage 创建系统通知消息
func NewSystemNotificationMessage(title, message, level string) *Message {
	data := &SystemNotificationData{
		Title:   title,
		Message: message,
		Level:   level,
	}
	return NewMessage(MessageTypeSystemNotification, data)
}

// NewSystemAlertMessage 创建系统告警消息
func NewSystemAlertMessage(alertID, title, message, severity, component string) *Message {
	data := &SystemAlertData{
		AlertID:   alertID,
		Title:     title,
		Message:   message,
		Severity:  severity,
		Component: component,
		Timestamp: time.Now(),
		Resolved:  false,
	}
	return NewMessage(MessageTypeSystemAlert, data)
}

// NewUserEventMessage 创建用户事件消息
func NewUserEventMessage(msgType MessageType, userID uint, username, action, ip string) *Message {
	data := &UserEventData{
		UserID:    userID,
		Username:  username,
		Action:    action,
		Timestamp: time.Now(),
		IP:        ip,
	}
	return NewMessage(msgType, data)
}

// NewErrorMessage 创建错误消息
func NewErrorMessage(code int, message, details string) *Message {
	data := &ErrorData{
		Code:    code,
		Message: message,
		Details: details,
	}
	return NewMessage(MessageTypeError, data)
}

// NewFileProcessingProgressMessage 创建文件处理进度消息
func NewFileProcessingProgressMessage(userID uint, data *FileProcessingProgressData) *Message {
	return NewUserMessage(MessageTypeTaskProcessing, userID, data)
}

// NewChunkUploadMessage 创建分片上传消息
func NewChunkUploadMessage(userID uint, data *ChunkUploadData) *Message {
	return NewUserMessage(MessageTypeChunkUploaded, userID, data)
}

// NewPlaylistReadyMessage 创建播放列表就绪消息
func NewPlaylistReadyMessage(userID uint, data *PlaylistReadyData) *Message {
	return NewUserMessage(MessageTypePlaylistReady, userID, data)
}

// NewProcessingErrorMessage 创建处理错误消息
func NewProcessingErrorMessage(userID uint, taskID uint, taskName, errorMsg string) *Message {
	data := &ErrorData{
		Code:    500,
		Message: fmt.Sprintf("Task %s processing failed", taskName),
		Details: errorMsg,
	}
	return NewUserMessage(MessageTypeProcessingError, userID, data)
}

// NewMixFileCompletedMessage 创建MixFile完成消息
func NewMixFileCompletedMessage(userID uint, data *MixFileCompletedData) *Message {
	data.CompletedAt = time.Now().Unix()
	return NewUserMessage(MessageTypeMixFileCompleted, userID, data)
}

// NewMixFileProgressMessage 创建MixFile进度消息
func NewMixFileProgressMessage(userID uint, data *MixFileProgressData) *Message {
	return NewUserMessage(MessageTypeMixFileProgress, userID, data)
}
