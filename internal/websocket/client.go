package websocket

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/logger"

	"github.com/gorilla/websocket"
)

const (
	// 写入等待时间
	writeWait = 10 * time.Second

	// 读取等待时间
	pongWait = 60 * time.Second

	// ping周期，必须小于pongWait
	pingPeriod = (pongWait * 9) / 10

	// 最大消息大小
	maxMessageSize = 512
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 在生产环境中应该检查Origin
		return true
	},
}

// Client WebSocket客户端
type Client struct {
	// WebSocket连接
	conn *websocket.Conn

	// 消息发送通道
	send chan []byte

	// 客户端ID
	id string

	// 用户信息
	userID   uint
	username string
	role     model.UserRole

	// 订阅的频道
	channels map[string]bool
	mutex    sync.RWMutex

	// 连接时间
	connectedAt time.Time

	// 最后活跃时间
	lastActiveAt time.Time

	// Hub引用
	hub *Hub
}

// NewClient 创建新客户端
func NewClient(conn *websocket.Conn, userID uint, username string, role model.UserRole, hub *Hub) *Client {
	return &Client{
		conn:         conn,
		send:         make(chan []byte, 256),
		id:           generateClientID(),
		userID:       userID,
		username:     username,
		role:         role,
		channels:     make(map[string]bool),
		connectedAt:  time.Now(),
		lastActiveAt: time.Now(),
		hub:          hub,
	}
}

// readPump 处理从WebSocket连接读取消息
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		c.lastActiveAt = time.Now()
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logger.Errorf("WebSocket error: %v", err)
			}
			break
		}

		c.lastActiveAt = time.Now()
		c.handleMessage(message)
	}
}

// writePump 处理向WebSocket连接写入消息
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 批量发送队列中的消息
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理接收到的消息
func (c *Client) handleMessage(data []byte) {
	var msg Message
	if err := json.Unmarshal(data, &msg); err != nil {
		logger.Errorf("Failed to unmarshal message: %v", err)
		c.sendError(400, "Invalid message format", err.Error())
		return
	}

	switch msg.Type {
	case MessageTypePing:
		c.sendPong()
	case MessageTypeError:
		logger.Warnf("Client %s sent error message: %v", c.id, msg.Data)
	default:
		logger.Debugf("Received message from client %s: type=%s", c.id, msg.Type)
	}
}

// SendMessage 发送消息
func (c *Client) SendMessage(msg *Message) {
	data, err := msg.ToJSON()
	if err != nil {
		logger.Errorf("Failed to marshal message: %v", err)
		return
	}

	select {
	case c.send <- data:
	default:
		close(c.send)
	}
}

// sendPong 发送pong消息
func (c *Client) sendPong() {
	msg := NewMessage(MessageTypePong, nil)
	c.SendMessage(msg)
}

// sendError 发送错误消息
func (c *Client) sendError(code int, message, details string) {
	msg := NewErrorMessage(code, message, details)
	c.SendMessage(msg)
}

// SubscribeChannel 订阅频道
func (c *Client) SubscribeChannel(channel string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.channels[channel] = true
	logger.Debugf("Client %s subscribed to channel %s", c.id, channel)
}

// UnsubscribeChannel 取消订阅频道
func (c *Client) UnsubscribeChannel(channel string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	delete(c.channels, channel)
	logger.Debugf("Client %s unsubscribed from channel %s", c.id, channel)
}

// IsSubscribedToChannel 检查是否订阅了频道
func (c *Client) IsSubscribedToChannel(channel string) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.channels[channel]
}

// GetChannels 获取订阅的频道列表
func (c *Client) GetChannels() []string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	channels := make([]string, 0, len(c.channels))
	for channel := range c.channels {
		channels = append(channels, channel)
	}
	return channels
}

// CanReceiveMessage 检查是否可以接收消息
func (c *Client) CanReceiveMessage(msg *Message) bool {
	// 广播消息所有人都可以接收
	if msg.IsBroadcast() {
		return true
	}

	// 用户消息只有目标用户可以接收
	if msg.IsUserMessage() {
		return msg.UserID == c.userID
	}

	// 频道消息只有订阅了该频道的用户可以接收
	if msg.IsChannelMessage() {
		return c.IsSubscribedToChannel(msg.Channel)
	}

	return false
}

// IsAdmin 检查是否为管理员
func (c *Client) IsAdmin() bool {
	return c.role == model.UserRoleAdmin
}

// GetInfo 获取客户端信息
func (c *Client) GetInfo() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return map[string]interface{}{
		"id":             c.id,
		"user_id":        c.userID,
		"username":       c.username,
		"role":           c.role,
		"connected_at":   c.connectedAt,
		"last_active_at": c.lastActiveAt,
		"channels":       c.GetChannels(),
	}
}

// Close 关闭客户端连接
func (c *Client) Close() {
	close(c.send)
}

// generateClientID 生成客户端ID
func generateClientID() string {
	return fmt.Sprintf("client_%d", time.Now().UnixNano())
}

// ServeWS 处理WebSocket连接升级
func ServeWS(hub *Hub, w http.ResponseWriter, r *http.Request, userID uint, username string, role model.UserRole) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Println(err)
		return
	}

	client := NewClient(conn, userID, username, role, hub)
	client.hub.register <- client

	// 发送连接成功消息
	connectMsg := NewMessage(MessageTypeConnected, map[string]interface{}{
		"client_id": client.id,
		"user_id":   userID,
		"username":  username,
		"timestamp": time.Now().Unix(),
	})
	client.SendMessage(connectMsg)

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}
