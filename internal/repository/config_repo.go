package repository

import (
	"fmt"
	"sync"

	"magnet-downloader/internal/model"

	"gorm.io/gorm"
)

// configRepository 配置仓库实现
type configRepository struct {
	db    *gorm.DB
	cache map[string]interface{}
	mutex sync.RWMutex
}

// NewConfigRepository 创建配置仓库
func NewConfigRepository(db *gorm.DB) ConfigRepository {
	return &configRepository{
		db:    db,
		cache: make(map[string]interface{}),
	}
}

// Create 创建配置
func (r *configRepository) Create(config *model.SystemConfig) error {
	err := r.db.Create(config).Error
	if err == nil {
		r.SetCachedValue(config.Key, config.Value)
	}
	return err
}

// GetByID 根据ID获取配置
func (r *configRepository) GetByID(id uint) (*model.SystemConfig, error) {
	var config model.SystemConfig
	err := r.db.First(&config, id).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// Update 更新配置
func (r *configRepository) Update(config *model.SystemConfig) error {
	err := r.db.Save(config).Error
	if err == nil {
		r.SetCachedValue(config.Key, config.Value)
	}
	return err
}

// Delete 删除配置
func (r *configRepository) Delete(id uint) error {
	// 先获取配置以便清除缓存
	config, err := r.GetByID(id)
	if err != nil {
		return err
	}

	err = r.db.Delete(&model.SystemConfig{}, id).Error
	if err == nil {
		r.mutex.Lock()
		delete(r.cache, config.Key)
		r.mutex.Unlock()
	}
	return err
}

// List 获取配置列表
func (r *configRepository) List(offset, limit int, filters map[string]interface{}) ([]*model.SystemConfig, int64, error) {
	var configs []*model.SystemConfig
	var total int64

	query := r.db.Model(&model.SystemConfig{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "category":
			query = query.Where("category = ?", value)
		case "is_public":
			query = query.Where("is_public = ?", value)
		case "is_editable":
			query = query.Where("is_editable = ?", value)
		case "type":
			query = query.Where("type = ?", value)
		case "search":
			searchTerm := fmt.Sprintf("%%%s%%", value)
			query = query.Where("key ILIKE ? OR description ILIKE ?", searchTerm, searchTerm)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Order("category ASC, key ASC").Offset(offset).Limit(limit).Find(&configs).Error
	if err != nil {
		return nil, 0, err
	}

	return configs, total, nil
}

// GetByKey 根据键获取配置
func (r *configRepository) GetByKey(key string) (*model.SystemConfig, error) {
	var config model.SystemConfig
	err := r.db.Where("key = ?", key).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// GetByCategory 根据分类获取配置
func (r *configRepository) GetByCategory(category string) ([]*model.SystemConfig, error) {
	var configs []*model.SystemConfig
	err := r.db.Where("category = ?", category).Order("key ASC").Find(&configs).Error
	return configs, err
}

// GetPublicConfigs 获取公开配置
func (r *configRepository) GetPublicConfigs() ([]*model.SystemConfig, error) {
	var configs []*model.SystemConfig
	err := r.db.Where("is_public = ?", true).Order("category ASC, key ASC").Find(&configs).Error
	return configs, err
}

// BatchUpdate 批量更新配置
func (r *configRepository) BatchUpdate(configs []*model.SystemConfig) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		for _, config := range configs {
			if err := tx.Save(config).Error; err != nil {
				return err
			}
			r.SetCachedValue(config.Key, config.Value)
		}
		return nil
	})
}

// BatchDelete 批量删除配置
func (r *configRepository) BatchDelete(keys []string) error {
	err := r.db.Where("key IN ?", keys).Delete(&model.SystemConfig{}).Error
	if err == nil {
		r.mutex.Lock()
		for _, key := range keys {
			delete(r.cache, key)
		}
		r.mutex.Unlock()
	}
	return err
}

// GetCachedValue 获取缓存值
func (r *configRepository) GetCachedValue(key string) (interface{}, bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	value, exists := r.cache[key]
	return value, exists
}

// SetCachedValue 设置缓存值
func (r *configRepository) SetCachedValue(key string, value interface{}) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.cache[key] = value
}

// ClearCache 清除缓存
func (r *configRepository) ClearCache() {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.cache = make(map[string]interface{})
}
