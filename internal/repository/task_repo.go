package repository

import (
	"fmt"
	"time"

	"magnet-downloader/internal/model"

	"gorm.io/gorm"
)

// taskRepository 任务仓库实现
type taskRepository struct {
	db *gorm.DB
}

// NewTaskRepository 创建任务仓库
func NewTaskRepository(db *gorm.DB) TaskRepository {
	return &taskRepository{db: db}
}

// Create 创建任务
func (r *taskRepository) Create(task *model.DownloadTask) error {
	return r.db.Create(task).Error
}

// GetByID 根据ID获取任务
func (r *taskRepository) GetByID(id uint) (*model.DownloadTask, error) {
	var task model.DownloadTask
	err := r.db.Preload("User").First(&task, id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// Update 更新任务
func (r *taskRepository) Update(task *model.DownloadTask) error {
	return r.db.Save(task).Error
}

// Delete 删除任务
func (r *taskRepository) Delete(id uint) error {
	return r.db.Delete(&model.DownloadTask{}, id).Error
}

// List 获取任务列表
func (r *taskRepository) List(offset, limit int, filters map[string]interface{}) ([]*model.DownloadTask, int64, error) {
	var tasks []*model.DownloadTask
	var total int64

	query := r.db.Model(&model.DownloadTask{}).Preload("User")

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "status":
			query = query.Where("status = ?", value)
		case "user_id":
			query = query.Where("user_id = ?", value)
		case "priority":
			query = query.Where("priority = ?", value)
		case "created_after":
			query = query.Where("created_at >= ?", value)
		case "created_before":
			query = query.Where("created_at <= ?", value)
		case "search":
			searchTerm := fmt.Sprintf("%%%s%%", value)
			query = query.Where("task_name ILIKE ? OR magnet_uri ILIKE ?", searchTerm, searchTerm)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&tasks).Error
	if err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// GetByUserID 根据用户ID获取任务
func (r *taskRepository) GetByUserID(userID uint, offset, limit int) ([]*model.DownloadTask, int64, error) {
	var tasks []*model.DownloadTask
	var total int64

	query := r.db.Model(&model.DownloadTask{}).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&tasks).Error
	if err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// GetByStatus 根据状态获取任务
func (r *taskRepository) GetByStatus(status model.TaskStatus, offset, limit int) ([]*model.DownloadTask, int64, error) {
	var tasks []*model.DownloadTask
	var total int64

	query := r.db.Model(&model.DownloadTask{}).Where("status = ?", status).Preload("User")

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Order("priority DESC, created_at ASC").Offset(offset).Limit(limit).Find(&tasks).Error
	if err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// GetByAria2GID 根据aria2 GID获取任务
func (r *taskRepository) GetByAria2GID(gid string) (*model.DownloadTask, error) {
	var task model.DownloadTask
	err := r.db.Where("aria2_gid = ?", gid).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// CountByStatus 根据状态统计任务数量
func (r *taskRepository) CountByStatus(status model.TaskStatus) (int64, error) {
	var count int64
	err := r.db.Model(&model.DownloadTask{}).Where("status = ?", status).Count(&count).Error
	return count, err
}

// CountByUserID 根据用户ID统计任务数量
func (r *taskRepository) CountByUserID(userID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.DownloadTask{}).Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

// GetActiveTasksByUser 获取用户的活跃任务
func (r *taskRepository) GetActiveTasksByUser(userID uint) ([]*model.DownloadTask, error) {
	var tasks []*model.DownloadTask
	err := r.db.Where("user_id = ? AND status IN ?", userID, []model.TaskStatus{
		model.TaskStatusPending,
		model.TaskStatusRunning,
	}).Find(&tasks).Error
	return tasks, err
}

// BatchUpdateStatus 批量更新状态
func (r *taskRepository) BatchUpdateStatus(ids []uint, status model.TaskStatus) error {
	return r.db.Model(&model.DownloadTask{}).Where("id IN ?", ids).Update("status", status).Error
}

// BatchDelete 批量删除
func (r *taskRepository) BatchDelete(ids []uint) error {
	return r.db.Delete(&model.DownloadTask{}, ids).Error
}

// CleanCompletedTasks 清理已完成的任务
func (r *taskRepository) CleanCompletedTasks(days int) error {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	return r.db.Where("status = ? AND completed_at < ?", model.TaskStatusCompleted, cutoffTime).
		Delete(&model.DownloadTask{}).Error
}

// GetByStatusAndTime 根据状态和时间查询任务
func (r *taskRepository) GetByStatusAndTime(status model.TaskStatus, before time.Time) ([]*model.DownloadTask, error) {
	var tasks []*model.DownloadTask
	err := r.db.Where("status = ? AND created_at < ?", status, before).Find(&tasks).Error
	return tasks, err
}
