package repository

import (
	"fmt"
	"time"

	"magnet-downloader/internal/model"

	"gorm.io/gorm"
)

// userRepository 用户仓库实现
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓库
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

// Create 创建用户
func (r *userRepository) Create(user *model.User) error {
	return r.db.Create(user).Error
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(id uint) (*model.User, error) {
	var user model.User
	err := r.db.First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// Update 更新用户
func (r *userRepository) Update(user *model.User) error {
	return r.db.Save(user).Error
}

// Delete 删除用户
func (r *userRepository) Delete(id uint) error {
	return r.db.Delete(&model.User{}, id).Error
}

// List 获取用户列表
func (r *userRepository) List(offset, limit int, filters map[string]interface{}) ([]*model.User, int64, error) {
	var users []*model.User
	var total int64

	query := r.db.Model(&model.User{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "role":
			query = query.Where("role = ?", value)
		case "status":
			query = query.Where("status = ?", value)
		case "created_after":
			query = query.Where("created_at >= ?", value)
		case "created_before":
			query = query.Where("created_at <= ?", value)
		case "search":
			searchTerm := fmt.Sprintf("%%%s%%", value)
			query = query.Where("username ILIKE ? OR email ILIKE ?", searchTerm, searchTerm)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// GetByUsername 根据用户名获取用户
func (r *userRepository) GetByUsername(username string) (*model.User, error) {
	var user model.User
	err := r.db.Where("username = ?", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *userRepository) GetByEmail(email string) (*model.User, error) {
	var user model.User
	err := r.db.Where("email = ?", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// Authenticate 用户认证
func (r *userRepository) Authenticate(username, password string) (*model.User, error) {
	user, err := r.GetByUsername(username)
	if err != nil {
		return nil, err
	}

	if !user.CheckPassword(password) {
		return nil, fmt.Errorf("invalid password")
	}

	if !user.IsActive() {
		return nil, fmt.Errorf("user account is not active")
	}

	return user, nil
}

// UpdateLoginInfo 更新登录信息
func (r *userRepository) UpdateLoginInfo(userID uint) error {
	now := time.Now()
	return r.db.Model(&model.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"last_login_at": now,
		"login_count":   gorm.Expr("login_count + 1"),
	}).Error
}

// CountByRole 根据角色统计用户数量
func (r *userRepository) CountByRole(role model.UserRole) (int64, error) {
	var count int64
	err := r.db.Model(&model.User{}).Where("role = ?", role).Count(&count).Error
	return count, err
}

// CountByStatus 根据状态统计用户数量
func (r *userRepository) CountByStatus(status model.UserStatus) (int64, error) {
	var count int64
	err := r.db.Model(&model.User{}).Where("status = ?", status).Count(&count).Error
	return count, err
}

// BatchUpdateStatus 批量更新状态
func (r *userRepository) BatchUpdateStatus(ids []uint, status model.UserStatus) error {
	return r.db.Model(&model.User{}).Where("id IN ?", ids).Update("status", status).Error
}
