package repository

import (
	"magnet-downloader/internal/model"
	"time"

	"gorm.io/gorm"
)

// TaskRepository 任务仓库接口
type TaskRepository interface {
	// 基础CRUD操作
	Create(task *model.DownloadTask) error
	GetByID(id uint) (*model.DownloadTask, error)
	Update(task *model.DownloadTask) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.DownloadTask, int64, error)
	GetByUserID(userID uint, offset, limit int) ([]*model.DownloadTask, int64, error)
	GetByStatus(status model.TaskStatus, offset, limit int) ([]*model.DownloadTask, int64, error)
	GetByAria2GID(gid string) (*model.DownloadTask, error)

	// 统计操作
	CountByStatus(status model.TaskStatus) (int64, error)
	CountByUserID(userID uint) (int64, error)
	GetActiveTasksByUser(userID uint) ([]*model.DownloadTask, error)

	// 批量操作
	BatchUpdateStatus(ids []uint, status model.TaskStatus) error
	BatchDelete(ids []uint) error

	// 清理操作
	CleanCompletedTasks(days int) error
	GetByStatusAndTime(status model.TaskStatus, before time.Time) ([]*model.DownloadTask, error)
}

// UserRepository 用户仓库接口
type UserRepository interface {
	// 基础CRUD操作
	Create(user *model.User) error
	GetByID(id uint) (*model.User, error)
	Update(user *model.User) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.User, int64, error)
	GetByUsername(username string) (*model.User, error)
	GetByEmail(email string) (*model.User, error)

	// 认证相关
	Authenticate(username, password string) (*model.User, error)
	UpdateLoginInfo(userID uint) error

	// 统计操作
	CountByRole(role model.UserRole) (int64, error)
	CountByStatus(status model.UserStatus) (int64, error)

	// 批量操作
	BatchUpdateStatus(ids []uint, status model.UserStatus) error
}

// ConfigRepository 配置仓库接口
type ConfigRepository interface {
	// 基础CRUD操作
	Create(config *model.SystemConfig) error
	GetByID(id uint) (*model.SystemConfig, error)
	Update(config *model.SystemConfig) error
	Delete(id uint) error

	// 查询操作
	List(offset, limit int, filters map[string]interface{}) ([]*model.SystemConfig, int64, error)
	GetByKey(key string) (*model.SystemConfig, error)
	GetByCategory(category string) ([]*model.SystemConfig, error)
	GetPublicConfigs() ([]*model.SystemConfig, error)

	// 批量操作
	BatchUpdate(configs []*model.SystemConfig) error
	BatchDelete(keys []string) error

	// 缓存操作
	GetCachedValue(key string) (interface{}, bool)
	SetCachedValue(key string, value interface{})
	ClearCache()
}

// Repository 仓库集合接口
type Repository interface {
	Task() TaskRepository
	User() UserRepository
	Config() ConfigRepository

	// 事务支持
	Transaction(fn func(Repository) error) error

	// 健康检查
	Health() bool

	// 获取数据库实例
	GetDB() *gorm.DB
}
