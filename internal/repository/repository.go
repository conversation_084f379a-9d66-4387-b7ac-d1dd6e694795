package repository

import (
	"gorm.io/gorm"
)

// repository 仓库管理器实现
type repository struct {
	db         *gorm.DB
	taskRepo   TaskRepository
	userRepo   UserRepository
	configRepo ConfigRepository
}

// NewRepository 创建仓库管理器
func NewRepository(db *gorm.DB) Repository {
	return &repository{
		db:         db,
		taskRepo:   NewTaskRepository(db),
		userRepo:   NewUserRepository(db),
		configRepo: NewConfigRepository(db),
	}
}

// Task 获取任务仓库
func (r *repository) Task() TaskRepository {
	return r.taskRepo
}

// User 获取用户仓库
func (r *repository) User() UserRepository {
	return r.userRepo
}

// Config 获取配置仓库
func (r *repository) Config() ConfigRepository {
	return r.configRepo
}

// Transaction 执行事务
func (r *repository) Transaction(fn func(Repository) error) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 创建事务仓库
		txRepo := &repository{
			db:         tx,
			taskRepo:   NewTaskRepository(tx),
			userRepo:   NewUserRepository(tx),
			configRepo: NewConfigRepository(tx),
		}
		return fn(txRepo)
	})
}

// Health 健康检查
func (r *repository) Health() bool {
	sqlDB, err := r.db.DB()
	if err != nil {
		return false
	}
	return sqlDB.Ping() == nil
}

// GetDB 获取数据库实例
func (r *repository) GetDB() *gorm.DB {
	return r.db
}
