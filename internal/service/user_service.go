package service

import (
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/pkg/auth"
	"magnet-downloader/pkg/logger"
)

// userService 用户服务实现
type userService struct {
	repo      repository.Repository
	jwtSecret string
}

// NewUserService 创建用户服务
func NewUserService(repo repository.Repository, jwtSecret string) UserService {
	return &userService{
		repo:      repo,
		jwtSecret: jwtSecret,
	}
}

// Login 用户登录
func (us *userService) Login(req *LoginRequest) (*LoginResponse, error) {
	// 验证用户凭据
	user, err := us.repo.User().Authenticate(req.Username, req.Password)
	if err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	// 更新登录信息
	if err := us.repo.User().UpdateLoginInfo(user.ID); err != nil {
		logger.Warnf("Failed to update login info for user %d: %v", user.ID, err)
	}

	// 生成JWT令牌
	accessToken, err := auth.GenerateToken(user.ID, user.Username, user.Role, us.jwtSecret, time.Hour*24)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := auth.GenerateToken(user.ID, user.Username, user.Role, us.jwtSecret, time.Hour*24*7)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	response := &LoginResponse{
		User:         user.ToProfile(),
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(time.Hour * 24 / time.Second),
	}

	logger.Infof("User logged in: ID=%d, Username=%s", user.ID, user.Username)
	return response, nil
}

// Logout 用户登出
func (us *userService) Logout(userID uint) error {
	// 这里可以实现令牌黑名单或会话清理
	logger.Infof("User logged out: ID=%d", userID)
	return nil
}

// RefreshToken 刷新令牌
func (us *userService) RefreshToken(refreshToken string) (*TokenResponse, error) {
	// 验证刷新令牌
	claims, err := auth.ValidateToken(refreshToken, us.jwtSecret)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// 获取用户信息
	user, err := us.repo.User().GetByID(claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	if !user.IsActive() {
		return nil, fmt.Errorf("user account is not active")
	}

	// 生成新的访问令牌
	accessToken, err := auth.GenerateToken(user.ID, user.Username, user.Role, us.jwtSecret, time.Hour*24)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	newRefreshToken, err := auth.GenerateToken(user.ID, user.Username, user.Role, us.jwtSecret, time.Hour*24*7)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	response := &TokenResponse{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresIn:    int64(time.Hour * 24 / time.Second),
	}

	return response, nil
}

// CreateUser 创建用户
func (us *userService) CreateUser(req *CreateUserRequest) (*model.User, error) {
	// 检查用户名是否已存在
	if _, err := us.repo.User().GetByUsername(req.Username); err == nil {
		return nil, fmt.Errorf("username already exists")
	}

	// 检查邮箱是否已存在
	if req.Email != "" {
		if _, err := us.repo.User().GetByEmail(req.Email); err == nil {
			return nil, fmt.Errorf("email already exists")
		}
	}

	// 创建用户
	user := &model.User{
		Username: req.Username,
		Email:    req.Email,
		Role:     req.Role,
		Status:   req.Status,
	}

	// 设置默认值
	if user.Role == "" {
		user.Role = model.UserRoleUser
	}
	if user.Status == "" {
		user.Status = model.UserStatusActive
	}

	// 设置密码
	if err := user.SetPassword(req.Password); err != nil {
		return nil, fmt.Errorf("failed to set password: %w", err)
	}

	// 保存到数据库
	if err := us.repo.User().Create(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	logger.Infof("Created user: ID=%d, Username=%s", user.ID, user.Username)
	return user, nil
}

// GetUser 获取用户
func (us *userService) GetUser(userID uint) (*model.User, error) {
	user, err := us.repo.User().GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}
	return user, nil
}

// UpdateUser 更新用户
func (us *userService) UpdateUser(userID uint, req *UpdateUserRequest) (*model.User, error) {
	user, err := us.repo.User().GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// 更新字段
	if req.Email != "" && req.Email != user.Email {
		// 检查邮箱是否已被其他用户使用
		if existingUser, err := us.repo.User().GetByEmail(req.Email); err == nil && existingUser.ID != userID {
			return nil, fmt.Errorf("email already exists")
		}
		user.Email = req.Email
	}

	if req.Role != "" {
		user.Role = req.Role
	}
	if req.Status != "" {
		user.Status = req.Status
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}

	if err := us.repo.User().Update(user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	logger.Infof("Updated user: ID=%d", userID)
	return user, nil
}

// DeleteUser 删除用户
func (us *userService) DeleteUser(userID uint) error {
	user, err := us.repo.User().GetByID(userID)
	if err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	// 检查是否为管理员
	if user.IsAdmin() {
		adminCount, err := us.repo.User().CountByRole(model.UserRoleAdmin)
		if err != nil {
			return fmt.Errorf("failed to count admin users: %w", err)
		}
		if adminCount <= 1 {
			return fmt.Errorf("cannot delete the last admin user")
		}
	}

	if err := us.repo.User().Delete(userID); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	logger.Infof("Deleted user: ID=%d", userID)
	return nil
}

// ListUsers 获取用户列表
func (us *userService) ListUsers(req *ListUsersRequest) ([]*model.User, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize
	filters := us.buildUserFilters(req)

	users, total, err := us.repo.User().List(offset, req.PageSize, filters)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", err)
	}

	return users, total, nil
}

// GetUserByUsername 根据用户名获取用户
func (us *userService) GetUserByUsername(username string) (*model.User, error) {
	user, err := us.repo.User().GetByUsername(username)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}
	return user, nil
}

// GetUserByEmail 根据邮箱获取用户
func (us *userService) GetUserByEmail(email string) (*model.User, error) {
	user, err := us.repo.User().GetByEmail(email)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}
	return user, nil
}

// ChangePassword 修改密码
func (us *userService) ChangePassword(userID uint, req *ChangePasswordRequest) error {
	user, err := us.repo.User().GetByID(userID)
	if err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	// 验证旧密码
	if !user.CheckPassword(req.OldPassword) {
		return fmt.Errorf("old password is incorrect")
	}

	// 设置新密码
	if err := user.SetPassword(req.NewPassword); err != nil {
		return fmt.Errorf("failed to set new password: %w", err)
	}

	if err := us.repo.User().Update(user); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	logger.Infof("Password changed for user: ID=%d", userID)
	return nil
}

// ResetPassword 重置密码
func (us *userService) ResetPassword(email string) error {
	user, err := us.repo.User().GetByEmail(email)
	if err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	// 这里应该生成重置令牌并发送邮件
	// 暂时简化处理
	logger.Infof("Password reset requested for user: ID=%d, Email=%s", user.ID, email)
	return nil
}

// CheckPermission 检查权限
func (us *userService) CheckPermission(userID uint, permission string) (bool, error) {
	user, err := us.repo.User().GetByID(userID)
	if err != nil {
		return false, fmt.Errorf("user not found: %w", err)
	}

	// 管理员拥有所有权限
	if user.IsAdmin() {
		return true, nil
	}

	// 这里可以实现更复杂的权限检查逻辑
	switch permission {
	case "download":
		return user.CanDownload(), nil
	case "upload":
		return user.IsActive(), nil
	default:
		return false, nil
	}
}

// UpdateUserRole 更新用户角色
func (us *userService) UpdateUserRole(userID uint, role model.UserRole) error {
	user, err := us.repo.User().GetByID(userID)
	if err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	user.Role = role
	if err := us.repo.User().Update(user); err != nil {
		return fmt.Errorf("failed to update user role: %w", err)
	}

	logger.Infof("Updated user role: ID=%d, Role=%s", userID, role)
	return nil
}

// UpdateUserStatus 更新用户状态
func (us *userService) UpdateUserStatus(userID uint, status model.UserStatus) error {
	user, err := us.repo.User().GetByID(userID)
	if err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	user.Status = status
	if err := us.repo.User().Update(user); err != nil {
		return fmt.Errorf("failed to update user status: %w", err)
	}

	logger.Infof("Updated user status: ID=%d, Status=%s", userID, status)
	return nil
}

// GetUserStats 获取用户统计
func (us *userService) GetUserStats() (*UserStats, error) {
	stats := &UserStats{}

	// 获取各角色用户数量
	if count, err := us.repo.User().CountByRole(model.UserRoleUser); err == nil {
		stats.TotalUsers += count
	}
	if count, err := us.repo.User().CountByRole(model.UserRoleAdmin); err == nil {
		stats.AdminUsers = count
		stats.TotalUsers += count
	}

	// 获取各状态用户数量
	if count, err := us.repo.User().CountByStatus(model.UserStatusActive); err == nil {
		stats.ActiveUsers = count
	}
	if count, err := us.repo.User().CountByStatus(model.UserStatusBanned); err == nil {
		stats.BannedUsers = count
	}

	return stats, nil
}

// 辅助方法

// buildUserFilters 构建用户查询过滤器
func (us *userService) buildUserFilters(req *ListUsersRequest) map[string]interface{} {
	filters := make(map[string]interface{})

	if req.Role != "" {
		filters["role"] = req.Role
	}
	if req.Status != "" {
		filters["status"] = req.Status
	}
	if req.Search != "" {
		filters["search"] = req.Search
	}

	// 添加自定义过滤器
	for key, value := range req.Filters {
		filters[key] = value
	}

	return filters
}
