package service

import (
	"fmt"
	"path/filepath"
	"strconv"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/aria2"
	"magnet-downloader/pkg/logger"
)

// Aria2Service aria2服务接口
type Aria2Service interface {
	// 连接管理
	Connect() error
	Disconnect() error
	Ping() error
	GetVersion() (*aria2.Version, error)
	GetGlobalStats() (*aria2.GlobalStat, error)

	// 下载任务管理
	AddMagnetTask(magnetURI string, options *aria2.AddUriOptions) (string, error)
	RemoveTask(gid string, force bool) error
	PauseTask(gid string) error
	ResumeTask(gid string) error

	// 状态查询
	GetTaskStatus(gid string) (*aria2.DownloadInfo, error)
	GetActiveDownloads() ([]*aria2.DownloadInfo, error)
	GetWaitingDownloads(offset, limit int) ([]*aria2.DownloadInfo, error)
	GetStoppedDownloads(offset, limit int) ([]*aria2.DownloadInfo, error)
	GetActualDownloadedFiles(gid string) ([]model.ActualFileInfo, error)

	// 选项管理
	ChangeTaskOptions(gid string, options *aria2.ChangeOptions) error

	// 任务同步
	SyncTaskStatus(task *model.DownloadTask) error
	SyncAllTasks() error
}

// aria2Service aria2服务实现
type aria2Service struct {
	client *aria2.Client
	config *config.Aria2Config
}

// NewAria2Service 创建aria2服务
func NewAria2Service(cfg *config.Aria2Config) Aria2Service {
	clientConfig := &aria2.ClientConfig{
		Host:    cfg.Host,
		Port:    cfg.Port,
		Secret:  cfg.Secret,
		Timeout: 30 * time.Second,
		Secure:  false,
	}

	return &aria2Service{
		client: aria2.NewClient(clientConfig),
		config: cfg,
	}
}

// Connect 连接到aria2
func (as *aria2Service) Connect() error {
	if err := as.client.Ping(); err != nil {
		return fmt.Errorf("failed to connect to aria2: %w", err)
	}

	version, err := as.client.GetVersion()
	if err != nil {
		return fmt.Errorf("failed to get aria2 version: %w", err)
	}

	logger.Infof("Connected to aria2 version %s", version.Version)
	return nil
}

// Disconnect 断开连接
func (as *aria2Service) Disconnect() error {
	// aria2客户端使用HTTP连接，无需显式断开
	logger.Info("Disconnected from aria2")
	return nil
}

// Ping 检查连接状态
func (as *aria2Service) Ping() error {
	return as.client.Ping()
}

// GetVersion 获取版本信息
func (as *aria2Service) GetVersion() (*aria2.Version, error) {
	return as.client.GetVersion()
}

// GetGlobalStats 获取全局统计信息
func (as *aria2Service) GetGlobalStats() (*aria2.GlobalStat, error) {
	return as.client.GetGlobalStat()
}

// AddMagnetTask 添加磁力链接下载任务
func (as *aria2Service) AddMagnetTask(magnetURI string, options *aria2.AddUriOptions) (string, error) {
	// 如果没有提供选项，使用默认选项
	if options == nil {
		options = aria2.DefaultOptions
	}

	gid, err := as.client.AddMagnet(magnetURI, options)
	if err != nil {
		return "", fmt.Errorf("failed to add magnet task: %w", err)
	}

	logger.Infof("Added magnet task: GID=%s, URI=%s", gid, magnetURI)
	return gid, nil
}

// RemoveTask 移除下载任务
func (as *aria2Service) RemoveTask(gid string, force bool) error {
	var err error
	if force {
		_, err = as.client.ForceRemove(gid)
	} else {
		_, err = as.client.Remove(gid)
	}

	if err != nil {
		return fmt.Errorf("failed to remove task %s: %w", gid, err)
	}

	logger.Infof("Removed task: GID=%s, Force=%v", gid, force)
	return nil
}

// PauseTask 暂停下载任务
func (as *aria2Service) PauseTask(gid string) error {
	_, err := as.client.Pause(gid)
	if err != nil {
		return fmt.Errorf("failed to pause task %s: %w", gid, err)
	}

	logger.Infof("Paused task: GID=%s", gid)
	return nil
}

// ResumeTask 恢复下载任务
func (as *aria2Service) ResumeTask(gid string) error {
	_, err := as.client.Unpause(gid)
	if err != nil {
		return fmt.Errorf("failed to resume task %s: %w", gid, err)
	}

	logger.Infof("Resumed task: GID=%s", gid)
	return nil
}

// GetTaskStatus 获取任务状态
func (as *aria2Service) GetTaskStatus(gid string) (*aria2.DownloadInfo, error) {
	info, err := as.client.TellStatus(gid)
	if err != nil {
		return nil, fmt.Errorf("failed to get task status %s: %w", gid, err)
	}

	return info, nil
}

// GetActualDownloadedFiles 获取实际下载的文件列表
func (as *aria2Service) GetActualDownloadedFiles(gid string) ([]model.ActualFileInfo, error) {
	info, err := as.GetTaskStatus(gid)
	if err != nil {
		return nil, fmt.Errorf("failed to get task status: %w", err)
	}

	var actualFiles []model.ActualFileInfo
	
	for _, file := range info.Files {
		// 解析文件索引
		index := 0
		if idx, err := strconv.Atoi(file.Index); err == nil {
			index = idx
		}

		// 解析文件大小
		size := int64(0)
		if s, err := strconv.ParseInt(file.Length, 10, 64); err == nil {
			size = s
		}

		// 解析已完成大小
		completedLength := int64(0)
		if cl, err := strconv.ParseInt(file.CompletedLength, 10, 64); err == nil {
			completedLength = cl
		}

		// 解析是否选中
		selected := file.Selected == "true"

		// 提取文件名
		fileName := filepath.Base(file.Path)

		actualFile := model.ActualFileInfo{
			Index:           index,
			Path:            file.Path,
			Name:            fileName,
			Size:            size,
			CompletedLength: completedLength,
			Selected:        selected,
		}

		actualFiles = append(actualFiles, actualFile)
		
		logger.Debugf("Found file: index=%d, path=%s, name=%s, size=%d, selected=%v", 
			index, file.Path, fileName, size, selected)
	}

	logger.Infof("Retrieved %d files for GID %s", len(actualFiles), gid)
	return actualFiles, nil
}

// GetActiveDownloads 获取活跃下载列表
func (as *aria2Service) GetActiveDownloads() ([]*aria2.DownloadInfo, error) {
	downloads, err := as.client.TellActive()
	if err != nil {
		return nil, fmt.Errorf("failed to get active downloads: %w", err)
	}

	return downloads, nil
}

// GetWaitingDownloads 获取等待下载列表
func (as *aria2Service) GetWaitingDownloads(offset, limit int) ([]*aria2.DownloadInfo, error) {
	downloads, err := as.client.TellWaiting(offset, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get waiting downloads: %w", err)
	}

	return downloads, nil
}

// GetStoppedDownloads 获取已停止下载列表
func (as *aria2Service) GetStoppedDownloads(offset, limit int) ([]*aria2.DownloadInfo, error) {
	downloads, err := as.client.TellStopped(offset, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get stopped downloads: %w", err)
	}

	return downloads, nil
}

// ChangeTaskOptions 修改任务选项
func (as *aria2Service) ChangeTaskOptions(gid string, options *aria2.ChangeOptions) error {
	_, err := as.client.ChangeOption(gid, options)
	if err != nil {
		return fmt.Errorf("failed to change task options %s: %w", gid, err)
	}

	logger.Infof("Changed task options: GID=%s", gid)
	return nil
}

// SyncTaskStatus 同步任务状态
func (as *aria2Service) SyncTaskStatus(task *model.DownloadTask) error {
	if task.Aria2GID == "" {
		return fmt.Errorf("task has no aria2 GID")
	}

	info, err := as.GetTaskStatus(task.Aria2GID)
	if err != nil {
		return fmt.Errorf("failed to get aria2 status: %w", err)
	}

	// 同步状态
	task.Status = as.convertAria2Status(info.Status)

	// 同步进度
	if totalLength, err := strconv.ParseInt(info.TotalLength, 10, 64); err == nil && totalLength > 0 {
		task.TotalSize = totalLength
		if completedLength, err := strconv.ParseInt(info.CompletedLength, 10, 64); err == nil {
			task.DownloadedSize = completedLength
			task.Progress = float64(completedLength) / float64(totalLength) * 100
		}
	}

	// 同步速度
	if downloadSpeed, err := strconv.ParseInt(info.DownloadSpeed, 10, 64); err == nil {
		task.DownloadSpeed = downloadSpeed
	}
	if uploadSpeed, err := strconv.ParseInt(info.UploadSpeed, 10, 64); err == nil {
		task.UploadSpeed = uploadSpeed
	}

	// 同步错误信息
	if info.HasError() {
		task.ErrorMessage = info.ErrorMessage
	}

	// 设置完成时间
	if info.IsCompleted() && task.CompletedAt == nil {
		now := time.Now()
		task.CompletedAt = &now
	}

	logger.Debugf("Synced task status: ID=%d, Status=%s, Progress=%.2f%%",
		task.ID, task.Status, task.Progress)

	return nil
}

// SyncAllTasks 同步所有任务状态
func (as *aria2Service) SyncAllTasks() error {
	// 获取所有活跃下载
	activeDownloads, err := as.GetActiveDownloads()
	if err != nil {
		return fmt.Errorf("failed to get active downloads: %w", err)
	}

	logger.Infof("Syncing %d active downloads", len(activeDownloads))

	// 这里应该从数据库获取对应的任务并更新状态
	// 由于没有repository依赖，暂时只记录日志
	for _, download := range activeDownloads {
		logger.Debugf("Active download: GID=%s, Status=%s, Progress=%.2f%%",
			download.GID, download.Status, download.GetProgress())
	}

	return nil
}

// convertAria2Status 转换aria2状态到内部状态
func (as *aria2Service) convertAria2Status(aria2Status aria2.DownloadStatus) model.TaskStatus {
	switch aria2Status {
	case aria2.StatusActive:
		return model.TaskStatusRunning
	case aria2.StatusWaiting:
		return model.TaskStatusPending
	case aria2.StatusPaused:
		return model.TaskStatusPaused
	case aria2.StatusComplete:
		return model.TaskStatusCompleted
	case aria2.StatusError:
		return model.TaskStatusFailed
	case aria2.StatusRemoved:
		return model.TaskStatusCancelled
	default:
		return model.TaskStatusPending
	}
}

// CreateDownloadOptions 创建下载选项
func (as *aria2Service) CreateDownloadOptions(task *model.DownloadTask) *aria2.AddUriOptions {
	options := &aria2.AddUriOptions{
		Dir:                    task.SavePath,
		MaxConnections:         "16",
		Split:                  "16",
		MinSplitSize:           "1M",
		MaxConcurrentDownloads: "5",
		ContinueDownload:       "true",
		CheckIntegrity:         "true",
		AllowOverwrite:         "false",
		AutoFileRenaming:       "true",
		UserAgent:              "magnet-downloader/1.0",
	}

	// 如果任务名称不为空，设置输出文件名
	if task.TaskName != "" {
		options.Out = task.TaskName
	}

	return options
}
