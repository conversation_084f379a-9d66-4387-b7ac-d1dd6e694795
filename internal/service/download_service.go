package service

import (
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/pkg/logger"
)

// DownloadService 下载服务接口
type DownloadService interface {
	// 任务管理
	CreateDownloadTask(userID uint, magnetURI, taskName, savePath string) (*model.DownloadTask, error)
	StartDownload(taskID uint) error
	PauseDownload(taskID uint) error
	ResumeDownload(taskID uint) error
	CancelDownload(taskID uint) error
	RetryDownload(taskID uint) error

	// 任务查询
	GetDownloadTask(taskID uint) (*model.DownloadTask, error)
	GetUserDownloads(userID uint, offset, limit int) ([]*model.DownloadTask, int64, error)
	GetDownloadsByStatus(status model.TaskStatus, offset, limit int) ([]*model.DownloadTask, int64, error)

	// 状态同步
	SyncTaskStatus(taskID uint) error
	SyncAllTaskStatus() error

	// 批量操作
	BatchPause(taskIDs []uint) error
	BatchResume(taskIDs []uint) error
	BatchCancel(taskIDs []uint) error

	// 统计信息
	GetDownloadStats() (*DownloadStats, error)
	GetUserStats(userID uint) (*UserDownloadStats, error)

	// 文件处理集成
	SetFileProcessingService(fps FileProcessingService)
	EnableAutoProcessing(taskID uint, enabled bool) error
	TriggerFileProcessing(taskID uint) error
	OnDownloadCompleted(taskID uint) error
}

// DownloadStats 下载统计信息
type DownloadStats struct {
	TotalTasks      int64 `json:"total_tasks"`      // 总任务数
	ActiveTasks     int64 `json:"active_tasks"`     // 活跃任务数
	CompletedTasks  int64 `json:"completed_tasks"`  // 已完成任务数
	FailedTasks     int64 `json:"failed_tasks"`     // 失败任务数
	TotalDownloaded int64 `json:"total_downloaded"` // 总下载量(字节)
	DownloadSpeed   int64 `json:"download_speed"`   // 当前下载速度(字节/秒)
	UploadSpeed     int64 `json:"upload_speed"`     // 当前上传速度(字节/秒)
}

// UserDownloadStats 用户下载统计
type UserDownloadStats struct {
	UserID          uint    `json:"user_id"`          // 用户ID
	TotalTasks      int64   `json:"total_tasks"`      // 总任务数
	ActiveTasks     int64   `json:"active_tasks"`     // 活跃任务数
	CompletedTasks  int64   `json:"completed_tasks"`  // 已完成任务数
	FailedTasks     int64   `json:"failed_tasks"`     // 失败任务数
	TotalDownloaded int64   `json:"total_downloaded"` // 总下载量(字节)
	SuccessRate     float64 `json:"success_rate"`     // 成功率
}

// downloadService 下载服务实现
type downloadService struct {
	repo                  repository.Repository
	aria2Service          Aria2Service
	fileProcessingService FileProcessingService
	autoProcessingEnabled map[uint]bool // 任务ID -> 是否启用自动处理
}

// NewDownloadService 创建下载服务
func NewDownloadService(repo repository.Repository, aria2Service Aria2Service) DownloadService {
	return &downloadService{
		repo:                  repo,
		aria2Service:          aria2Service,
		autoProcessingEnabled: make(map[uint]bool),
	}
}

// CreateDownloadTask 创建下载任务
func (ds *downloadService) CreateDownloadTask(userID uint, magnetURI, taskName, savePath string) (*model.DownloadTask, error) {
	// 验证用户权限
	user, err := ds.repo.User().GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	if !user.CanDownload() {
		return nil, fmt.Errorf("user cannot download")
	}

	// 检查用户活跃任务数量限制
	activeTasks, err := ds.repo.Task().GetActiveTasksByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active tasks: %w", err)
	}

	// 假设最大并发下载数为5
	maxConcurrent := 5
	if len(activeTasks) >= maxConcurrent {
		return nil, fmt.Errorf("maximum concurrent downloads (%d) reached", maxConcurrent)
	}

	// 创建下载任务
	task := &model.DownloadTask{
		MagnetURI:  magnetURI,
		TaskName:   taskName,
		Status:     model.TaskStatusPending,
		Priority:   model.TaskPriorityNormal,
		UserID:     userID,
		SavePath:   savePath,
		MaxRetries: 3,
	}

	// 保存到数据库
	if err := ds.repo.Task().Create(task); err != nil {
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	logger.Infof("Created download task: ID=%d, User=%d, URI=%s", task.ID, userID, magnetURI)
	return task, nil
}

// StartDownload 开始下载
func (ds *downloadService) StartDownload(taskID uint) error {
	// 获取任务
	task, err := ds.repo.Task().GetByID(taskID)
	if err != nil {
		return fmt.Errorf("task not found: %w", err)
	}

	// 检查任务状态
	if task.Status != model.TaskStatusPending && task.Status != model.TaskStatusPaused {
		return fmt.Errorf("task cannot be started, current status: %s", task.Status)
	}

	// 创建aria2下载选项
	options := ds.aria2Service.(*aria2Service).CreateDownloadOptions(task)

	// 添加到aria2
	gid, err := ds.aria2Service.AddMagnetTask(task.MagnetURI, options)
	if err != nil {
		return fmt.Errorf("failed to add task to aria2: %w", err)
	}

	// 更新任务状态
	task.Aria2GID = gid
	task.Status = model.TaskStatusRunning
	now := time.Now()
	task.StartedAt = &now

	if err := ds.repo.Task().Update(task); err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}

	logger.Infof("Started download: TaskID=%d, GID=%s", taskID, gid)
	return nil
}

// PauseDownload 暂停下载
func (ds *downloadService) PauseDownload(taskID uint) error {
	task, err := ds.repo.Task().GetByID(taskID)
	if err != nil {
		return fmt.Errorf("task not found: %w", err)
	}

	if task.Aria2GID == "" {
		return fmt.Errorf("task not started")
	}

	if err := ds.aria2Service.PauseTask(task.Aria2GID); err != nil {
		return fmt.Errorf("failed to pause aria2 task: %w", err)
	}

	task.Status = model.TaskStatusPaused
	if err := ds.repo.Task().Update(task); err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}

	logger.Infof("Paused download: TaskID=%d, GID=%s", taskID, task.Aria2GID)
	return nil
}

// ResumeDownload 恢复下载
func (ds *downloadService) ResumeDownload(taskID uint) error {
	task, err := ds.repo.Task().GetByID(taskID)
	if err != nil {
		return fmt.Errorf("task not found: %w", err)
	}

	if task.Aria2GID == "" {
		return fmt.Errorf("task not started")
	}

	if err := ds.aria2Service.ResumeTask(task.Aria2GID); err != nil {
		return fmt.Errorf("failed to resume aria2 task: %w", err)
	}

	task.Status = model.TaskStatusRunning
	if err := ds.repo.Task().Update(task); err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}

	logger.Infof("Resumed download: TaskID=%d, GID=%s", taskID, task.Aria2GID)
	return nil
}

// CancelDownload 取消下载
func (ds *downloadService) CancelDownload(taskID uint) error {
	task, err := ds.repo.Task().GetByID(taskID)
	if err != nil {
		return fmt.Errorf("task not found: %w", err)
	}

	if task.Aria2GID != "" {
		if err := ds.aria2Service.RemoveTask(task.Aria2GID, true); err != nil {
			logger.Warnf("Failed to remove aria2 task %s: %v", task.Aria2GID, err)
		}
	}

	task.Status = model.TaskStatusCancelled
	if err := ds.repo.Task().Update(task); err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}

	logger.Infof("Cancelled download: TaskID=%d, GID=%s", taskID, task.Aria2GID)
	return nil
}

// RetryDownload 重试下载
func (ds *downloadService) RetryDownload(taskID uint) error {
	task, err := ds.repo.Task().GetByID(taskID)
	if err != nil {
		return fmt.Errorf("task not found: %w", err)
	}

	if !task.CanRetry() {
		return fmt.Errorf("task cannot be retried")
	}

	// 清除旧的aria2 GID
	task.Aria2GID = ""
	task.ErrorMessage = ""
	task.RetryCount++
	task.Status = model.TaskStatusPending

	if err := ds.repo.Task().Update(task); err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}

	// 重新开始下载
	return ds.StartDownload(taskID)
}

// GetDownloadTask 获取下载任务
func (ds *downloadService) GetDownloadTask(taskID uint) (*model.DownloadTask, error) {
	return ds.repo.Task().GetByID(taskID)
}

// GetUserDownloads 获取用户下载列表
func (ds *downloadService) GetUserDownloads(userID uint, offset, limit int) ([]*model.DownloadTask, int64, error) {
	return ds.repo.Task().GetByUserID(userID, offset, limit)
}

// GetDownloadsByStatus 根据状态获取下载列表
func (ds *downloadService) GetDownloadsByStatus(status model.TaskStatus, offset, limit int) ([]*model.DownloadTask, int64, error) {
	return ds.repo.Task().GetByStatus(status, offset, limit)
}

// SyncTaskStatus 同步任务状态
func (ds *downloadService) SyncTaskStatus(taskID uint) error {
	task, err := ds.repo.Task().GetByID(taskID)
	if err != nil {
		return fmt.Errorf("task not found: %w", err)
	}

	if task.Aria2GID == "" {
		return nil // 任务未开始，无需同步
	}

	// 记录同步前的状态
	oldStatus := task.Status

	if err := ds.aria2Service.SyncTaskStatus(task); err != nil {
		return fmt.Errorf("failed to sync task status: %w", err)
	}

	if err := ds.repo.Task().Update(task); err != nil {
		return fmt.Errorf("failed to update task: %w", err)
	}

	// 检查是否从非完成状态变为完成状态
	if oldStatus != model.TaskStatusCompleted && task.Status == model.TaskStatusCompleted {
		// 触发下载完成回调
		if err := ds.OnDownloadCompleted(taskID); err != nil {
			logger.Errorf("Download completion callback failed for task %d: %v", taskID, err)
			// 不返回错误，避免影响状态同步
		}
	}

	return nil
}

// SyncAllTaskStatus 同步所有任务状态
func (ds *downloadService) SyncAllTaskStatus() error {
	// 获取所有活跃任务
	activeTasks, _, err := ds.repo.Task().GetByStatus(model.TaskStatusRunning, 0, 100)
	if err != nil {
		return fmt.Errorf("failed to get active tasks: %w", err)
	}

	for _, task := range activeTasks {
		if err := ds.SyncTaskStatus(task.ID); err != nil {
			logger.Errorf("Failed to sync task %d: %v", task.ID, err)
		}
	}

	return nil
}

// BatchPause 批量暂停
func (ds *downloadService) BatchPause(taskIDs []uint) error {
	for _, taskID := range taskIDs {
		if err := ds.PauseDownload(taskID); err != nil {
			logger.Errorf("Failed to pause task %d: %v", taskID, err)
		}
	}
	return nil
}

// BatchResume 批量恢复
func (ds *downloadService) BatchResume(taskIDs []uint) error {
	for _, taskID := range taskIDs {
		if err := ds.ResumeDownload(taskID); err != nil {
			logger.Errorf("Failed to resume task %d: %v", taskID, err)
		}
	}
	return nil
}

// BatchCancel 批量取消
func (ds *downloadService) BatchCancel(taskIDs []uint) error {
	for _, taskID := range taskIDs {
		if err := ds.CancelDownload(taskID); err != nil {
			logger.Errorf("Failed to cancel task %d: %v", taskID, err)
		}
	}
	return nil
}

// GetDownloadStats 获取下载统计
func (ds *downloadService) GetDownloadStats() (*DownloadStats, error) {
	stats := &DownloadStats{}

	// 获取任务统计
	if total, err := ds.repo.Task().CountByStatus(model.TaskStatusPending); err == nil {
		stats.TotalTasks += total
	}
	if active, err := ds.repo.Task().CountByStatus(model.TaskStatusRunning); err == nil {
		stats.ActiveTasks = active
		stats.TotalTasks += active
	}
	if completed, err := ds.repo.Task().CountByStatus(model.TaskStatusCompleted); err == nil {
		stats.CompletedTasks = completed
		stats.TotalTasks += completed
	}
	if failed, err := ds.repo.Task().CountByStatus(model.TaskStatusFailed); err == nil {
		stats.FailedTasks = failed
		stats.TotalTasks += failed
	}

	// 获取aria2全局统计
	if _, err := ds.aria2Service.GetGlobalStats(); err == nil {
		// 这里需要解析字符串到int64，简化处理
		stats.DownloadSpeed = 0 // 实际应该解析globalStat.DownloadSpeed
		stats.UploadSpeed = 0   // 实际应该解析globalStat.UploadSpeed
	}

	return stats, nil
}

// GetUserStats 获取用户统计
func (ds *downloadService) GetUserStats(userID uint) (*UserDownloadStats, error) {
	stats := &UserDownloadStats{
		UserID: userID,
	}

	// 获取用户任务总数
	if total, err := ds.repo.Task().CountByUserID(userID); err == nil {
		stats.TotalTasks = total
	}

	// 这里应该实现更详细的用户统计逻辑
	// 暂时返回基础统计
	return stats, nil
}

// SetFileProcessingService 设置文件处理服务
func (ds *downloadService) SetFileProcessingService(fps FileProcessingService) {
	ds.fileProcessingService = fps
}

// EnableAutoProcessing 启用/禁用自动处理
func (ds *downloadService) EnableAutoProcessing(taskID uint, enabled bool) error {
	// 检查任务是否存在
	_, err := ds.repo.Task().GetByID(taskID)
	if err != nil {
		return fmt.Errorf("task not found: %w", err)
	}

	ds.autoProcessingEnabled[taskID] = enabled

	logger.Infof("Auto processing %s for task %d",
		map[bool]string{true: "enabled", false: "disabled"}[enabled], taskID)

	return nil
}

// TriggerFileProcessing 手动触发文件处理
func (ds *downloadService) TriggerFileProcessing(taskID uint) error {
	if ds.fileProcessingService == nil {
		return fmt.Errorf("file processing service not available")
	}

	// 获取任务
	task, err := ds.repo.Task().GetByID(taskID)
	if err != nil {
		return fmt.Errorf("task not found: %w", err)
	}

	// 检查任务状态
	if !task.CanStartProcessing() {
		return fmt.Errorf("task cannot start processing: status=%s", task.Status)
	}

	// 启动文件处理
	if err := ds.fileProcessingService.StartProcessing(taskID); err != nil {
		return fmt.Errorf("failed to start file processing: %w", err)
	}

	logger.Infof("Manually triggered file processing for task %d", taskID)
	return nil
}

// OnDownloadCompleted 下载完成回调
func (ds *downloadService) OnDownloadCompleted(taskID uint) error {
	logger.Infof("Download completed callback triggered for task %d", taskID)

	// 获取任务信息
	task, err := ds.repo.Task().GetByID(taskID)
	if err != nil {
		logger.Errorf("Failed to get task %d: %v", taskID, err)
		return fmt.Errorf("failed to get task: %w", err)
	}

	// 获取实际下载的文件列表
	if task.Aria2GID != "" {
		logger.Infof("Retrieving actual downloaded files for task %d (GID: %s)", taskID, task.Aria2GID)
		
		actualFiles, err := ds.aria2Service.GetActualDownloadedFiles(task.Aria2GID)
		if err != nil {
			logger.Errorf("Failed to get actual files for task %d: %v", taskID, err)
			// 不返回错误，继续处理，但记录警告
		} else {
			// 存储实际文件信息到数据库
			task.SetActualFiles(actualFiles)
			if err := ds.repo.Task().Update(task); err != nil {
				logger.Errorf("Failed to update task %d with actual files: %v", taskID, err)
			} else {
				logger.Infof("Updated task %d with %d actual files", taskID, len(actualFiles))
				
				// 记录文件详情
				for i, file := range actualFiles {
					logger.Infof("File %d: %s (size: %d bytes, selected: %v)", 
						i+1, file.Name, file.Size, file.Selected)
				}
			}
		}
	} else {
		logger.Warnf("Task %d has no aria2 GID, cannot retrieve actual files", taskID)
	}

	// 检查是否启用自动处理
	autoEnabled, exists := ds.autoProcessingEnabled[taskID]
	if !exists {
		// 默认启用自动处理
		autoEnabled = true
	}

	if !autoEnabled {
		logger.Infof("Auto processing disabled for task %d, skipping", taskID)
		return nil
	}

	// 检查文件处理服务是否可用
	if ds.fileProcessingService == nil {
		logger.Warnf("File processing service not available for task %d", taskID)
		return nil
	}

	// 自动启动文件处理
	if err := ds.fileProcessingService.StartProcessing(taskID); err != nil {
		logger.Errorf("Failed to auto-start file processing for task %d: %v", taskID, err)
		return err
	}

	logger.Infof("Auto-started file processing for task %d", taskID)
	return nil
}
