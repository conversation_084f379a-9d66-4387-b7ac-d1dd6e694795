package service

import (
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/queue"
)

// QueueService 队列服务接口
type QueueService interface {
	// 下载任务队列操作
	EnqueueDownloadTask(task *model.DownloadTask) error
	ProcessDownloadTasks() error

	// 通知队列操作
	EnqueueNotification(userID uint, message string, notificationType string) error
	ProcessNotifications() error

	// 清理队列操作
	EnqueueCleanupTask(taskType string, params map[string]interface{}) error
	ProcessCleanupTasks() error

	// 文件处理队列操作
	EnqueueFileProcessingTask(taskID uint, priority queue.Priority) error
	EnqueueChunkUploadTask(taskID uint, chunkIndex int, priority queue.Priority) error
	EnqueuePlaylistGenTask(taskID uint, priority queue.Priority) error
	ProcessFileProcessingTasks() error

	// 队列管理
	GetQueueStats(queueName string) (*queue.QueueStats, error)
	PurgeQueue(queueName string) error
	RetryFailedTasks(queueName string, limit int) error

	// 延迟任务
	ScheduleTask(task *queue.Task, delay time.Duration) error
	ProcessScheduledTasks() error

	// 健康检查
	HealthCheck() error
}

// queueService 队列服务实现
type queueService struct {
	queue queue.Queue
}

// NewQueueService 创建队列服务
func NewQueueService(q queue.Queue) QueueService {
	return &queueService{
		queue: q,
	}
}

// EnqueueDownloadTask 将下载任务加入队列
func (qs *queueService) EnqueueDownloadTask(task *model.DownloadTask) error {
	// 创建队列任务
	queueTask := queue.NewTask(queue.TaskTypeDownload, queue.Priority(task.Priority), map[string]interface{}{
		"task_id":    task.ID,
		"magnet_uri": task.MagnetURI,
		"task_name":  task.TaskName,
		"user_id":    task.UserID,
		"save_path":  task.SavePath,
	})

	// 设置重试次数
	queueTask.MaxRetries = task.MaxRetries

	logger.Infof("Enqueuing download task: ID=%d, Name=%s", task.ID, task.TaskName)
	return qs.queue.Enqueue(queueTask)
}

// ProcessDownloadTasks 处理下载任务
func (qs *queueService) ProcessDownloadTasks() error {
	for {
		// 阻塞获取任务，超时时间5秒
		task, err := qs.queue.DequeueBlocking(queue.QueueDownload, 5*time.Second)
		if err != nil {
			// 超时或其他错误，继续循环
			continue
		}

		logger.Infof("Processing download task: ID=%s, Type=%s", task.ID, task.Type)

		// 处理任务
		if err := qs.processDownloadTask(task); err != nil {
			logger.Errorf("Failed to process download task %s: %v", task.ID, err)

			// 标记任务失败
			task.MarkAsFailed(err.Error())
			qs.queue.UpdateTask(task)

			// 检查是否可以重试
			if task.CanRetry() {
				logger.Infof("Retrying download task: ID=%s, RetryCount=%d", task.ID, task.RetryCount)
				qs.queue.RetryTask(task.ID)
			}
		} else {
			// 标记任务完成
			task.MarkAsCompleted()
			qs.queue.UpdateTask(task)
			logger.Infof("Download task completed: ID=%s", task.ID)
		}
	}
}

// processDownloadTask 处理单个下载任务
func (qs *queueService) processDownloadTask(task *queue.Task) error {
	// 从任务载荷中提取信息
	taskID, ok := task.Payload["task_id"].(float64) // JSON数字解析为float64
	if !ok {
		return fmt.Errorf("invalid task_id in payload")
	}

	magnetURI, ok := task.Payload["magnet_uri"].(string)
	if !ok {
		return fmt.Errorf("invalid magnet_uri in payload")
	}

	// 这里应该调用aria2服务来实际执行下载
	// 暂时模拟处理
	logger.Infof("Starting download: TaskID=%d, MagnetURI=%s", uint(taskID), magnetURI)

	// 模拟下载过程
	time.Sleep(1 * time.Second)

	return nil
}

// EnqueueNotification 将通知加入队列
func (qs *queueService) EnqueueNotification(userID uint, message string, notificationType string) error {
	queueTask := queue.NewTask(queue.TaskTypeNotification, queue.PriorityNormal, map[string]interface{}{
		"user_id":           userID,
		"message":           message,
		"notification_type": notificationType,
		"created_at":        time.Now(),
	})

	logger.Infof("Enqueuing notification: UserID=%d, Type=%s", userID, notificationType)
	return qs.queue.Enqueue(queueTask)
}

// ProcessNotifications 处理通知任务
func (qs *queueService) ProcessNotifications() error {
	for {
		task, err := qs.queue.DequeueBlocking(queue.QueueNotification, 5*time.Second)
		if err != nil {
			continue
		}

		logger.Infof("Processing notification task: ID=%s", task.ID)

		if err := qs.processNotificationTask(task); err != nil {
			logger.Errorf("Failed to process notification task %s: %v", task.ID, err)
			task.MarkAsFailed(err.Error())
		} else {
			task.MarkAsCompleted()
		}

		qs.queue.UpdateTask(task)
	}
}

// processNotificationTask 处理单个通知任务
func (qs *queueService) processNotificationTask(task *queue.Task) error {
	userID, ok := task.Payload["user_id"].(float64)
	if !ok {
		return fmt.Errorf("invalid user_id in payload")
	}

	message, ok := task.Payload["message"].(string)
	if !ok {
		return fmt.Errorf("invalid message in payload")
	}

	notificationType, ok := task.Payload["notification_type"].(string)
	if !ok {
		return fmt.Errorf("invalid notification_type in payload")
	}

	// 这里应该实际发送通知（WebSocket、邮件等）
	logger.Infof("Sending notification: UserID=%d, Type=%s, Message=%s", uint(userID), notificationType, message)

	return nil
}

// EnqueueCleanupTask 将清理任务加入队列
func (qs *queueService) EnqueueCleanupTask(taskType string, params map[string]interface{}) error {
	queueTask := queue.NewTask(queue.TaskTypeCleanup, queue.PriorityLow, map[string]interface{}{
		"cleanup_type": taskType,
		"params":       params,
	})

	logger.Infof("Enqueuing cleanup task: Type=%s", taskType)
	return qs.queue.Enqueue(queueTask)
}

// ProcessCleanupTasks 处理清理任务
func (qs *queueService) ProcessCleanupTasks() error {
	for {
		task, err := qs.queue.DequeueBlocking(queue.QueueCleanup, 10*time.Second)
		if err != nil {
			continue
		}

		logger.Infof("Processing cleanup task: ID=%s", task.ID)

		if err := qs.processCleanupTask(task); err != nil {
			logger.Errorf("Failed to process cleanup task %s: %v", task.ID, err)
			task.MarkAsFailed(err.Error())
		} else {
			task.MarkAsCompleted()
		}

		qs.queue.UpdateTask(task)
	}
}

// processCleanupTask 处理单个清理任务
func (qs *queueService) processCleanupTask(task *queue.Task) error {
	cleanupType, ok := task.Payload["cleanup_type"].(string)
	if !ok {
		return fmt.Errorf("invalid cleanup_type in payload")
	}

	switch cleanupType {
	case "completed_tasks":
		// 清理已完成的任务
		logger.Info("Cleaning up completed tasks")
		// 这里应该调用repository清理已完成的任务

	case "expired_sessions":
		// 清理过期会话
		logger.Info("Cleaning up expired sessions")
		// 这里应该清理过期的用户会话

	case "temp_files":
		// 清理临时文件
		logger.Info("Cleaning up temporary files")
		// 这里应该清理临时文件

	default:
		return fmt.Errorf("unknown cleanup type: %s", cleanupType)
	}

	return nil
}

// GetQueueStats 获取队列统计信息
func (qs *queueService) GetQueueStats(queueName string) (*queue.QueueStats, error) {
	return qs.queue.GetQueueStats(queueName)
}

// PurgeQueue 清空队列
func (qs *queueService) PurgeQueue(queueName string) error {
	logger.Warnf("Purging queue: %s", queueName)
	return qs.queue.PurgeQueue(queueName)
}

// RetryFailedTasks 重试失败的任务
func (qs *queueService) RetryFailedTasks(queueName string, limit int) error {
	failedTasks, err := qs.queue.GetFailedTasks(limit)
	if err != nil {
		return err
	}

	retryCount := 0
	for _, task := range failedTasks {
		if task.CanRetry() {
			if err := qs.queue.RetryTask(task.ID); err != nil {
				logger.Errorf("Failed to retry task %s: %v", task.ID, err)
				continue
			}
			retryCount++
		}
	}

	logger.Infof("Retried %d failed tasks in queue %s", retryCount, queueName)
	return nil
}

// ScheduleTask 调度延迟任务
func (qs *queueService) ScheduleTask(task *queue.Task, delay time.Duration) error {
	logger.Infof("Scheduling task: ID=%s, Delay=%v", task.ID, delay)
	return qs.queue.EnqueueDelayed(task, delay)
}

// ProcessScheduledTasks 处理计划任务
func (qs *queueService) ProcessScheduledTasks() error {
	logger.Debug("Processing scheduled tasks")
	return qs.queue.ProcessDelayedTasks()
}

// HealthCheck 健康检查
func (qs *queueService) HealthCheck() error {
	return qs.queue.Ping()
}

// EnqueueFileProcessingTask 将文件处理任务加入队列
func (qs *queueService) EnqueueFileProcessingTask(taskID uint, priority queue.Priority) error {
	queueTask := queue.NewTask(queue.TaskTypeFileProcessing, priority, map[string]interface{}{
		"task_id":    taskID,
		"created_at": time.Now(),
	})

	logger.Infof("Enqueuing file processing task: TaskID=%d", taskID)
	return qs.queue.Enqueue(queueTask)
}

// EnqueueChunkUploadTask 将分片上传任务加入队列
func (qs *queueService) EnqueueChunkUploadTask(taskID uint, chunkIndex int, priority queue.Priority) error {
	queueTask := queue.NewTask(queue.TaskTypeChunkUpload, priority, map[string]interface{}{
		"task_id":     taskID,
		"chunk_index": chunkIndex,
		"created_at":  time.Now(),
	})

	logger.Infof("Enqueuing chunk upload task: TaskID=%d, ChunkIndex=%d", taskID, chunkIndex)
	return qs.queue.Enqueue(queueTask)
}

// EnqueuePlaylistGenTask 将播放列表生成任务加入队列
func (qs *queueService) EnqueuePlaylistGenTask(taskID uint, priority queue.Priority) error {
	queueTask := queue.NewTask(queue.TaskTypePlaylistGen, priority, map[string]interface{}{
		"task_id":    taskID,
		"created_at": time.Now(),
	})

	logger.Infof("Enqueuing playlist generation task: TaskID=%d", taskID)
	return qs.queue.Enqueue(queueTask)
}

// ProcessFileProcessingTasks 处理文件处理任务
func (qs *queueService) ProcessFileProcessingTasks() error {
	for {
		// 处理文件处理任务
		task, err := qs.queue.DequeueBlocking(queue.QueueFileProcessing, 5*time.Second)
		if err != nil {
			continue
		}

		// 根据任务类型分发处理
		switch task.Type {
		case queue.TaskTypeFileProcessing:
			logger.Infof("Processing file processing task: ID=%s", task.ID)
			if err := qs.processFileProcessingTask(task); err != nil {
				logger.Errorf("Failed to process file processing task %s: %v", task.ID, err)
				task.MarkAsFailed(err.Error())
			} else {
				task.MarkAsCompleted()
			}

		case queue.TaskTypeChunkUpload:
			logger.Infof("Processing chunk upload task: ID=%s", task.ID)
			if err := qs.processChunkUploadTask(task); err != nil {
				logger.Errorf("Failed to process chunk upload task %s: %v", task.ID, err)
				task.MarkAsFailed(err.Error())
			} else {
				task.MarkAsCompleted()
			}

		case queue.TaskTypePlaylistGen:
			logger.Infof("Processing playlist generation task: ID=%s", task.ID)
			if err := qs.processPlaylistGenTask(task); err != nil {
				logger.Errorf("Failed to process playlist generation task %s: %v", task.ID, err)
				task.MarkAsFailed(err.Error())
			} else {
				task.MarkAsCompleted()
			}

		default:
			// 跳过不相关的任务类型
			continue
		}

		// 更新任务状态
		qs.queue.UpdateTask(task)

		// 检查重试
		if task.Status == queue.TaskStatusFailed && task.CanRetry() {
			logger.Infof("Retrying failed task: ID=%s, RetryCount=%d", task.ID, task.RetryCount)
			qs.queue.RetryTask(task.ID)
		}
	}
}

// processFileProcessingTask 处理文件处理任务
func (qs *queueService) processFileProcessingTask(task *queue.Task) error {
	taskID, ok := task.Payload["task_id"].(float64)
	if !ok {
		return fmt.Errorf("invalid task_id in payload")
	}

	// 这里应该调用文件处理服务
	logger.Infof("Processing file for task: TaskID=%d", uint(taskID))

	// 模拟处理时间
	time.Sleep(2 * time.Second)

	return nil
}

// processChunkUploadTask 处理分片上传任务
func (qs *queueService) processChunkUploadTask(task *queue.Task) error {
	taskID, ok := task.Payload["task_id"].(float64)
	if !ok {
		return fmt.Errorf("invalid task_id in payload")
	}

	chunkIndex, ok := task.Payload["chunk_index"].(float64)
	if !ok {
		return fmt.Errorf("invalid chunk_index in payload")
	}

	// 这里应该调用分片上传服务
	logger.Infof("Uploading chunk for task: TaskID=%d, ChunkIndex=%d", uint(taskID), int(chunkIndex))

	// 模拟上传时间
	time.Sleep(1 * time.Second)

	return nil
}

// processPlaylistGenTask 处理播放列表生成任务
func (qs *queueService) processPlaylistGenTask(task *queue.Task) error {
	taskID, ok := task.Payload["task_id"].(float64)
	if !ok {
		return fmt.Errorf("invalid task_id in payload")
	}

	// 这里应该调用播放列表生成服务
	logger.Infof("Generating playlist for task: TaskID=%d", uint(taskID))

	// 模拟生成时间
	time.Sleep(500 * time.Millisecond)

	return nil
}
