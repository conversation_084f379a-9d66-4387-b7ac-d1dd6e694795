package app

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"magnet-downloader/internal/api/handler"
	"magnet-downloader/internal/config"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/scheduler"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/database"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/redis"

	"github.com/gin-gonic/gin"
)

// App 应用程序结构
type App struct {
	config    *config.Config
	server    *http.Server
	repo      repository.Repository
	services  *service.Services
	scheduler *scheduler.Scheduler
}

// NewApp 创建应用程序实例
func NewApp() (*App, error) {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// 初始化日志
	logger.Init(cfg.Log.Level, cfg.Log.Format)
	log := logger.GetLogger()

	log.Info("Initializing Magnet Downloader Server...")

	// 初始化数据库
	if err := database.Init(&cfg.Database); err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	// 自动迁移数据库 (暂时跳过，表已存在)
	// if err := database.AutoMigrate(); err != nil {
	// 	return nil, fmt.Errorf("failed to migrate database: %w", err)
	// }

	// 初始化默认数据 (暂时跳过，数据已存在)
	// if err := database.SeedDefaultData(); err != nil {
	// 	return nil, fmt.Errorf("failed to seed default data: %w", err)
	// }

	// 初始化Redis
	if err := redis.Init(&cfg.Redis); err != nil {
		return nil, fmt.Errorf("failed to initialize Redis: %w", err)
	}

	// 创建仓库层
	repo := repository.NewRepository(database.GetDB())

	// 创建服务层
	services := service.NewServices(repo, cfg)

	// 创建调度器
	var taskScheduler *scheduler.Scheduler
	if cfg.Scheduler.Enabled {
		taskScheduler, err = scheduler.NewScheduler(cfg, repo, services)
		if err != nil {
			return nil, fmt.Errorf("failed to create scheduler: %w", err)
		}
	}

	app := &App{
		config:    cfg,
		repo:      repo,
		services:  services,
		scheduler: taskScheduler,
	}

	// 创建HTTP服务器
	if err := app.setupServer(); err != nil {
		return nil, fmt.Errorf("failed to setup server: %w", err)
	}

	return app, nil
}

// setupServer 设置HTTP服务器
func (a *App) setupServer() error {
	// 设置Gin模式
	if a.config.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := a.setupRoutes()

	// 创建HTTP服务器
	a.server = &http.Server{
		Addr:         fmt.Sprintf("%s:%d", a.config.Server.Host, a.config.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(a.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(a.config.Server.WriteTimeout) * time.Second,
	}

	return nil
}

// setupRoutes 设置路由
func (a *App) setupRoutes() *gin.Engine {
	router := gin.New()

	// 添加基础中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// 健康检查接口
	router.GET("/health", a.healthCheck)
	router.GET("/health/database", a.databaseHealth)
	router.GET("/health/redis", a.redisHealth)

	// 基础路由
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Magnet Downloader API Server",
			"version": a.config.App.Version,
		})
	})

	// API路由组
	api := router.Group("/api/v1")
	{
		// 调度器管理API（暂时不需要认证，用于测试）
		if a.scheduler != nil {
			schedulerGroup := api.Group("/scheduler")
			{
				schedulerHandler := handler.NewSchedulerHandler(a.scheduler)
				schedulerGroup.GET("/status", schedulerHandler.GetSchedulerStatus)
				schedulerGroup.GET("/jobs", schedulerHandler.GetJobs)
				schedulerGroup.GET("/jobs/:job_name", schedulerHandler.GetJobStats)
				schedulerGroup.POST("/jobs", schedulerHandler.AddJob)
				schedulerGroup.DELETE("/jobs/:job_name", schedulerHandler.RemoveJob)
				schedulerGroup.POST("/validate", schedulerHandler.ValidateCronSpec)
			}
		}

		// 系统信息API
		api.GET("/system/info", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message":   "Magnet Downloader System",
				"version":   a.config.App.Version,
				"scheduler": a.scheduler != nil && a.scheduler.IsRunning(),
			})
		})
	}

	return router
}

// healthCheck 健康检查
func (a *App) healthCheck(c *gin.Context) {
	dbHealth := database.IsHealthy()
	redisHealth := redis.IsHealthy()
	status := "ok"
	if !dbHealth || !redisHealth {
		status = "degraded"
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    status,
		"timestamp": time.Now().Unix(),
		"version":   a.config.App.Version,
		"database":  dbHealth,
		"redis":     redisHealth,
		"scheduler": a.scheduler != nil && a.scheduler.IsRunning(),
	})
}

// databaseHealth 数据库健康检查
func (a *App) databaseHealth(c *gin.Context) {
	stats := database.GetStats()
	c.JSON(http.StatusOK, stats)
}

// redisHealth Redis健康检查
func (a *App) redisHealth(c *gin.Context) {
	stats := redis.GetStats()
	c.JSON(http.StatusOK, stats)
}

// Run 运行应用程序
func (a *App) Run() error {
	log := logger.GetLogger()

	// 启动调度器
	if a.scheduler != nil {
		if err := a.scheduler.Start(); err != nil {
			return fmt.Errorf("failed to start scheduler: %w", err)
		}
		log.Info("Scheduler started successfully")
	}

	// 启动自动上传服务
	if a.services.AutoUpload != nil {
		ctx := context.Background()
		if err := a.services.AutoUpload.Start(ctx); err != nil {
			log.Errorf("Failed to start auto upload service: %v", err)
		} else {
			log.Info("Auto upload service started successfully")
		}
	}

	// 启动HTTP服务器
	go func() {
		log.Infof("Server starting on %s", a.server.Addr)
		if err := a.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("Shutting down server...")

	// 停止自动上传服务
	if a.services.AutoUpload != nil {
		if err := a.services.AutoUpload.Stop(); err != nil {
			log.Errorf("Failed to stop auto upload service: %v", err)
		} else {
			log.Info("Auto upload service stopped")
		}
	}

	// 停止调度器
	if a.scheduler != nil {
		a.scheduler.Stop()
		log.Info("Scheduler stopped")
	}

	// 优雅关闭HTTP服务器
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := a.server.Shutdown(ctx); err != nil {
		log.Errorf("Server forced to shutdown: %v", err)
		return err
	}

	// 关闭数据库连接
	database.Close()
	redis.Close()

	log.Info("Server exited")
	return nil
}

// Stop 停止应用程序
func (a *App) Stop() error {
	if a.scheduler != nil {
		a.scheduler.Stop()
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return a.server.Shutdown(ctx)
}
