package scheduler

import (
	"context"
	"fmt"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/cron"
	"magnet-downloader/pkg/logger"
)

// TaskCleanupJob 任务清理作业
type TaskCleanupJob struct {
	repo repository.Repository
}

// NewTaskCleanupJob 创建任务清理作业
func NewTaskCleanupJob(repo repository.Repository) cron.Job {
	return &TaskCleanupJob{
		repo: repo,
	}
}

// Execute 执行任务清理
func (j *TaskCleanupJob) Execute(ctx context.Context) error {
	logger.Info("Starting task cleanup job")

	// 清理30天前的已完成任务
	cutoffTime := time.Now().AddDate(0, 0, -30)

	// 获取需要清理的任务
	tasks, err := j.repo.Task().GetByStatusAndTime(model.TaskStatusCompleted, cutoffTime)
	if err != nil {
		return fmt.Errorf("failed to get completed tasks: %w", err)
	}

	cleanedCount := 0
	for _, task := range tasks {
		if err := j.repo.Task().Delete(task.ID); err != nil {
			logger.Errorf("Failed to delete task %d: %v", task.ID, err)
			continue
		}
		cleanedCount++
	}

	// 清理失败的任务（7天前）
	failedCutoffTime := time.Now().AddDate(0, 0, -7)
	failedTasks, err := j.repo.Task().GetByStatusAndTime(model.TaskStatusFailed, failedCutoffTime)
	if err != nil {
		logger.Errorf("Failed to get failed tasks: %v", err)
	} else {
		for _, task := range failedTasks {
			if err := j.repo.Task().Delete(task.ID); err != nil {
				logger.Errorf("Failed to delete failed task %d: %v", task.ID, err)
				continue
			}
			cleanedCount++
		}
	}

	logger.Infof("Task cleanup completed, cleaned %d tasks", cleanedCount)
	return nil
}

// GetName 获取任务名称
func (j *TaskCleanupJob) GetName() string {
	return "task_cleanup"
}

// GetDescription 获取任务描述
func (j *TaskCleanupJob) GetDescription() string {
	return "Clean up old completed and failed tasks"
}

// SystemStatsJob 系统统计作业
type SystemStatsJob struct {
	services *service.Services
}

// NewSystemStatsJob 创建系统统计作业
func NewSystemStatsJob(services *service.Services) cron.Job {
	return &SystemStatsJob{
		services: services,
	}
}

// Execute 执行系统统计
func (j *SystemStatsJob) Execute(ctx context.Context) error {
	logger.Info("Starting system stats job")

	// 获取任务统计
	taskStats, err := j.services.Task.GetTaskStats()
	if err != nil {
		logger.Errorf("Failed to get task stats: %v", err)
	} else {
		logger.Infof("Task stats: Total=%d, Active=%d, Completed=%d",
			taskStats.TotalTasks, taskStats.ActiveTasks, taskStats.CompletedTasks)
	}

	// 获取用户统计
	userStats, err := j.services.User.GetUserStats()
	if err != nil {
		logger.Errorf("Failed to get user stats: %v", err)
	} else {
		logger.Infof("User stats: Total=%d, Active=%d",
			userStats.TotalUsers, userStats.ActiveUsers)
	}

	// 获取WebSocket统计
	wsStats := j.services.WebSocket.GetStats()
	logger.Infof("WebSocket stats: Current=%d, Total=%d, Messages=%d",
		wsStats.CurrentConnections, wsStats.TotalConnections, wsStats.MessagesSent)

	return nil
}

// GetName 获取任务名称
func (j *SystemStatsJob) GetName() string {
	return "system_stats"
}

// GetDescription 获取任务描述
func (j *SystemStatsJob) GetDescription() string {
	return "Collect and log system statistics"
}

// TaskRetryJob 任务重试作业
type TaskRetryJob struct {
	taskService service.TaskService
}

// NewTaskRetryJob 创建任务重试作业
func NewTaskRetryJob(taskService service.TaskService) cron.Job {
	return &TaskRetryJob{
		taskService: taskService,
	}
}

// Execute 执行任务重试
func (j *TaskRetryJob) Execute(ctx context.Context) error {
	logger.Info("Starting task retry job")

	// 这里可以实现自动重试失败任务的逻辑
	// 例如：重试失败时间超过1小时但重试次数未达到上限的任务

	logger.Info("Task retry job completed")
	return nil
}

// GetName 获取任务名称
func (j *TaskRetryJob) GetName() string {
	return "task_retry"
}

// GetDescription 获取任务描述
func (j *TaskRetryJob) GetDescription() string {
	return "Automatically retry failed tasks"
}

// ConfigCacheRefreshJob 配置缓存刷新作业
type ConfigCacheRefreshJob struct {
	configService service.ConfigService
}

// NewConfigCacheRefreshJob 创建配置缓存刷新作业
func NewConfigCacheRefreshJob(configService service.ConfigService) cron.Job {
	return &ConfigCacheRefreshJob{
		configService: configService,
	}
}

// Execute 执行配置缓存刷新
func (j *ConfigCacheRefreshJob) Execute(ctx context.Context) error {
	logger.Info("Starting config cache refresh job")

	if err := j.configService.RefreshConfigCache(); err != nil {
		return fmt.Errorf("failed to refresh config cache: %w", err)
	}

	logger.Info("Config cache refresh completed")
	return nil
}

// GetName 获取任务名称
func (j *ConfigCacheRefreshJob) GetName() string {
	return "config_cache_refresh"
}

// GetDescription 获取任务描述
func (j *ConfigCacheRefreshJob) GetDescription() string {
	return "Refresh configuration cache"
}

// HealthCheckJob 健康检查作业
type HealthCheckJob struct {
	services *service.Services
}

// NewHealthCheckJob 创建健康检查作业
func NewHealthCheckJob(services *service.Services) cron.Job {
	return &HealthCheckJob{
		services: services,
	}
}

// Execute 执行健康检查
func (j *HealthCheckJob) Execute(ctx context.Context) error {
	logger.Info("Starting health check job")

	// 检查各个服务的健康状态
	health := j.services.HealthCheck()

	unhealthyServices := []string{}
	for service, healthy := range health {
		if !healthy {
			unhealthyServices = append(unhealthyServices, service)
		}
	}

	if len(unhealthyServices) > 0 {
		logger.Warnf("Unhealthy services detected: %v", unhealthyServices)

		// 这里可以发送告警通知
		// 例如：发送WebSocket通知给管理员
		if j.services.WebSocket != nil {
			j.services.WebSocket.BroadcastSystemAlert(
				"health_check_alert",
				"服务健康检查告警",
				fmt.Sprintf("检测到不健康的服务: %v", unhealthyServices),
				"warning",
				"health_check",
			)
		}
	} else {
		logger.Info("All services are healthy")
	}

	return nil
}

// GetName 获取任务名称
func (j *HealthCheckJob) GetName() string {
	return "health_check"
}

// GetDescription 获取任务描述
func (j *HealthCheckJob) GetDescription() string {
	return "Check system health and send alerts if needed"
}

// DatabaseMaintenanceJob 数据库维护作业
type DatabaseMaintenanceJob struct {
	repo repository.Repository
}

// NewDatabaseMaintenanceJob 创建数据库维护作业
func NewDatabaseMaintenanceJob(repo repository.Repository) cron.Job {
	return &DatabaseMaintenanceJob{
		repo: repo,
	}
}

// Execute 执行数据库维护
func (j *DatabaseMaintenanceJob) Execute(ctx context.Context) error {
	logger.Info("Starting database maintenance job")

	// 这里可以实现数据库维护逻辑
	// 例如：VACUUM、ANALYZE、索引重建等

	logger.Info("Database maintenance completed")
	return nil
}

// GetName 获取任务名称
func (j *DatabaseMaintenanceJob) GetName() string {
	return "database_maintenance"
}

// GetDescription 获取任务描述
func (j *DatabaseMaintenanceJob) GetDescription() string {
	return "Perform database maintenance tasks"
}
