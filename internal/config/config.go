package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	App            AppConfig            `mapstructure:"app"`
	Server         ServerConfig         `mapstructure:"server"`
	Database       DatabaseConfig       `mapstructure:"database"`
	Redis          RedisConfig          `mapstructure:"redis"`
	Aria2          Aria2Config          `mapstructure:"aria2"`
	Log            LogConfig            `mapstructure:"log"`
	JWT            JWTConfig            `mapstructure:"jwt"`
	Scheduler      SchedulerConfig      `mapstructure:"scheduler"`
	FileProcessing FileProcessingConfig `mapstructure:"file_processing"`
}

// AppConfig 应用基础配置
type AppConfig struct {
	Name    string `mapstructure:"name"`
	Version string `mapstructure:"version"`
	Env     string `mapstructure:"env"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Mode         string `mapstructure:"mode"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	User         string `mapstructure:"user"`
	Password     string `mapstructure:"password"`
	DBName       string `mapstructure:"dbname"`
	SSLMode      string `mapstructure:"sslmode"`
	MaxOpenConns int    `mapstructure:"max_open_conns"`
	MaxIdleConns int    `mapstructure:"max_idle_conns"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	PoolSize int    `mapstructure:"pool_size"`
}

// Aria2Config aria2配置
type Aria2Config struct {
	Host   string `mapstructure:"host"`
	Port   int    `mapstructure:"port"`
	Secret string `mapstructure:"secret"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	ExpireTime int    `mapstructure:"expire_time"`
}

// SchedulerConfig 调度器配置
type SchedulerConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	Timezone string `mapstructure:"timezone"`
}

// FileProcessingConfig 文件处理配置
type FileProcessingConfig struct {
	Enabled              bool           `mapstructure:"enabled"`                // 是否启用文件处理
	ChunkSizeMB          int            `mapstructure:"chunk_size_mb"`          // 分片大小(MB)
	MaxConcurrentUploads int            `mapstructure:"max_concurrent_uploads"` // 最大并发上传数
	EncryptionAlgorithm  string         `mapstructure:"encryption_algorithm"`   // 加密算法
	EncryptionEnabled    bool           `mapstructure:"encryption_enabled"`     // 是否启用加密
	KeepOriginalFiles    bool           `mapstructure:"keep_original_files"`    // 是否保留原始文件
	WorkDir              string         `mapstructure:"work_dir"`               // 工作目录
	RetryAttempts        int            `mapstructure:"retry_attempts"`         // 重试次数
	AutoStartProcessing  bool             `mapstructure:"auto_start_processing"`  // 下载完成后自动开始处理
	CleanupAfterDays     int              `mapstructure:"cleanup_after_days"`     // 多少天后清理文件
	UploadProvider       string           `mapstructure:"upload_provider"`        // 上传服务提供商 (imgbb, doodstream)
	ImgBB                ImgBBConfig      `mapstructure:"imgbb"`                  // ImgBB配置
	Imgur                ImgurConfig      `mapstructure:"imgur"`                  // Imgur配置
	DoodStream           DoodStreamConfig `mapstructure:"doodstream"`             // DoodStream配置
	AutoUpload           AutoUploadConfig `mapstructure:"auto_upload"`            // 自动上传配置
	Playlist             PlaylistConfig   `mapstructure:"playlist"`               // 播放列表配置
	MixFile              MixFileConfig    `mapstructure:"mixfile"`                // MixFile配置
}

// ImgBBConfig ImgBB图床配置
type ImgBBConfig struct {
	APIKey     string `mapstructure:"api_key"`     // API密钥
	BaseURL    string `mapstructure:"base_url"`    // API基础URL
	Timeout    int    `mapstructure:"timeout"`     // 请求超时时间(秒)
	MaxRetries int    `mapstructure:"max_retries"` // 最大重试次数
}

// ImgurConfig Imgur图床配置
type ImgurConfig struct {
	ClientID     string `mapstructure:"client_id"`     // 客户端ID
	ClientSecret string `mapstructure:"client_secret"` // 客户端密钥
	RedirectURI  string `mapstructure:"redirect_uri"`  // 回调URL
	BaseURL      string `mapstructure:"base_url"`      // API基础URL
	Timeout      int    `mapstructure:"timeout"`       // 请求超时时间(秒)
	MaxRetries   int    `mapstructure:"max_retries"`   // 最大重试次数
}

// DoodStreamConfig DoodStream配置
type DoodStreamConfig struct {
	APIKey     string `mapstructure:"api_key"`     // API密钥
	BaseURL    string `mapstructure:"base_url"`    // API基础URL
	Timeout    int    `mapstructure:"timeout"`     // 请求超时时间(秒)
	MaxRetries int    `mapstructure:"max_retries"` // 最大重试次数
}

// PlaylistConfig 播放列表配置
type PlaylistConfig struct {
	Version        int    `mapstructure:"version"`         // HLS版本
	TargetDuration int    `mapstructure:"target_duration"` // 目标时长(秒)
	MediaSequence  int    `mapstructure:"media_sequence"`  // 媒体序列号
	AllowCache     bool   `mapstructure:"allow_cache"`     // 是否允许缓存
	PlaylistType   string `mapstructure:"playlist_type"`   // 播放列表类型
}

// MixFileConfig MixFile配置
type MixFileConfig struct {
	Enabled                bool   `mapstructure:"enabled"`                  // 是否启用MixFile功能
	EnableSteganography    bool   `mapstructure:"enable_steganography"`     // 是否启用隐写术
	EnableIndexCompression bool   `mapstructure:"enable_index_compression"` // 是否启用索引压缩
	EnableIndexEncryption  bool   `mapstructure:"enable_index_encryption"`  // 是否启用索引加密
	ShareCodePrefix        string `mapstructure:"share_code_prefix"`        // 分享码前缀
	MaxShareCodeLength     int    `mapstructure:"max_share_code_length"`    // 分享码最大长度
	IndexUploadRetries     int    `mapstructure:"index_upload_retries"`     // 索引上传重试次数
}

// AutoUploadConfig 自动上传配置
type AutoUploadConfig struct {
	Enabled              bool `mapstructure:"enabled"`                // 是否启用自动上传
	ScanInterval         int  `mapstructure:"scan_interval"`          // 扫描间隔(分钟)
	MaxConcurrentUploads int  `mapstructure:"max_concurrent_uploads"` // 最大并发上传数
	MinFileSize          int64 `mapstructure:"min_file_size"`         // 最小文件大小(字节)
	SkipExistingFiles    bool `mapstructure:"skip_existing_files"`    // 跳过已存在的文件
	DeleteAfterUpload    bool `mapstructure:"delete_after_upload"`    // 上传后删除原文件
}

// Load 加载配置
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("/etc/magnet-downloader")

	// 设置默认值
	setDefaults()

	// 环境变量支持
	viper.SetEnvPrefix("MD")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// 配置文件不存在时使用默认配置
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// App默认配置
	viper.SetDefault("app.name", "magnet-downloader")
	viper.SetDefault("app.version", "1.0.0")
	viper.SetDefault("app.env", "development")

	// Server默认配置
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("server.read_timeout", 60)
	viper.SetDefault("server.write_timeout", 60)

	// Database默认配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "password")
	viper.SetDefault("database.dbname", "magnet_downloader")
	viper.SetDefault("database.sslmode", "disable")
	viper.SetDefault("database.max_open_conns", 100)
	viper.SetDefault("database.max_idle_conns", 10)

	// Redis默认配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.pool_size", 10)

	// Aria2默认配置
	viper.SetDefault("aria2.host", "localhost")
	viper.SetDefault("aria2.port", 6800)
	viper.SetDefault("aria2.secret", "")

	// Log默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	viper.SetDefault("log.output", "stdout")

	// JWT默认配置
	viper.SetDefault("jwt.secret", "your-secret-key")
	viper.SetDefault("jwt.expire_time", 3600)

	// Scheduler默认配置
	viper.SetDefault("scheduler.enabled", true)
	viper.SetDefault("scheduler.timezone", "UTC")

	// FileProcessing默认配置
	viper.SetDefault("file_processing.enabled", true)
	viper.SetDefault("file_processing.chunk_size_mb", 1)
	viper.SetDefault("file_processing.max_concurrent_uploads", 3)
	viper.SetDefault("file_processing.encryption_algorithm", "aes-gcm-256")
	viper.SetDefault("file_processing.encryption_enabled", true)
	viper.SetDefault("file_processing.keep_original_files", false)
	viper.SetDefault("file_processing.work_dir", "/tmp/fileprocessor")
	viper.SetDefault("file_processing.retry_attempts", 3)
	viper.SetDefault("file_processing.auto_start_processing", true)
	viper.SetDefault("file_processing.cleanup_after_days", 30)
	viper.SetDefault("file_processing.upload_provider", "imgbb")

	// 本地图床默认配置 (Telegraph-Image Express)
	viper.SetDefault("file_processing.imgbb.api_key", "")
	viper.SetDefault("file_processing.imgbb.base_url", "http://localhost:3000")
	viper.SetDefault("file_processing.imgbb.timeout", 30)
	viper.SetDefault("file_processing.imgbb.max_retries", 3)

	// Imgur默认配置
	viper.SetDefault("file_processing.imgur.client_id", "")
	viper.SetDefault("file_processing.imgur.client_secret", "")
	viper.SetDefault("file_processing.imgur.redirect_uri", "http://localhost:8080/api/imgur/callback")
	viper.SetDefault("file_processing.imgur.base_url", "https://api.imgur.com/3")
	viper.SetDefault("file_processing.imgur.timeout", 30)
	viper.SetDefault("file_processing.imgur.max_retries", 3)

	// DoodStream默认配置
	viper.SetDefault("file_processing.doodstream.api_key", "520970w0nqtrdi6r1w6a3u")
	viper.SetDefault("file_processing.doodstream.base_url", "https://doodapi.co")
	viper.SetDefault("file_processing.doodstream.timeout", 300)
	viper.SetDefault("file_processing.doodstream.max_retries", 3)

	// Playlist默认配置
	viper.SetDefault("file_processing.playlist.version", 3)
	viper.SetDefault("file_processing.playlist.target_duration", 10)
	viper.SetDefault("file_processing.playlist.media_sequence", 0)
	viper.SetDefault("file_processing.playlist.allow_cache", true)
	viper.SetDefault("file_processing.playlist.playlist_type", "VOD")

	// MixFile默认配置
	viper.SetDefault("file_processing.mixfile.enabled", false)
	viper.SetDefault("file_processing.mixfile.enable_steganography", true)
	viper.SetDefault("file_processing.mixfile.enable_index_compression", true)
	viper.SetDefault("file_processing.mixfile.enable_index_encryption", true)
	viper.SetDefault("file_processing.mixfile.share_code_prefix", "mf://")
	viper.SetDefault("file_processing.mixfile.max_share_code_length", 2048)
	viper.SetDefault("file_processing.mixfile.index_upload_retries", 3)

	// 自动上传默认配置
	viper.SetDefault("file_processing.auto_upload.enabled", true)
	viper.SetDefault("file_processing.auto_upload.scan_interval", 5)            // 5分钟扫描一次，更频繁
	viper.SetDefault("file_processing.auto_upload.max_concurrent_uploads", 3)   // 最大3个并发上传
	viper.SetDefault("file_processing.auto_upload.min_file_size", 10485760)     // 10MB最小文件大小
	viper.SetDefault("file_processing.auto_upload.skip_existing_files", true)   // 跳过已存在文件
	viper.SetDefault("file_processing.auto_upload.delete_after_upload", true)   // 上传后删除原文件夹
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 验证应用配置
	if c.App.Name == "" {
		return fmt.Errorf("app.name is required")
	}

	// 验证服务器配置
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("server.port must be between 1 and 65535")
	}

	// 验证数据库配置
	if c.Database.Host == "" {
		return fmt.Errorf("database.host is required")
	}
	if c.Database.Port <= 0 || c.Database.Port > 65535 {
		return fmt.Errorf("database.port must be between 1 and 65535")
	}
	if c.Database.DBName == "" {
		return fmt.Errorf("database.dbname is required")
	}

	// 验证Redis配置
	if c.Redis.Host == "" {
		return fmt.Errorf("redis.host is required")
	}
	if c.Redis.Port <= 0 || c.Redis.Port > 65535 {
		return fmt.Errorf("redis.port must be between 1 and 65535")
	}

	// 验证Aria2配置
	if c.Aria2.Host == "" {
		return fmt.Errorf("aria2.host is required")
	}
	if c.Aria2.Port <= 0 || c.Aria2.Port > 65535 {
		return fmt.Errorf("aria2.port must be between 1 and 65535")
	}

	// 验证JWT配置
	if c.JWT.Secret == "" || c.JWT.Secret == "your-secret-key" {
		return fmt.Errorf("jwt.secret must be set to a secure value")
	}
	if c.JWT.ExpireTime <= 0 {
		return fmt.Errorf("jwt.expire_time must be positive")
	}

	// 验证文件处理配置
	if err := c.validateFileProcessing(); err != nil {
		return fmt.Errorf("file_processing validation failed: %w", err)
	}

	return nil
}

// validateFileProcessing 验证文件处理配置
func (c *Config) validateFileProcessing() error {
	fp := &c.FileProcessing

	// 验证分片大小
	if fp.ChunkSizeMB <= 0 || fp.ChunkSizeMB > 100 {
		return fmt.Errorf("chunk_size_mb must be between 1 and 100")
	}

	// 验证并发数
	if fp.MaxConcurrentUploads <= 0 || fp.MaxConcurrentUploads > 10 {
		return fmt.Errorf("max_concurrent_uploads must be between 1 and 10")
	}

	// 验证加密算法
	validAlgorithms := []string{"aes-gcm-256", "aes-gcm-128"}
	algorithmValid := false
	for _, alg := range validAlgorithms {
		if fp.EncryptionAlgorithm == alg {
			algorithmValid = true
			break
		}
	}
	if !algorithmValid {
		return fmt.Errorf("encryption_algorithm must be one of: %v", validAlgorithms)
	}

	// 验证工作目录
	if fp.WorkDir == "" {
		return fmt.Errorf("work_dir is required")
	}

	// 验证重试次数
	if fp.RetryAttempts < 0 || fp.RetryAttempts > 10 {
		return fmt.Errorf("retry_attempts must be between 0 and 10")
	}

	// 验证清理天数
	if fp.CleanupAfterDays < 1 || fp.CleanupAfterDays > 365 {
		return fmt.Errorf("cleanup_after_days must be between 1 and 365")
	}

	// 验证图床配置
	// 本地图床(localhost:3000)不需要API密钥，其他图床需要
	if fp.Enabled && fp.ImgBB.APIKey == "" && !strings.Contains(fp.ImgBB.BaseURL, "localhost:3000") {
		return fmt.Errorf("imgbb.api_key is required when file processing is enabled (except for local image host)")
	}

	if fp.ImgBB.Timeout <= 0 || fp.ImgBB.Timeout > 300 {
		return fmt.Errorf("imgbb.timeout must be between 1 and 300 seconds")
	}

	if fp.ImgBB.MaxRetries < 0 || fp.ImgBB.MaxRetries > 10 {
		return fmt.Errorf("imgbb.max_retries must be between 0 and 10")
	}

	// 验证播放列表配置
	if fp.Playlist.Version < 1 || fp.Playlist.Version > 7 {
		return fmt.Errorf("playlist.version must be between 1 and 7")
	}

	if fp.Playlist.TargetDuration <= 0 || fp.Playlist.TargetDuration > 3600 {
		return fmt.Errorf("playlist.target_duration must be between 1 and 3600 seconds")
	}

	validPlaylistTypes := []string{"VOD", "EVENT"}
	playlistTypeValid := false
	for _, pt := range validPlaylistTypes {
		if fp.Playlist.PlaylistType == pt {
			playlistTypeValid = true
			break
		}
	}
	if !playlistTypeValid {
		return fmt.Errorf("playlist.playlist_type must be one of: %v", validPlaylistTypes)
	}

	// 验证上传提供商
	validProviders := []string{"imgbb", "doodstream"}
	providerValid := false
	for _, provider := range validProviders {
		if fp.UploadProvider == provider {
			providerValid = true
			break
		}
	}
	if !providerValid {
		return fmt.Errorf("upload_provider must be one of: %v", validProviders)
	}

	// 验证DoodStream配置
	if fp.UploadProvider == "doodstream" {
		if fp.DoodStream.APIKey == "" {
			return fmt.Errorf("doodstream.api_key is required when upload_provider is doodstream")
		}

		if fp.DoodStream.BaseURL == "" {
			return fmt.Errorf("doodstream.base_url is required")
		}

		if fp.DoodStream.Timeout <= 0 || fp.DoodStream.Timeout > 600 {
			return fmt.Errorf("doodstream.timeout must be between 1 and 600 seconds")
		}

		if fp.DoodStream.MaxRetries < 0 || fp.DoodStream.MaxRetries > 10 {
			return fmt.Errorf("doodstream.max_retries must be between 0 and 10")
		}
	}

	return nil
}
