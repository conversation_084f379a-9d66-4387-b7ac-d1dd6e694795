package middleware

import (
	"strconv"
	"time"

	"magnet-downloader/pkg/metrics"

	"github.com/gin-gonic/gin"
)

// MetricsMiddleware HTTP指标中间件
func MetricsMiddleware(collector *metrics.Collector) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		// 增加正在处理的请求数
		collector.GetMetrics().HTTPRequestsInFlight.Inc()
		defer collector.GetMetrics().HTTPRequestsInFlight.Dec()

		// 处理请求
		c.Next()

		// 记录请求指标
		duration := time.Since(start)
		method := c.Request.Method
		endpoint := c.FullPath()
		status := strconv.Itoa(c.Writer.Status())

		collector.GetMetrics().RecordHTTPRequest(method, endpoint, status, duration)
	}
}
