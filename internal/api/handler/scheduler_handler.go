package handler

import (
	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/scheduler"
	"magnet-downloader/pkg/cron"

	"github.com/gin-gonic/gin"
)

// SchedulerHandler 调度器处理器
type SchedulerHandler struct {
	scheduler *scheduler.Scheduler
}

// NewSchedulerHandler 创建调度器处理器
func NewSchedulerHandler(scheduler *scheduler.Scheduler) *SchedulerHandler {
	return &SchedulerHandler{
		scheduler: scheduler,
	}
}

// GetJobs 获取所有调度任务
// @Summary 获取调度任务列表
// @Description 获取所有已注册的调度任务信息（仅管理员）
// @Tags scheduler
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=[]map[string]interface{}}
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/scheduler/jobs [get]
func (h *SchedulerHandler) GetJobs(c *gin.Context) {
	jobs := h.scheduler.GetJobs()
	middleware.SuccessResponse(c, jobs)
}

// GetJobStats 获取指定任务统计信息
// @Summary 获取任务统计信息
// @Description 获取指定调度任务的统计信息（仅管理员）
// @Tags scheduler
// @Produce json
// @Param job_name path string true "任务名称"
// @Success 200 {object} middleware.APIResponse{data=map[string]interface{}}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/scheduler/jobs/{job_name} [get]
func (h *SchedulerHandler) GetJobStats(c *gin.Context) {
	jobName := c.Param("job_name")
	if jobName == "" {
		middleware.BadRequestResponse(c, "job name is required", nil)
		return
	}

	stats, err := h.scheduler.GetJobStats(jobName)
	if err != nil {
		middleware.NotFoundResponse(c, "job not found")
		return
	}

	middleware.SuccessResponse(c, stats)
}

// AddJob 添加调度任务
// @Summary 添加调度任务
// @Description 添加新的调度任务（仅管理员）
// @Tags scheduler
// @Accept json
// @Produce json
// @Param request body AddJobRequest true "添加任务请求"
// @Success 201 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/scheduler/jobs [post]
func (h *SchedulerHandler) AddJob(c *gin.Context) {
	var req AddJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 验证Cron表达式
	if err := h.scheduler.ValidateCronSpec(req.Spec); err != nil {
		middleware.BadRequestResponse(c, "invalid cron spec", err.Error())
		return
	}

	// 这里需要根据任务类型创建具体的Job实例
	// 暂时返回成功，实际实现需要扩展
	middleware.SuccessResponse(c, gin.H{
		"message": "job added successfully",
		"name":    req.Name,
		"spec":    req.Spec,
	})
}

// RemoveJob 移除调度任务
// @Summary 移除调度任务
// @Description 移除指定的调度任务（仅管理员）
// @Tags scheduler
// @Produce json
// @Param job_name path string true "任务名称"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/scheduler/jobs/{job_name} [delete]
func (h *SchedulerHandler) RemoveJob(c *gin.Context) {
	jobName := c.Param("job_name")
	if jobName == "" {
		middleware.BadRequestResponse(c, "job name is required", nil)
		return
	}

	if err := h.scheduler.RemoveJob(jobName); err != nil {
		middleware.BadRequestResponse(c, "failed to remove job", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{
		"message": "job removed successfully",
		"name":    jobName,
	})
}

// GetSchedulerStatus 获取调度器状态
// @Summary 获取调度器状态
// @Description 获取调度器运行状态和基本信息（仅管理员）
// @Tags scheduler
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=SchedulerStatusResponse}
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/scheduler/status [get]
func (h *SchedulerHandler) GetSchedulerStatus(c *gin.Context) {
	status := SchedulerStatusResponse{
		IsRunning: h.scheduler.IsRunning(),
		Timezone:  h.scheduler.GetTimezone(),
		JobCount:  len(h.scheduler.GetJobs()),
	}

	middleware.SuccessResponse(c, status)
}

// ValidateCronSpec 验证Cron表达式
// @Summary 验证Cron表达式
// @Description 验证Cron表达式的有效性（仅管理员）
// @Tags scheduler
// @Accept json
// @Produce json
// @Param request body ValidateCronSpecRequest true "验证请求"
// @Success 200 {object} middleware.APIResponse{data=ValidateCronSpecResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/scheduler/validate [post]
func (h *SchedulerHandler) ValidateCronSpec(c *gin.Context) {
	var req ValidateCronSpecRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	err := cron.ValidateCronSpec(req.Spec)
	response := ValidateCronSpecResponse{
		Valid: err == nil,
		Spec:  req.Spec,
	}

	if err != nil {
		response.Error = err.Error()
	}

	middleware.SuccessResponse(c, response)
}

// 请求和响应结构体

// AddJobRequest 添加任务请求
type AddJobRequest struct {
	Name        string `json:"name" binding:"required"`
	Spec        string `json:"spec" binding:"required"`
	Type        string `json:"type" binding:"required"`
	Description string `json:"description"`
}

// SchedulerStatusResponse 调度器状态响应
type SchedulerStatusResponse struct {
	IsRunning bool   `json:"is_running"`
	Timezone  string `json:"timezone"`
	JobCount  int    `json:"job_count"`
}

// ValidateCronSpecRequest 验证Cron表达式请求
type ValidateCronSpecRequest struct {
	Spec string `json:"spec" binding:"required"`
}

// ValidateCronSpecResponse 验证Cron表达式响应
type ValidateCronSpecResponse struct {
	Valid bool   `json:"valid"`
	Spec  string `json:"spec"`
	Error string `json:"error,omitempty"`
}
