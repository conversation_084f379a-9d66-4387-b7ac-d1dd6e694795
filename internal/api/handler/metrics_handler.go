package handler

import (
	"magnet-downloader/pkg/metrics"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// MetricsHandler 指标处理器
type MetricsHandler struct {
	collector *metrics.Collector
}

// NewMetricsHandler 创建指标处理器
func NewMetricsHandler(collector *metrics.Collector) *MetricsHandler {
	return &MetricsHandler{
		collector: collector,
	}
}

// PrometheusMetrics Prometheus指标端点
// @Summary Prometheus指标
// @Description 提供Prometheus格式的指标数据
// @Tags metrics
// @Produce text/plain
// @Success 200 {string} string "Prometheus metrics"
// @Router /metrics [get]
func (h *MetricsHandler) PrometheusMetrics() gin.HandlerFunc {
	return gin.WrapH(promhttp.Handler())
}

// GetMetricsInfo 获取指标信息
// @Summary 获取指标信息
// @Description 获取系统指标的基本信息和统计
// @Tags metrics
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=MetricsInfoResponse}
// @Router /api/v1/metrics/info [get]
func (h *MetricsHandler) GetMetricsInfo(c *gin.Context) {
	info := MetricsInfoResponse{
		Available: true,
		Endpoint:  "/metrics",
		Format:    "prometheus",
		Categories: []string{
			"http_requests",
			"tasks",
			"users",
			"websocket",
			"system",
			"database",
			"redis",
			"business",
		},
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    info,
	})
}

// MetricsInfoResponse 指标信息响应
type MetricsInfoResponse struct {
	Available  bool     `json:"available"`
	Endpoint   string   `json:"endpoint"`
	Format     string   `json:"format"`
	Categories []string `json:"categories"`
}
