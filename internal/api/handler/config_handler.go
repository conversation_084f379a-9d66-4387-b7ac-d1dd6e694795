package handler

import (
	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/model"
	"magnet-downloader/internal/service"

	"github.com/gin-gonic/gin"
)

// ConfigHandler 配置处理器
type ConfigHandler struct {
	configService service.ConfigService
}

// NewConfigHandler 创建配置处理器
func NewConfigHandler(configService service.ConfigService) *ConfigHandler {
	return &ConfigHandler{
		configService: configService,
	}
}

// GetConfig 获取配置
// @Summary 获取配置
// @Description 根据配置键获取配置值
// @Tags config
// @Produce json
// @Param key path string true "配置键"
// @Success 200 {object} middleware.APIResponse{data=model.SystemConfig}
// @Failure 400 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/config/{key} [get]
func (h *ConfigHandler) GetConfig(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		middleware.BadRequestResponse(c, "config key is required", nil)
		return
	}

	config, err := h.configService.GetConfig(key)
	if err != nil {
		middleware.NotFoundResponse(c, "config not found")
		return
	}

	middleware.SuccessResponse(c, config)
}

// SetConfig 设置配置（管理员）
// @Summary 设置配置
// @Description 设置系统配置（仅管理员）
// @Tags admin
// @Accept json
// @Produce json
// @Param key path string true "配置键"
// @Param request body map[string]interface{} true "配置值"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/config/{key} [put]
func (h *ConfigHandler) SetConfig(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		middleware.BadRequestResponse(c, "config key is required", nil)
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	value, exists := req["value"]
	if !exists {
		middleware.BadRequestResponse(c, "config value is required", nil)
		return
	}

	description, _ := req["description"].(string)

	if err := h.configService.SetConfig(key, value, description); err != nil {
		middleware.BadRequestResponse(c, "failed to set config", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": "config set successfully"})
}

// DeleteConfig 删除配置（管理员）
// @Summary 删除配置
// @Description 删除系统配置（仅管理员）
// @Tags admin
// @Produce json
// @Param key path string true "配置键"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/config/{key} [delete]
func (h *ConfigHandler) DeleteConfig(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		middleware.BadRequestResponse(c, "config key is required", nil)
		return
	}

	if err := h.configService.DeleteConfig(key); err != nil {
		middleware.BadRequestResponse(c, "failed to delete config", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": "config deleted successfully"})
}

// ListConfigs 获取配置列表
// @Summary 获取配置列表
// @Description 获取配置列表，支持分页和过滤
// @Tags config
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param category query string false "配置分类"
// @Param is_public query bool false "是否公开"
// @Param search query string false "搜索关键词"
// @Success 200 {object} middleware.APIResponse{data=middleware.PaginatedResponse}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/config [get]
func (h *ConfigHandler) ListConfigs(c *gin.Context) {
	var req service.ListConfigsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 非管理员只能查看公开配置
	if !middleware.IsAdmin(c) {
		isPublic := true
		req.IsPublic = &isPublic
	}

	configs, total, err := h.configService.ListConfigs(&req)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "failed to get configs", err.Error())
		return
	}

	middleware.PaginatedSuccessResponse(c, configs, total, req.Page, req.PageSize)
}

// GetConfigsByCategory 根据分类获取配置
// @Summary 根据分类获取配置
// @Description 根据分类获取配置列表
// @Tags config
// @Produce json
// @Param category path string true "配置分类"
// @Success 200 {object} middleware.APIResponse{data=[]model.SystemConfig}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/config/category/{category} [get]
func (h *ConfigHandler) GetConfigsByCategory(c *gin.Context) {
	category := c.Param("category")
	if category == "" {
		middleware.BadRequestResponse(c, "category is required", nil)
		return
	}

	configs, err := h.configService.GetConfigsByCategory(category)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "failed to get configs", err.Error())
		return
	}

	// 非管理员只能查看公开配置
	if !middleware.IsAdmin(c) {
		var publicConfigs []*model.SystemConfig
		for _, config := range configs {
			if config.IsPublic {
				publicConfigs = append(publicConfigs, config)
			}
		}
		configs = publicConfigs
	}

	middleware.SuccessResponse(c, configs)
}

// GetPublicConfigs 获取公开配置
// @Summary 获取公开配置
// @Description 获取所有公开的系统配置
// @Tags config
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=[]model.SystemConfig}
// @Failure 500 {object} middleware.APIResponse
// @Router /api/v1/config/public [get]
func (h *ConfigHandler) GetPublicConfigs(c *gin.Context) {
	configs, err := h.configService.GetPublicConfigs()
	if err != nil {
		middleware.InternalServerErrorResponse(c, "failed to get public configs", err.Error())
		return
	}

	middleware.SuccessResponse(c, configs)
}

// BatchSetConfigs 批量设置配置（管理员）
// @Summary 批量设置配置
// @Description 批量设置系统配置（仅管理员）
// @Tags admin
// @Accept json
// @Produce json
// @Param request body map[string]interface{} true "配置键值对"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/config/batch [put]
func (h *ConfigHandler) BatchSetConfigs(c *gin.Context) {
	var configs map[string]interface{}
	if err := c.ShouldBindJSON(&configs); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	if len(configs) == 0 {
		middleware.BadRequestResponse(c, "no configs provided", nil)
		return
	}

	if err := h.configService.BatchSetConfigs(configs); err != nil {
		middleware.BadRequestResponse(c, "failed to batch set configs", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{
		"message": "configs set successfully",
		"count":   len(configs),
	})
}

// BatchDeleteConfigs 批量删除配置（管理员）
// @Summary 批量删除配置
// @Description 批量删除系统配置（仅管理员）
// @Tags admin
// @Accept json
// @Produce json
// @Param request body map[string][]string true "配置键列表"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/config/batch [delete]
func (h *ConfigHandler) BatchDeleteConfigs(c *gin.Context) {
	var req map[string][]string
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	keys, exists := req["keys"]
	if !exists || len(keys) == 0 {
		middleware.BadRequestResponse(c, "no config keys provided", nil)
		return
	}

	if err := h.configService.BatchDeleteConfigs(keys); err != nil {
		middleware.BadRequestResponse(c, "failed to batch delete configs", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{
		"message": "configs deleted successfully",
		"count":   len(keys),
	})
}

// RefreshConfigCache 刷新配置缓存（管理员）
// @Summary 刷新配置缓存
// @Description 刷新系统配置缓存（仅管理员）
// @Tags admin
// @Produce json
// @Success 200 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/config/cache/refresh [post]
func (h *ConfigHandler) RefreshConfigCache(c *gin.Context) {
	if err := h.configService.RefreshConfigCache(); err != nil {
		middleware.InternalServerErrorResponse(c, "failed to refresh config cache", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": "config cache refreshed successfully"})
}

// TestDoodStreamConnection 测试DoodStream连接
// @Summary 测试DoodStream连接
// @Description 测试DoodStream API连接（仅管理员）
// @Tags admin
// @Accept json
// @Produce json
// @Param config body object true "DoodStream配置"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/test/doodstream [post]
func (h *ConfigHandler) TestDoodStreamConnection(c *gin.Context) {
	var config struct {
		APIKey     string `json:"api_key" binding:"required"`
		BaseURL    string `json:"base_url"`
		Timeout    int    `json:"timeout"`
		MaxRetries int    `json:"max_retries"`
	}

	if err := c.ShouldBindJSON(&config); err != nil {
		middleware.BadRequestResponse(c, "invalid request body", err.Error())
		return
	}

	// 设置默认值
	if config.BaseURL == "" {
		config.BaseURL = "https://doodapi.co"
	}
	if config.Timeout == 0 {
		config.Timeout = 300
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}

	// 调用配置服务测试连接
	if err := h.configService.TestDoodStreamConnection(config.APIKey, config.BaseURL, config.Timeout, config.MaxRetries); err != nil {
		middleware.BadRequestResponse(c, "DoodStream connection test failed", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": "DoodStream connection test successful"})
}

// TestImgBBConnection 测试ImgBB连接
// @Summary 测试ImgBB连接
// @Description 测试ImgBB服务连接（仅管理员）
// @Tags admin
// @Accept json
// @Produce json
// @Param config body object true "ImgBB配置"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/test/imgbb [post]
func (h *ConfigHandler) TestImgBBConnection(c *gin.Context) {
	var config struct {
		BaseURL string `json:"base_url"`
		Timeout int    `json:"timeout"`
	}

	if err := c.ShouldBindJSON(&config); err != nil {
		middleware.BadRequestResponse(c, "invalid request body", err.Error())
		return
	}

	// 设置默认值
	if config.BaseURL == "" {
		config.BaseURL = "http://localhost:3000"
	}
	if config.Timeout == 0 {
		config.Timeout = 30
	}

	// 调用配置服务测试连接
	if err := h.configService.TestImgBBConnection(config.BaseURL, config.Timeout); err != nil {
		middleware.BadRequestResponse(c, "ImgBB connection test failed", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": "ImgBB connection test successful"})
}
