package handler

import (
	"strconv"

	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/service"

	"github.com/gin-gonic/gin"
)

// TaskHandler 任务处理器
type TaskHandler struct {
	taskService service.TaskService
}

// NewTaskHandler 创建任务处理器
func NewTaskHandler(taskService service.TaskService) *TaskHandler {
	return &TaskHandler{
		taskService: taskService,
	}
}

// CreateTask 创建任务
// @Summary 创建下载任务
// @Description 创建新的磁力链接下载任务
// @Tags tasks
// @Accept json
// @Produce json
// @Param request body service.CreateTaskRequest true "创建任务请求"
// @Success 201 {object} middleware.APIResponse{data=model.DownloadTask}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks [post]
func (h *TaskHandler) CreateTask(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		middleware.UnauthorizedResponse(c, "user not authenticated")
		return
	}

	var req service.CreateTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	task, err := h.taskService.CreateTask(userID, &req)
	if err != nil {
		middleware.BadRequestResponse(c, "failed to create task", err.Error())
		return
	}

	middleware.CreatedResponse(c, task)
}

// GetTask 获取任务详情
// @Summary 获取任务详情
// @Description 根据任务ID获取任务详细信息
// @Tags tasks
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} middleware.APIResponse{data=model.DownloadTask}
// @Failure 400 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks/{id} [get]
func (h *TaskHandler) GetTask(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "invalid task ID", err.Error())
		return
	}

	task, err := h.taskService.GetTask(uint(taskID))
	if err != nil {
		middleware.NotFoundResponse(c, "task not found")
		return
	}

	// 检查权限：用户只能查看自己的任务，管理员可以查看所有任务
	userID, _ := middleware.GetCurrentUserID(c)
	if !middleware.IsAdmin(c) && task.UserID != userID {
		middleware.ForbiddenResponse(c, "access denied")
		return
	}

	middleware.SuccessResponse(c, task)
}

// UpdateTask 更新任务
// @Summary 更新任务
// @Description 更新任务信息
// @Tags tasks
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Param request body service.UpdateTaskRequest true "更新任务请求"
// @Success 200 {object} middleware.APIResponse{data=model.DownloadTask}
// @Failure 400 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks/{id} [put]
func (h *TaskHandler) UpdateTask(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "invalid task ID", err.Error())
		return
	}

	// 检查任务是否存在和权限
	task, err := h.taskService.GetTask(uint(taskID))
	if err != nil {
		middleware.NotFoundResponse(c, "task not found")
		return
	}

	userID, _ := middleware.GetCurrentUserID(c)
	if !middleware.IsAdmin(c) && task.UserID != userID {
		middleware.ForbiddenResponse(c, "access denied")
		return
	}

	var req service.UpdateTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	updatedTask, err := h.taskService.UpdateTask(uint(taskID), &req)
	if err != nil {
		middleware.BadRequestResponse(c, "failed to update task", err.Error())
		return
	}

	middleware.SuccessResponse(c, updatedTask)
}

// DeleteTask 删除任务
// @Summary 删除任务
// @Description 删除指定的任务
// @Tags tasks
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks/{id} [delete]
func (h *TaskHandler) DeleteTask(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "invalid task ID", err.Error())
		return
	}

	// 检查任务是否存在和权限
	task, err := h.taskService.GetTask(uint(taskID))
	if err != nil {
		middleware.NotFoundResponse(c, "task not found")
		return
	}

	userID, _ := middleware.GetCurrentUserID(c)
	if !middleware.IsAdmin(c) && task.UserID != userID {
		middleware.ForbiddenResponse(c, "access denied")
		return
	}

	if err := h.taskService.DeleteTask(uint(taskID)); err != nil {
		middleware.BadRequestResponse(c, "failed to delete task", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": "task deleted successfully"})
}

// ListTasks 获取任务列表
// @Summary 获取任务列表
// @Description 获取任务列表，支持分页和过滤
// @Tags tasks
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param status query string false "任务状态"
// @Param priority query int false "任务优先级"
// @Param search query string false "搜索关键词"
// @Success 200 {object} middleware.APIResponse{data=middleware.PaginatedResponse}
// @Failure 400 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks [get]
func (h *TaskHandler) ListTasks(c *gin.Context) {
	var req service.ListTasksRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 非管理员只能查看自己的任务
	if !middleware.IsAdmin(c) {
		userID, _ := middleware.GetCurrentUserID(c)
		tasks, total, err := h.taskService.GetUserTasks(userID, &req)
		if err != nil {
			middleware.InternalServerErrorResponse(c, "failed to get tasks", err.Error())
			return
		}
		middleware.PaginatedSuccessResponse(c, tasks, total, req.Page, req.PageSize)
		return
	}

	// 管理员可以查看所有任务
	tasks, total, err := h.taskService.ListTasks(&req)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "failed to get tasks", err.Error())
		return
	}

	middleware.PaginatedSuccessResponse(c, tasks, total, req.Page, req.PageSize)
}

// StartTask 开始任务
// @Summary 开始任务
// @Description 开始指定的下载任务
// @Tags tasks
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks/{id}/start [post]
func (h *TaskHandler) StartTask(c *gin.Context) {
	h.handleTaskAction(c, "start", h.taskService.StartTask)
}

// PauseTask 暂停任务
// @Summary 暂停任务
// @Description 暂停指定的下载任务
// @Tags tasks
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks/{id}/pause [post]
func (h *TaskHandler) PauseTask(c *gin.Context) {
	h.handleTaskAction(c, "pause", h.taskService.PauseTask)
}

// ResumeTask 恢复任务
// @Summary 恢复任务
// @Description 恢复指定的下载任务
// @Tags tasks
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks/{id}/resume [post]
func (h *TaskHandler) ResumeTask(c *gin.Context) {
	h.handleTaskAction(c, "resume", h.taskService.ResumeTask)
}

// CancelTask 取消任务
// @Summary 取消任务
// @Description 取消指定的下载任务
// @Tags tasks
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks/{id}/cancel [post]
func (h *TaskHandler) CancelTask(c *gin.Context) {
	h.handleTaskAction(c, "cancel", h.taskService.CancelTask)
}

// RetryTask 重试任务
// @Summary 重试任务
// @Description 重试失败的下载任务
// @Tags tasks
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks/{id}/retry [post]
func (h *TaskHandler) RetryTask(c *gin.Context) {
	h.handleTaskAction(c, "retry", h.taskService.RetryTask)
}

// GetTaskStats 获取任务统计
// @Summary 获取任务统计
// @Description 获取任务统计信息
// @Tags tasks
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=service.TaskStats}
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/tasks/stats [get]
func (h *TaskHandler) GetTaskStats(c *gin.Context) {
	stats, err := h.taskService.GetTaskStats()
	if err != nil {
		middleware.InternalServerErrorResponse(c, "failed to get task stats", err.Error())
		return
	}

	middleware.SuccessResponse(c, stats)
}

// handleTaskAction 处理任务操作的通用方法
func (h *TaskHandler) handleTaskAction(c *gin.Context, action string, actionFunc func(uint) error) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "invalid task ID", err.Error())
		return
	}

	// 检查任务是否存在和权限
	task, err := h.taskService.GetTask(uint(taskID))
	if err != nil {
		middleware.NotFoundResponse(c, "task not found")
		return
	}

	userID, _ := middleware.GetCurrentUserID(c)
	if !middleware.IsAdmin(c) && task.UserID != userID {
		middleware.ForbiddenResponse(c, "access denied")
		return
	}

	if err := actionFunc(uint(taskID)); err != nil {
		middleware.BadRequestResponse(c, "failed to "+action+" task", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": action + " task successfully"})
}
