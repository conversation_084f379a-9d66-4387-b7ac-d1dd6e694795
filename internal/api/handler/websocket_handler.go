package handler

import (
	"fmt"
	"strconv"

	"magnet-downloader/internal/api/middleware"
	ws "magnet-downloader/internal/websocket"

	"github.com/gin-gonic/gin"
)

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	hub *ws.Hub
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(hub *ws.Hub) *WebSocketHandler {
	return &WebSocketHandler{
		hub: hub,
	}
}

// HandleWebSocket 处理WebSocket连接
// @Summary WebSocket连接
// @Description 建立WebSocket连接，用于实时通信
// @Tags websocket
// @Param Authorization header string true "Bearer token"
// @Success 101 {string} string "Switching Protocols"
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/ws [get]
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// 获取用户信息
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		middleware.UnauthorizedResponse(c, "user not authenticated")
		return
	}

	username, _ := middleware.GetCurrentUsername(c)
	role, _ := middleware.GetCurrentUserRole(c)

	// 升级到WebSocket连接
	ws.ServeWS(h.hub, c.Writer, c.Request, userID, username, role)
}

// GetWebSocketStats 获取WebSocket统计信息
// @Summary 获取WebSocket统计信息
// @Description 获取WebSocket连接和消息统计信息（仅管理员）
// @Tags websocket
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=ws.HubStats}
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/websocket/stats [get]
func (h *WebSocketHandler) GetWebSocketStats(c *gin.Context) {
	stats := h.hub.GetStats()
	middleware.SuccessResponse(c, stats)
}

// GetWebSocketClients 获取WebSocket客户端列表
// @Summary 获取WebSocket客户端列表
// @Description 获取当前连接的WebSocket客户端信息（仅管理员）
// @Tags websocket
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=[]map[string]interface{}}
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/websocket/clients [get]
func (h *WebSocketHandler) GetWebSocketClients(c *gin.Context) {
	clients := h.hub.GetClients()
	middleware.SuccessResponse(c, clients)
}

// SendBroadcastMessage 发送广播消息
// @Summary 发送广播消息
// @Description 向所有连接的客户端发送广播消息（仅管理员）
// @Tags websocket
// @Accept json
// @Produce json
// @Param request body BroadcastMessageRequest true "广播消息请求"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/websocket/broadcast [post]
func (h *WebSocketHandler) SendBroadcastMessage(c *gin.Context) {
	var req BroadcastMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 创建系统通知消息
	msg := ws.NewSystemNotificationMessage(req.Title, req.Message, req.Level)

	// 广播消息
	h.hub.Broadcast(msg)

	middleware.SuccessResponse(c, gin.H{
		"message": "broadcast message sent successfully",
		"type":    msg.Type,
		"id":      msg.ID,
	})
}

// SendUserMessage 发送用户消息
// @Summary 发送用户消息
// @Description 向指定用户发送消息（仅管理员）
// @Tags websocket
// @Accept json
// @Produce json
// @Param request body UserMessageRequest true "用户消息请求"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/websocket/user-message [post]
func (h *WebSocketHandler) SendUserMessage(c *gin.Context) {
	var req UserMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 检查用户是否在线
	if !h.hub.IsUserOnline(req.UserID) {
		middleware.BadRequestResponse(c, "user is not online", nil)
		return
	}

	// 创建系统通知消息
	msg := ws.NewSystemNotificationMessage(req.Title, req.Message, req.Level)

	// 发送给指定用户
	h.hub.SendToUser(req.UserID, msg)

	middleware.SuccessResponse(c, gin.H{
		"message": "user message sent successfully",
		"user_id": req.UserID,
		"type":    msg.Type,
		"id":      msg.ID,
	})
}

// GetOnlineUsers 获取在线用户
// @Summary 获取在线用户
// @Description 获取当前在线的用户列表（仅管理员）
// @Tags websocket
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=OnlineUsersResponse}
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/websocket/online-users [get]
func (h *WebSocketHandler) GetOnlineUsers(c *gin.Context) {
	clients := h.hub.GetClients()

	// 按用户ID分组
	userMap := make(map[uint]map[string]interface{})
	for _, client := range clients {
		userID := uint(client["user_id"].(uint))
		if _, exists := userMap[userID]; !exists {
			userMap[userID] = map[string]interface{}{
				"user_id":        client["user_id"],
				"username":       client["username"],
				"role":           client["role"],
				"connected_at":   client["connected_at"],
				"last_active_at": client["last_active_at"],
				"connections":    1,
			}
		} else {
			userMap[userID]["connections"] = userMap[userID]["connections"].(int) + 1
		}
	}

	// 转换为数组
	users := make([]map[string]interface{}, 0, len(userMap))
	for _, user := range userMap {
		users = append(users, user)
	}

	response := OnlineUsersResponse{
		Users: users,
		Total: len(users),
		Stats: h.hub.GetStats(),
	}

	middleware.SuccessResponse(c, response)
}

// CheckUserOnline 检查用户是否在线
// @Summary 检查用户是否在线
// @Description 检查指定用户是否在线
// @Tags websocket
// @Produce json
// @Param user_id path int true "用户ID"
// @Success 200 {object} middleware.APIResponse{data=UserOnlineResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/websocket/user/{user_id}/online [get]
func (h *WebSocketHandler) CheckUserOnline(c *gin.Context) {
	userID, err := parseUintParam(c, "user_id")
	if err != nil {
		middleware.BadRequestResponse(c, "invalid user ID", err.Error())
		return
	}

	// 检查权限：用户只能查看自己的在线状态，管理员可以查看所有用户
	currentUserID, _ := middleware.GetCurrentUserID(c)
	if !middleware.IsAdmin(c) && currentUserID != userID {
		middleware.ForbiddenResponse(c, "access denied")
		return
	}

	isOnline := h.hub.IsUserOnline(userID)
	clients := h.hub.GetUserClients(userID)

	response := UserOnlineResponse{
		UserID:      userID,
		IsOnline:    isOnline,
		Connections: len(clients),
	}

	middleware.SuccessResponse(c, response)
}

// 请求和响应结构体

// BroadcastMessageRequest 广播消息请求
type BroadcastMessageRequest struct {
	Title   string `json:"title" binding:"required"`
	Message string `json:"message" binding:"required"`
	Level   string `json:"level" binding:"required,oneof=info warning error"`
}

// UserMessageRequest 用户消息请求
type UserMessageRequest struct {
	UserID  uint   `json:"user_id" binding:"required"`
	Title   string `json:"title" binding:"required"`
	Message string `json:"message" binding:"required"`
	Level   string `json:"level" binding:"required,oneof=info warning error"`
}

// OnlineUsersResponse 在线用户响应
type OnlineUsersResponse struct {
	Users []map[string]interface{} `json:"users"`
	Total int                      `json:"total"`
	Stats *ws.HubStats             `json:"stats"`
}

// UserOnlineResponse 用户在线状态响应
type UserOnlineResponse struct {
	UserID      uint `json:"user_id"`
	IsOnline    bool `json:"is_online"`
	Connections int  `json:"connections"`
}

// parseUintParam 解析uint参数
func parseUintParam(c *gin.Context, param string) (uint, error) {
	value := c.Param(param)
	if value == "" {
		return 0, fmt.Errorf("parameter %s is required", param)
	}

	id, err := strconv.ParseUint(value, 10, 32)
	if err != nil {
		return 0, fmt.Errorf("invalid %s format", param)
	}

	return uint(id), nil
}
