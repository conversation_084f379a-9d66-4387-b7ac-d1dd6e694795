package handler

import (
	"net/http"
	"strconv"

	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/service"

	"github.com/gin-gonic/gin"
)

// AutoUploadHandler 自动上传处理器
type AutoUploadHandler struct {
	autoUploadService service.AutoUploadService
}

// NewAutoUploadHandler 创建自动上传处理器
func NewAutoUploadHandler(autoUploadService service.AutoUploadService) *AutoUploadHandler {
	return &AutoUploadHandler{
		autoUploadService: autoUploadService,
	}
}

// GetStatus 获取自动上传服务状态
// @Summary 获取自动上传服务状态
// @Description 获取自动上传服务的运行状态和统计信息
// @Tags auto-upload
// @Accept json
// @Produce json
// @Success 200 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/auto-upload/status [get]
func (h *AutoUploadHandler) GetStatus(c *gin.Context) {
	if h.autoUploadService == nil {
		middleware.ErrorResponse(c, http.StatusServiceUnavailable, "自动上传服务未启用", "auto upload service not enabled")
		return
	}

	status := h.autoUploadService.GetStatus()
	middleware.SuccessResponse(c, status)
}

// GetStats 获取自动上传统计
// @Summary 获取自动上传统计
// @Description 获取自动上传的详细统计信息
// @Tags auto-upload
// @Accept json
// @Produce json
// @Success 200 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/auto-upload/stats [get]
func (h *AutoUploadHandler) GetStats(c *gin.Context) {
	if h.autoUploadService == nil {
		middleware.ErrorResponse(c, http.StatusServiceUnavailable, "自动上传服务未启用", "auto upload service not enabled")
		return
	}

	stats := h.autoUploadService.GetStats()
	middleware.SuccessResponse(c, stats)
}

// TriggerScan 手动触发扫描
// @Summary 手动触发扫描
// @Description 手动触发视频文件扫描和上传
// @Tags auto-upload
// @Accept json
// @Produce json
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/auto-upload/scan [post]
func (h *AutoUploadHandler) TriggerScan(c *gin.Context) {
	if h.autoUploadService == nil {
		middleware.ErrorResponse(c, http.StatusServiceUnavailable, "自动上传服务未启用", "auto upload service not enabled")
		return
	}

	if err := h.autoUploadService.TriggerScan(); err != nil {
		middleware.ErrorResponse(c, http.StatusBadRequest, "触发扫描失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{
		"message": "扫描已触发",
		"status":  "scanning",
	})
}

// StartService 启动自动上传服务 (管理员)
// @Summary 启动自动上传服务
// @Description 启动自动上传服务（仅管理员）
// @Tags admin
// @Accept json
// @Produce json
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/auto-upload/start [post]
func (h *AutoUploadHandler) StartService(c *gin.Context) {
	if h.autoUploadService == nil {
		middleware.ErrorResponse(c, http.StatusServiceUnavailable, "自动上传服务未启用", "auto upload service not enabled")
		return
	}

	if err := h.autoUploadService.Start(c.Request.Context()); err != nil {
		middleware.ErrorResponse(c, http.StatusBadRequest, "启动服务失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{
		"message": "自动上传服务已启动",
		"status":  "running",
	})
}

// StopService 停止自动上传服务 (管理员)
// @Summary 停止自动上传服务
// @Description 停止自动上传服务（仅管理员）
// @Tags admin
// @Accept json
// @Produce json
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/auto-upload/stop [post]
func (h *AutoUploadHandler) StopService(c *gin.Context) {
	if h.autoUploadService == nil {
		middleware.ErrorResponse(c, http.StatusServiceUnavailable, "自动上传服务未启用", "auto upload service not enabled")
		return
	}

	if err := h.autoUploadService.Stop(); err != nil {
		middleware.ErrorResponse(c, http.StatusBadRequest, "停止服务失败", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{
		"message": "自动上传服务已停止",
		"status":  "stopped",
	})
}

// GetServiceInfo 获取服务信息
// @Summary 获取自动上传服务信息
// @Description 获取自动上传服务的配置和状态信息
// @Tags auto-upload
// @Accept json
// @Produce json
// @Success 200 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/auto-upload/info [get]
func (h *AutoUploadHandler) GetServiceInfo(c *gin.Context) {
	if h.autoUploadService == nil {
		middleware.SuccessResponse(c, gin.H{
			"enabled": false,
			"message": "自动上传服务未启用",
		})
		return
	}

	status := h.autoUploadService.GetStatus()
	stats := h.autoUploadService.GetStats()

	info := gin.H{
		"enabled":     true,
		"status":      status,
		"stats":       stats,
		"description": "自动扫描下载目录并上传视频到DoodStream",
		"features": []string{
			"智能视频文件选择",
			"分段文件支持",
			"并发上传控制",
			"实时进度监控",
			"错误处理和重试",
		},
	}

	middleware.SuccessResponse(c, info)
}

// GetUploadQueue 获取上传队列
// @Summary 获取上传队列
// @Description 获取当前的上传队列状态
// @Tags auto-upload
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/auto-upload/queue [get]
func (h *AutoUploadHandler) GetUploadQueue(c *gin.Context) {
	if h.autoUploadService == nil {
		middleware.ErrorResponse(c, http.StatusServiceUnavailable, "自动上传服务未启用", "auto upload service not enabled")
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// 获取状态信息（这里简化处理，实际应该从服务中获取队列详情）
	status := h.autoUploadService.GetStatus()

	result := gin.H{
		"pending_uploads": status.PendingUploads,
		"active_uploads":  status.ActiveUploads,
		"page":           page,
		"limit":          limit,
		"message":        "队列状态获取成功",
	}

	middleware.SuccessResponse(c, result)
}

// GetUploadHistory 获取上传历史
// @Summary 获取上传历史
// @Description 获取最近的上传历史记录
// @Tags auto-upload
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param status query string false "状态过滤" Enums(completed,failed,all)
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/auto-upload/history [get]
func (h *AutoUploadHandler) GetUploadHistory(c *gin.Context) {
	if h.autoUploadService == nil {
		middleware.ErrorResponse(c, http.StatusServiceUnavailable, "自动上传服务未启用", "auto upload service not enabled")
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	statusFilter := c.DefaultQuery("status", "all")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// 这里应该从数据库查询上传历史
	// 暂时返回统计信息
	stats := h.autoUploadService.GetStats()

	result := gin.H{
		"total_uploads":     stats.TotalUploads,
		"successful_uploads": stats.SuccessfulUploads,
		"failed_uploads":    stats.FailedUploads,
		"success_rate":      stats.UploadSuccessRate,
		"last_upload_time":  stats.LastUploadTime,
		"page":             page,
		"limit":            limit,
		"status_filter":    statusFilter,
		"message":          "上传历史获取成功",
	}

	middleware.SuccessResponse(c, result)
}