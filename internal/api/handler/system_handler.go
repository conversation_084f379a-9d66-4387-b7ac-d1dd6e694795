package handler

import (
	"runtime"
	"time"

	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/service"

	"github.com/gin-gonic/gin"
)

// SystemHandler 系统处理器
type SystemHandler struct {
	services *service.Services
}

// NewSystemHandler 创建系统处理器
func NewSystemHandler(services *service.Services) *SystemHandler {
	return &SystemHandler{
		services: services,
	}
}

// SystemInfo 系统信息
type SystemInfo struct {
	Version     string          `json:"version"`
	BuildTime   string          `json:"build_time"`
	GoVersion   string          `json:"go_version"`
	OS          string          `json:"os"`
	Arch        string          `json:"arch"`
	StartTime   time.Time       `json:"start_time"`
	Uptime      string          `json:"uptime"`
	MemoryUsage MemoryUsage     `json:"memory_usage"`
	Health      map[string]bool `json:"health"`
}

// MemoryUsage 内存使用情况
type MemoryUsage struct {
	Alloc      uint64 `json:"alloc"`       // 当前分配的内存
	TotalAlloc uint64 `json:"total_alloc"` // 总分配的内存
	Sys        uint64 `json:"sys"`         // 系统内存
	NumGC      uint32 `json:"num_gc"`      // GC次数
}

var startTime = time.Now()

// GetSystemInfo 获取系统信息
// @Summary 获取系统信息
// @Description 获取系统运行状态和基本信息
// @Tags system
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=SystemInfo}
// @Security BearerAuth
// @Router /api/v1/system/info [get]
func (h *SystemHandler) GetSystemInfo(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	uptime := time.Since(startTime)

	info := SystemInfo{
		Version:   "1.0.0",      // 从配置或构建信息获取
		BuildTime: "2025-06-23", // 从构建信息获取
		GoVersion: runtime.Version(),
		OS:        runtime.GOOS,
		Arch:      runtime.GOARCH,
		StartTime: startTime,
		Uptime:    uptime.String(),
		MemoryUsage: MemoryUsage{
			Alloc:      m.Alloc,
			TotalAlloc: m.TotalAlloc,
			Sys:        m.Sys,
			NumGC:      m.NumGC,
		},
		Health: h.services.HealthCheck(),
	}

	middleware.SuccessResponse(c, info)
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查系统各组件的健康状态
// @Tags system
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=map[string]bool}
// @Router /api/v1/system/health [get]
func (h *SystemHandler) HealthCheck(c *gin.Context) {
	health := h.services.HealthCheck()

	// 判断整体健康状态
	allHealthy := true
	for _, healthy := range health {
		if !healthy {
			allHealthy = false
			break
		}
	}

	if allHealthy {
		middleware.SuccessResponse(c, health)
	} else {
		middleware.ErrorResponse(c, 503, "service unavailable", health)
	}
}

// GetStats 获取统计信息
// @Summary 获取统计信息
// @Description 获取系统统计信息
// @Tags system
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=map[string]interface{}}
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/system/stats [get]
func (h *SystemHandler) GetStats(c *gin.Context) {
	stats := make(map[string]interface{})

	// 获取任务统计
	if taskStats, err := h.services.Task.GetTaskStats(); err == nil {
		stats["tasks"] = taskStats
	}

	// 获取用户统计
	if userStats, err := h.services.User.GetUserStats(); err == nil {
		stats["users"] = userStats
	}

	// 获取系统运行时统计
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	stats["runtime"] = map[string]interface{}{
		"goroutines":   runtime.NumGoroutine(),
		"memory_alloc": m.Alloc,
		"memory_sys":   m.Sys,
		"gc_count":     m.NumGC,
		"uptime":       time.Since(startTime).Seconds(),
	}

	middleware.SuccessResponse(c, stats)
}

// GetVersion 获取版本信息
// @Summary 获取版本信息
// @Description 获取应用版本信息
// @Tags system
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=map[string]string}
// @Router /api/v1/system/version [get]
func (h *SystemHandler) GetVersion(c *gin.Context) {
	version := map[string]string{
		"version":    "1.0.0",
		"build_time": "2025-06-23",
		"go_version": runtime.Version(),
		"git_commit": "unknown", // 从构建信息获取
	}

	middleware.SuccessResponse(c, version)
}

// Ping 简单的ping接口
// @Summary Ping
// @Description 简单的ping接口，用于检查服务是否可用
// @Tags system
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=map[string]string}
// @Router /api/v1/ping [get]
func (h *SystemHandler) Ping(c *gin.Context) {
	middleware.SuccessResponse(c, gin.H{
		"message":   "pong",
		"timestamp": time.Now().Unix(),
	})
}
