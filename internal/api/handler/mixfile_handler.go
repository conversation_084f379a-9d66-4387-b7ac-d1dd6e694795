package handler

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/mixfile"

	"github.com/gin-gonic/gin"
)

// 响应辅助函数
var resp = struct{}{}

func responseSuccess(c *gin.Context, message string, data interface{}) {
	c.JSO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": message,
		"data":    data,
	})
}

func responseError(c *gin.Context, status int, message string, details string) {
	c.<PERSON><PERSON>(status, gin.H{
		"success": false,
		"message": message,
		"error":   details,
	})
}

// MixFileHandler MixFile处理器
type MixFileHandler struct {
	downloader   mixfile.MixFileDownloader
	rangeHandler *mixfile.MixFileRangeHandler
	downloadDir  string
}

// NewMixFileHandler 创建MixFile处理器
func NewMixFileHandler(downloadDir string) *MixFileHandler {
	downloader := mixfile.NewMixFileDownloader(mixfile.DefaultDownloaderConfig())
	rangeHandler := mixfile.NewMixFileRangeHandler(downloader)

	return &MixFileHandler{
		downloader:   downloader,
		rangeHandler: rangeHandler,
		downloadDir:  downloadDir,
	}
}

// ParseShareCodeRequest 解析分享码请求
type ParseShareCodeRequest struct {
	ShareCode string `json:"share_code" binding:"required"`
}

// DownloadFileRequest 下载文件请求
type DownloadFileRequest struct {
	ShareCode  string `json:"share_code" binding:"required"`
	OutputPath string `json:"output_path,omitempty"`
}

// FileInfoResponse 文件信息响应
type FileInfoResponse struct {
	FileName    string    `json:"filename"`
	FileSize    int64     `json:"filesize"`
	ChunkCount  int       `json:"chunk_count"`
	Algorithm   string    `json:"algorithm"`
	CreatedAt   time.Time `json:"created_at"`
	Description string    `json:"description"`
	Encrypted   bool      `json:"encrypted"`
	ShareCode   string    `json:"share_code"`
}

// DownloadProgressResponse 下载进度响应
type DownloadProgressResponse struct {
	Stage           string  `json:"stage"`
	Progress        float64 `json:"progress"`
	OverallProgress float64 `json:"overall_progress"`
	ChunkIndex      int     `json:"chunk_index"`
	ChunkCount      int     `json:"chunk_count"`
	DownloadedSize  int64   `json:"downloaded_size"`
	TotalSize       int64   `json:"total_size"`
	Speed           int64   `json:"speed"`
	ETA             int64   `json:"eta"`
	Message         string  `json:"message"`
	Completed       bool    `json:"completed"`
	Error           string  `json:"error,omitempty"`
}

// ParseShareCode 解析分享码
func (h *MixFileHandler) ParseShareCode(c *gin.Context) {
	var req ParseShareCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responseError(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// 获取文件信息
	fileInfo, err := h.downloader.GetFileInfo(req.ShareCode)
	if err != nil {
		responseError(c, http.StatusInternalServerError, "Failed to get file info", err.Error())
		return
	}

	// 构建响应
	resp := &FileInfoResponse{
		FileName:    fileInfo.FileName,
		FileSize:    fileInfo.FileSize,
		ChunkCount:  fileInfo.ChunkCount,
		Algorithm:   fileInfo.Algorithm,
		CreatedAt:   fileInfo.CreatedAt,
		Description: fileInfo.Description,
		Encrypted:   fileInfo.Encrypted,
		ShareCode:   req.ShareCode,
	}

	responseSuccess(c, "Share code parsed successfully", resp)
}

// GetFileInfo 获取文件信息
func (h *MixFileHandler) GetFileInfo(c *gin.Context) {
	shareCode := c.Query("share_code")
	if shareCode == "" {
		responseError(c, http.StatusBadRequest, "Missing share_code parameter", "")
		return
	}

	// 获取文件信息
	fileInfo, err := h.downloader.GetFileInfo(shareCode)
	if err != nil {
		responseError(c, http.StatusBadRequest, "Failed to get file info", err.Error())
		return
	}

	// 构建响应
	resp := &FileInfoResponse{
		FileName:    fileInfo.FileName,
		FileSize:    fileInfo.FileSize,
		ChunkCount:  fileInfo.ChunkCount,
		Algorithm:   fileInfo.Algorithm,
		CreatedAt:   fileInfo.CreatedAt,
		Description: fileInfo.Description,
		Encrypted:   fileInfo.Encrypted,
		ShareCode:   shareCode,
	}

	responseSuccess(c, "File info retrieved successfully", resp)
}

// DownloadFile 下载文件
func (h *MixFileHandler) DownloadFile(c *gin.Context) {
	var req DownloadFileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responseError(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// 获取文件信息
	fileInfo, err := h.downloader.GetFileInfo(req.ShareCode)
	if err != nil {
		responseError(c, http.StatusBadRequest, "Failed to get file info", err.Error())
		return
	}

	// 生成输出路径
	outputPath := req.OutputPath
	if outputPath == "" {
		outputPath = filepath.Join(h.downloadDir, fmt.Sprintf("%d_%s",
			time.Now().Unix(), fileInfo.FileName))
	}

	// 确保下载目录存在
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		responseError(c, http.StatusInternalServerError, "Failed to create download directory", err.Error())
		return
	}

	// 创建进度通道
	progressChan := make(chan *mixfile.DownloadProgress, 100)
	errorChan := make(chan error, 1)

	// 启动下载
	go func() {
		defer close(progressChan)
		defer close(errorChan)

		err := h.downloader.DownloadFile(req.ShareCode, outputPath, func(progress *mixfile.DownloadProgress) {
			select {
			case progressChan <- progress:
			default:
				// 如果通道满了，跳过这个进度更新
			}
		})

		if err != nil {
			errorChan <- err
		}
	}()

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// 发送进度更新
	for {
		select {
		case progress, ok := <-progressChan:
			if !ok {
				// 进度通道关闭，下载完成
				finalResp := &DownloadProgressResponse{
					Stage:           "completed",
					Progress:        100,
					OverallProgress: 100,
					Message:         "下载完成",
					Completed:       true,
				}
				c.SSEvent("progress", finalResp)
				c.Writer.Flush()
				return
			}

			// 发送进度更新
			resp := &DownloadProgressResponse{
				Stage:           progress.Stage,
				Progress:        progress.Progress,
				OverallProgress: progress.OverallProgress,
				ChunkIndex:      progress.ChunkIndex,
				ChunkCount:      progress.ChunkCount,
				DownloadedSize:  progress.DownloadedSize,
				TotalSize:       progress.TotalSize,
				Speed:           progress.Speed,
				ETA:             progress.ETA,
				Message:         progress.Message,
				Completed:       progress.Stage == "completed",
			}

			c.SSEvent("progress", resp)
			c.Writer.Flush()

		case err, ok := <-errorChan:
			if ok && err != nil {
				// 发送错误
				errorResp := &DownloadProgressResponse{
					Stage:     "error",
					Message:   "下载失败",
					Error:     err.Error(),
					Completed: true,
				}
				c.SSEvent("error", errorResp)
				c.Writer.Flush()
				return
			}

		case <-c.Request.Context().Done():
			// 客户端断开连接
			logger.Info("Client disconnected from download progress stream")
			return
		}
	}
}

// StreamFile 流式传输文件（支持Range请求）
func (h *MixFileHandler) StreamFile(c *gin.Context) {
	shareCode := c.Query("share_code")
	if shareCode == "" {
		responseError(c, http.StatusBadRequest, "Missing share_code parameter", "")
		return
	}

	// 验证分享码
	fileInfo, err := h.downloader.GetFileInfo(shareCode)
	if err != nil {
		responseError(c, http.StatusBadRequest, "Invalid share code", err.Error())
		return
	}

	// 设置文件名头
	c.Header("Content-Disposition", fmt.Sprintf("inline; filename=\"%s\"", fileInfo.FileName))

	// 处理Range请求
	if err := h.rangeHandler.HandleRangeRequest(c.Writer, c.Request, shareCode); err != nil {
		logger.Errorf("Failed to handle range request: %v", err)
		if !c.Writer.Written() {
			responseError(c, http.StatusInternalServerError, "Failed to stream file", err.Error())
		}
		return
	}
}

// VerifyFile 验证文件完整性
func (h *MixFileHandler) VerifyFile(c *gin.Context) {
	filePath := c.Query("file_path")
	expectedHash := c.Query("expected_hash")

	if filePath == "" || expectedHash == "" {
		responseError(c, http.StatusBadRequest, "Missing file_path or expected_hash parameter", "")
		return
	}

	// 验证文件完整性
	if err := h.downloader.VerifyFileIntegrity(filePath, expectedHash); err != nil {
		responseError(c, http.StatusBadRequest, "File integrity verification failed", err.Error())
		return
	}

	responseSuccess(c, "File integrity verified successfully", map[string]interface{}{
		"file_path":     filePath,
		"expected_hash": expectedHash,
		"verified":      true,
	})
}

// GetCacheStats 获取缓存统计
func (h *MixFileHandler) GetCacheStats(c *gin.Context) {
	stats := h.rangeHandler.GetCacheStats()
	responseSuccess(c, "Cache stats retrieved successfully", stats)
}

// ClearCache 清理缓存
func (h *MixFileHandler) ClearCache(c *gin.Context) {
	h.rangeHandler.ClearCache()
	responseSuccess(c, "Cache cleared successfully", nil)
}

// GetDownloadHistory 获取下载历史（示例实现）
func (h *MixFileHandler) GetDownloadHistory(c *gin.Context) {
	// 这里可以实现下载历史记录功能
	// 暂时返回空列表
	responseSuccess(c, "Download history retrieved successfully", []interface{}{})
}

// DeleteDownloadedFile 删除已下载的文件
func (h *MixFileHandler) DeleteDownloadedFile(c *gin.Context) {
	filePath := c.Query("file_path")
	if filePath == "" {
		responseError(c, http.StatusBadRequest, "Missing file_path parameter", "")
		return
	}

	// 安全检查：确保文件在下载目录内
	absPath, err := filepath.Abs(filePath)
	if err != nil {
		responseError(c, http.StatusBadRequest, "Invalid file path", err.Error())
		return
	}

	absDownloadDir, err := filepath.Abs(h.downloadDir)
	if err != nil {
		responseError(c, http.StatusInternalServerError, "Invalid download directory", err.Error())
		return
	}

	if !filepath.HasPrefix(absPath, absDownloadDir) {
		responseError(c, http.StatusForbidden, "File path outside download directory", "")
		return
	}

	// 删除文件
	if err := os.Remove(absPath); err != nil {
		responseError(c, http.StatusInternalServerError, "Failed to delete file", err.Error())
		return
	}

	responseSuccess(c, "File deleted successfully", map[string]interface{}{
		"file_path": filePath,
		"deleted":   true,
	})
}
