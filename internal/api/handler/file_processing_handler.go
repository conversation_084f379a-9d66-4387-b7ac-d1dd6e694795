package handler

import (
	"strconv"

	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/logger"

	"github.com/gin-gonic/gin"
)

// FileProcessingHandler 文件处理处理器
type FileProcessingHandler struct {
	fileProcessingService service.FileProcessingService
}

// NewFileProcessingHandler 创建文件处理处理器
func NewFileProcessingHandler(fps service.FileProcessingService) *FileProcessingHandler {
	return &FileProcessingHandler{
		fileProcessingService: fps,
	}
}

// StartProcessing 开始文件处理
// @Summary 开始文件处理
// @Description 手动启动指定任务的文件处理
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} Response{data=string}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/{id}/start [post]
func (h *FileProcessingHandler) StartProcessing(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "Invalid task ID", err.Error())
		return
	}

	if err := h.fileProcessingService.StartProcessing(uint(taskID)); err != nil {
		logger.Errorf("Failed to start processing for task %d: %v", taskID, err)
		middleware.InternalServerErrorResponse(c, "Failed to start processing", err.Error())
		return
	}

	middleware.SuccessResponse(c, "Processing started successfully")
}

// PauseProcessing 暂停文件处理
// @Summary 暂停文件处理
// @Description 暂停指定任务的文件处理
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} Response{data=string}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/{id}/pause [post]
func (h *FileProcessingHandler) PauseProcessing(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "Invalid task ID", err.Error())
		return
	}

	if err := h.fileProcessingService.PauseProcessing(uint(taskID)); err != nil {
		logger.Errorf("Failed to pause processing for task %d: %v", taskID, err)
		middleware.InternalServerErrorResponse(c, "Failed to pause processing", err.Error())
		return
	}

	middleware.SuccessResponse(c, "Processing paused successfully")
}

// ResumeProcessing 恢复文件处理
// @Summary 恢复文件处理
// @Description 恢复指定任务的文件处理
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} Response{data=string}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/{id}/resume [post]
func (h *FileProcessingHandler) ResumeProcessing(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "Invalid task ID", err.Error())
		return
	}

	if err := h.fileProcessingService.ResumeProcessing(uint(taskID)); err != nil {
		logger.Errorf("Failed to resume processing for task %d: %v", taskID, err)
		middleware.InternalServerErrorResponse(c, "Failed to resume processing", err.Error())
		return
	}

	middleware.SuccessResponse(c, "Processing resumed successfully")
}

// CancelProcessing 取消文件处理
// @Summary 取消文件处理
// @Description 取消指定任务的文件处理
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} Response{data=string}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/{id}/cancel [post]
func (h *FileProcessingHandler) CancelProcessing(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "Invalid task ID", err.Error())
		return
	}

	if err := h.fileProcessingService.CancelProcessing(uint(taskID)); err != nil {
		logger.Errorf("Failed to cancel processing for task %d: %v", taskID, err)
		middleware.InternalServerErrorResponse(c, "Failed to cancel processing", err.Error())
		return
	}

	middleware.SuccessResponse(c, "Processing cancelled successfully")
}

// RetryProcessing 重试文件处理
// @Summary 重试文件处理
// @Description 重试失败的文件处理任务
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} Response{data=string}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/{id}/retry [post]
func (h *FileProcessingHandler) RetryProcessing(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "Invalid task ID", err.Error())
		return
	}

	if err := h.fileProcessingService.RetryProcessing(uint(taskID)); err != nil {
		logger.Errorf("Failed to retry processing for task %d: %v", taskID, err)
		middleware.InternalServerErrorResponse(c, "Failed to retry processing", err.Error())
		return
	}

	middleware.SuccessResponse(c, "Processing retry started successfully")
}

// GetProcessingStatus 获取处理状态
// @Summary 获取处理状态
// @Description 获取指定任务的文件处理状态
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} Response{data=service.ProcessingStatusResponse}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/{id}/status [get]
func (h *FileProcessingHandler) GetProcessingStatus(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "Invalid task ID", err.Error())
		return
	}

	status, err := h.fileProcessingService.GetProcessingStatus(uint(taskID))
	if err != nil {
		logger.Errorf("Failed to get processing status for task %d: %v", taskID, err)
		middleware.InternalServerErrorResponse(c, "Failed to get processing status", err.Error())
		return
	}

	middleware.SuccessResponse(c, status)
}

// GetProcessingProgress 获取处理进度
// @Summary 获取处理进度
// @Description 获取指定任务的文件处理进度
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} Response{data=service.ProcessingProgressResponse}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/{id}/progress [get]
func (h *FileProcessingHandler) GetProcessingProgress(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "Invalid task ID", err.Error())
		return
	}

	progress, err := h.fileProcessingService.GetProcessingProgress(uint(taskID))
	if err != nil {
		logger.Errorf("Failed to get processing progress for task %d: %v", taskID, err)
		middleware.InternalServerErrorResponse(c, "Failed to get processing progress", err.Error())
		return
	}

	middleware.SuccessResponse(c, progress)
}

// GetPlaylist 获取播放列表
// @Summary 获取播放列表
// @Description 获取指定任务的播放列表
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} Response{data=service.PlaylistResponse}
// @Failure 400 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/{id}/playlist [get]
func (h *FileProcessingHandler) GetPlaylist(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "Invalid task ID", err.Error())
		return
	}

	playlist, err := h.fileProcessingService.GetPlaylist(uint(taskID))
	if err != nil {
		logger.Errorf("Failed to get playlist for task %d: %v", taskID, err)
		middleware.InternalServerErrorResponse(c, "Failed to get playlist", err.Error())
		return
	}

	middleware.SuccessResponse(c, playlist)
}

// GetProcessingStats 获取处理统计
// @Summary 获取处理统计
// @Description 获取文件处理的统计信息
// @Tags 文件处理
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=service.ProcessingStats}
// @Failure 500 {object} Response
// @Router /api/processing/stats [get]
func (h *FileProcessingHandler) GetProcessingStats(c *gin.Context) {
	stats, err := h.fileProcessingService.GetProcessingStats()
	if err != nil {
		logger.Errorf("Failed to get processing stats: %v", err)
		middleware.InternalServerErrorResponse(c, "Failed to get processing stats", err.Error())
		return
	}

	middleware.SuccessResponse(c, stats)
}

// ListProcessingTasks 列出处理任务
// @Summary 列出处理任务
// @Description 获取文件处理任务列表
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(10)
// @Param processing_status query string false "处理状态"
// @Param search query string false "搜索关键词"
// @Success 200 {object} Response{data=object}
// @Failure 500 {object} Response
// @Router /api/processing/tasks [get]
func (h *FileProcessingHandler) ListProcessingTasks(c *gin.Context) {
	req := &service.ListProcessingTasksRequest{}

	// 解析查询参数
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			req.Page = p
		}
	}
	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			req.PageSize = ps
		}
	}
	req.ProcessingStatus = c.Query("processing_status")
	req.Search = c.Query("search")
	req.SortBy = c.DefaultQuery("sort_by", "created_at")
	req.SortDesc = c.Query("sort_desc") == "true"

	tasks, total, err := h.fileProcessingService.ListProcessingTasks(req)
	if err != nil {
		logger.Errorf("Failed to list processing tasks: %v", err)
		middleware.InternalServerErrorResponse(c, "Failed to list processing tasks", err.Error())
		return
	}

	response := map[string]interface{}{
		"tasks":     tasks,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	}

	middleware.SuccessResponse(c, response)
}

// BatchStartProcessing 批量开始处理
// @Summary 批量开始处理
// @Description 批量启动多个任务的文件处理
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param request body object{task_ids=[]int} true "任务ID列表"
// @Success 200 {object} Response{data=service.BatchOperationResult}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/batch/start [post]
func (h *FileProcessingHandler) BatchStartProcessing(c *gin.Context) {
	var req struct {
		TaskIDs []uint `json:"task_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.BadRequestResponse(c, "Invalid request body", err.Error())
		return
	}

	result, err := h.fileProcessingService.BatchStartProcessing(req.TaskIDs)
	if err != nil {
		logger.Errorf("Failed to batch start processing: %v", err)
		middleware.InternalServerErrorResponse(c, "Failed to batch start processing", err.Error())
		return
	}

	middleware.SuccessResponse(c, result)
}

// BatchCancelProcessing 批量取消处理
// @Summary 批量取消处理
// @Description 批量取消多个任务的文件处理
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param request body object{task_ids=[]int} true "任务ID列表"
// @Success 200 {object} Response{data=service.BatchOperationResult}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/batch/cancel [post]
func (h *FileProcessingHandler) BatchCancelProcessing(c *gin.Context) {
	var req struct {
		TaskIDs []uint `json:"task_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.BadRequestResponse(c, "Invalid request body", err.Error())
		return
	}

	result, err := h.fileProcessingService.BatchCancelProcessing(req.TaskIDs)
	if err != nil {
		logger.Errorf("Failed to batch cancel processing: %v", err)
		middleware.InternalServerErrorResponse(c, "Failed to batch cancel processing", err.Error())
		return
	}

	middleware.SuccessResponse(c, result)
}

// CleanupProcessingFiles 清理处理文件
// @Summary 清理处理文件
// @Description 清理指定任务的处理文件
// @Tags 文件处理
// @Accept json
// @Produce json
// @Param id path int true "任务ID"
// @Success 200 {object} Response{data=string}
// @Failure 400 {object} Response
// @Failure 500 {object} Response
// @Router /api/processing/{id}/cleanup [post]
func (h *FileProcessingHandler) CleanupProcessingFiles(c *gin.Context) {
	taskID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "Invalid task ID", err.Error())
		return
	}

	if err := h.fileProcessingService.CleanupProcessingFiles(uint(taskID)); err != nil {
		logger.Errorf("Failed to cleanup processing files for task %d: %v", taskID, err)
		middleware.InternalServerErrorResponse(c, "Failed to cleanup processing files", err.Error())
		return
	}

	middleware.SuccessResponse(c, "Processing files cleaned up successfully")
}
