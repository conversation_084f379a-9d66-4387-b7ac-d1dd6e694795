package handler

import (
	"strconv"

	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/service"

	"github.com/gin-gonic/gin"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService service.UserService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService service.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录获取访问令牌
// @Tags auth
// @Accept json
// @Produce json
// @Param request body service.LoginRequest true "登录请求"
// @Success 200 {object} middleware.APIResponse{data=service.LoginResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Router /api/v1/auth/login [post]
func (h *UserHandler) Login(c *gin.Context) {
	var req service.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	response, err := h.userService.Login(&req)
	if err != nil {
		middleware.UnauthorizedResponse(c, "login failed")
		return
	}

	middleware.SuccessResponse(c, response)
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出
// @Tags auth
// @Produce json
// @Success 200 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/auth/logout [post]
func (h *UserHandler) Logout(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		middleware.UnauthorizedResponse(c, "user not authenticated")
		return
	}

	if err := h.userService.Logout(userID); err != nil {
		middleware.InternalServerErrorResponse(c, "logout failed", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": "logout successful"})
}

// RefreshToken 刷新令牌
// @Summary 刷新令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags auth
// @Accept json
// @Produce json
// @Param request body map[string]string true "刷新令牌请求"
// @Success 200 {object} middleware.APIResponse{data=service.TokenResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Router /api/v1/auth/refresh [post]
func (h *UserHandler) RefreshToken(c *gin.Context) {
	var req map[string]string
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	refreshToken, exists := req["refresh_token"]
	if !exists || refreshToken == "" {
		middleware.BadRequestResponse(c, "refresh token is required", nil)
		return
	}

	response, err := h.userService.RefreshToken(refreshToken)
	if err != nil {
		middleware.UnauthorizedResponse(c, "invalid refresh token")
		return
	}

	middleware.SuccessResponse(c, response)
}

// GetProfile 获取用户资料
// @Summary 获取用户资料
// @Description 获取当前用户的资料信息
// @Tags users
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=model.User}
// @Failure 401 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/users/profile [get]
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		middleware.UnauthorizedResponse(c, "user not authenticated")
		return
	}

	user, err := h.userService.GetUser(userID)
	if err != nil {
		middleware.NotFoundResponse(c, "user not found")
		return
	}

	middleware.SuccessResponse(c, user.ToProfile())
}

// UpdateProfile 更新用户资料
// @Summary 更新用户资料
// @Description 更新当前用户的资料信息
// @Tags users
// @Accept json
// @Produce json
// @Param request body service.UpdateUserRequest true "更新用户请求"
// @Success 200 {object} middleware.APIResponse{data=model.User}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/users/profile [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		middleware.UnauthorizedResponse(c, "user not authenticated")
		return
	}

	var req service.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	// 普通用户不能修改角色和状态
	if !middleware.IsAdmin(c) {
		req.Role = ""
		req.Status = ""
	}

	user, err := h.userService.UpdateUser(userID, &req)
	if err != nil {
		middleware.BadRequestResponse(c, "failed to update profile", err.Error())
		return
	}

	middleware.SuccessResponse(c, user.ToProfile())
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前用户的密码
// @Tags users
// @Accept json
// @Produce json
// @Param request body service.ChangePasswordRequest true "修改密码请求"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/users/password [put]
func (h *UserHandler) ChangePassword(c *gin.Context) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		middleware.UnauthorizedResponse(c, "user not authenticated")
		return
	}

	var req service.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	if err := h.userService.ChangePassword(userID, &req); err != nil {
		middleware.BadRequestResponse(c, "failed to change password", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": "password changed successfully"})
}

// CreateUser 创建用户（管理员）
// @Summary 创建用户
// @Description 创建新用户（仅管理员）
// @Tags admin
// @Accept json
// @Produce json
// @Param request body service.CreateUserRequest true "创建用户请求"
// @Success 201 {object} middleware.APIResponse{data=model.User}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req service.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	user, err := h.userService.CreateUser(&req)
	if err != nil {
		middleware.BadRequestResponse(c, "failed to create user", err.Error())
		return
	}

	middleware.CreatedResponse(c, user.ToProfile())
}

// GetUser 获取用户信息（管理员）
// @Summary 获取用户信息
// @Description 获取指定用户的信息（仅管理员）
// @Tags admin
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} middleware.APIResponse{data=model.User}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "invalid user ID", err.Error())
		return
	}

	user, err := h.userService.GetUser(uint(userID))
	if err != nil {
		middleware.NotFoundResponse(c, "user not found")
		return
	}

	middleware.SuccessResponse(c, user.ToProfile())
}

// UpdateUser 更新用户信息（管理员）
// @Summary 更新用户信息
// @Description 更新指定用户的信息（仅管理员）
// @Tags admin
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body service.UpdateUserRequest true "更新用户请求"
// @Success 200 {object} middleware.APIResponse{data=model.User}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "invalid user ID", err.Error())
		return
	}

	var req service.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	user, err := h.userService.UpdateUser(uint(userID), &req)
	if err != nil {
		middleware.BadRequestResponse(c, "failed to update user", err.Error())
		return
	}

	middleware.SuccessResponse(c, user.ToProfile())
}

// DeleteUser 删除用户（管理员）
// @Summary 删除用户
// @Description 删除指定用户（仅管理员）
// @Tags admin
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 404 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		middleware.BadRequestResponse(c, "invalid user ID", err.Error())
		return
	}

	if err := h.userService.DeleteUser(uint(userID)); err != nil {
		middleware.BadRequestResponse(c, "failed to delete user", err.Error())
		return
	}

	middleware.SuccessResponse(c, gin.H{"message": "user deleted successfully"})
}

// ListUsers 获取用户列表（管理员）
// @Summary 获取用户列表
// @Description 获取用户列表，支持分页和过滤（仅管理员）
// @Tags admin
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param role query string false "用户角色"
// @Param status query string false "用户状态"
// @Param search query string false "搜索关键词"
// @Success 200 {object} middleware.APIResponse{data=middleware.PaginatedResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	var req service.ListUsersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		middleware.ValidationErrorResponse(c, err.Error())
		return
	}

	users, total, err := h.userService.ListUsers(&req)
	if err != nil {
		middleware.InternalServerErrorResponse(c, "failed to get users", err.Error())
		return
	}

	// 转换为用户资料格式
	profiles := make([]interface{}, len(users))
	for i, user := range users {
		profiles[i] = user.ToProfile()
	}

	middleware.PaginatedSuccessResponse(c, profiles, total, req.Page, req.PageSize)
}

// GetUserStats 获取用户统计（管理员）
// @Summary 获取用户统计
// @Description 获取用户统计信息（仅管理员）
// @Tags admin
// @Produce json
// @Success 200 {object} middleware.APIResponse{data=service.UserStats}
// @Failure 401 {object} middleware.APIResponse
// @Failure 403 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Security BearerAuth
// @Router /api/v1/admin/users/stats [get]
func (h *UserHandler) GetUserStats(c *gin.Context) {
	stats, err := h.userService.GetUserStats()
	if err != nil {
		middleware.InternalServerErrorResponse(c, "failed to get user stats", err.Error())
		return
	}

	middleware.SuccessResponse(c, stats)
}
