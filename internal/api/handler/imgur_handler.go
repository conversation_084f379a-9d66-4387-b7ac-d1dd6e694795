package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/pkg/logger"

	"github.com/gin-gonic/gin"
)

// ImgurHandler Imgur处理器
type ImgurHandler struct {
	clientID     string
	clientSecret string
	redirectURI  string
}

// NewImgurHandler 创建Imgur处理器
func NewImgurHandler(clientID, clientSecret, redirectURI string) *ImgurHandler {
	return &ImgurHandler{
		clientID:     clientID,
		clientSecret: clientSecret,
		redirectURI:  redirectURI,
	}
}

// ImgurTokenResponse Imgur令牌响应
type ImgurTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
	TokenType    string `json:"token_type"`
	Scope        string `json:"scope"`
}

// AuthCallback 处理Imgur OAuth2回调
// @Summary Imgur OAuth2回调
// @Description 处理Imgur OAuth2认证回调
// @Tags imgur
// @Param code query string true "授权码"
// @Param state query string false "状态参数"
// @Success 200 {object} middleware.APIResponse{data=ImgurTokenResponse}
// @Failure 400 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Router /api/imgur/callback [get]
func (h *ImgurHandler) AuthCallback(c *gin.Context) {
	// 获取授权码
	code := c.Query("code")
	if code == "" {
		middleware.BadRequestResponse(c, "missing authorization code", nil)
		return
	}

	// 获取状态参数（可选）
	state := c.Query("state")
	logger.Info("Imgur OAuth callback received", "code", code[:10]+"...", "state", state)

	// 交换访问令牌
	token, err := h.exchangeCodeForToken(code)
	if err != nil {
		logger.Error("Failed to exchange code for token", "error", err)
		middleware.InternalServerErrorResponse(c, "failed to get access token", err.Error())
		return
	}

	// 返回成功页面（而不是JSON）
	c.HTML(200, "imgur_success.html", gin.H{
		"message":      "Imgur认证成功！",
		"access_token": token.AccessToken,
		"expires_in":   token.ExpiresIn,
		"token_type":   token.TokenType,
	})
}

// GetAuthURL 获取Imgur认证URL
// @Summary 获取Imgur认证URL
// @Description 获取Imgur OAuth2认证URL
// @Tags imgur
// @Param state query string false "状态参数"
// @Success 200 {object} middleware.APIResponse{data=map[string]string}
// @Router /api/imgur/auth [get]
func (h *ImgurHandler) GetAuthURL(c *gin.Context) {
	state := c.Query("state")
	if state == "" {
		state = "default_state"
	}

	authURL := fmt.Sprintf(
		"https://api.imgur.com/oauth2/authorize?client_id=%s&response_type=code&state=%s",
		h.clientID,
		url.QueryEscape(state),
	)

	middleware.SuccessResponse(c, gin.H{
		"auth_url": authURL,
		"state":    state,
	})
}

// TestConnection 测试Imgur连接
// @Summary 测试Imgur API连接
// @Description 测试Imgur API连接状态
// @Tags imgur
// @Success 200 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Router /api/imgur/test [get]
func (h *ImgurHandler) TestConnection(c *gin.Context) {
	// 简单的API测试请求
	resp, err := http.Get("https://api.imgur.com/3/credits")
	if err != nil {
		middleware.InternalServerErrorResponse(c, "failed to connect to Imgur API", err.Error())
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		middleware.InternalServerErrorResponse(c, "Imgur API returned error", fmt.Sprintf("status: %d", resp.StatusCode))
		return
	}

	middleware.SuccessResponse(c, gin.H{
		"message": "Imgur API connection successful",
		"status":  "ok",
	})
}

// exchangeCodeForToken 交换授权码获取访问令牌
func (h *ImgurHandler) exchangeCodeForToken(code string) (*ImgurTokenResponse, error) {
	// 准备请求数据
	data := url.Values{}
	data.Set("client_id", h.clientID)
	data.Set("client_secret", h.clientSecret)
	data.Set("grant_type", "authorization_code")
	data.Set("code", code)

	// 发送POST请求
	resp, err := http.PostForm("https://api.imgur.com/oauth2/token", data)
	if err != nil {
		return nil, fmt.Errorf("failed to send token request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token request failed with status: %d", resp.StatusCode)
	}

	// 解析响应
	var tokenResp ImgurTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	return &tokenResp, nil
}

// UploadImage 上传图片到Imgur
// @Summary 上传图片到Imgur
// @Description 使用访问令牌上传图片到Imgur
// @Tags imgur
// @Accept multipart/form-data
// @Param Authorization header string true "Bearer access_token"
// @Param image formData file true "图片文件"
// @Success 200 {object} middleware.APIResponse
// @Failure 400 {object} middleware.APIResponse
// @Failure 401 {object} middleware.APIResponse
// @Failure 500 {object} middleware.APIResponse
// @Router /api/imgur/upload [post]
func (h *ImgurHandler) UploadImage(c *gin.Context) {
	// 获取访问令牌
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
		middleware.UnauthorizedResponse(c, "missing or invalid authorization header")
		return
	}

	accessToken := strings.TrimPrefix(authHeader, "Bearer ")

	// 获取上传的文件
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		middleware.BadRequestResponse(c, "failed to get uploaded file", err.Error())
		return
	}
	defer file.Close()

	logger.Info("Uploading image to Imgur", "filename", header.Filename, "size", header.Size, "token", accessToken[:10]+"...")

	// TODO: 实现实际的Imgur上传逻辑
	// 这里需要使用accessToken和文件数据调用Imgur API

	middleware.SuccessResponse(c, gin.H{
		"message":  "Image upload successful",
		"filename": header.Filename,
		"size":     header.Size,
		"url":      "https://i.imgur.com/example.jpg", // 示例URL
	})
}

// GetUploadStatus 获取上传状态
// @Summary 获取上传状态
// @Description 获取Imgur上传任务状态
// @Tags imgur
// @Success 200 {object} middleware.APIResponse
// @Router /api/imgur/status [get]
func (h *ImgurHandler) GetUploadStatus(c *gin.Context) {
	middleware.SuccessResponse(c, gin.H{
		"status":  "ready",
		"message": "Imgur upload service is ready",
	})
}
