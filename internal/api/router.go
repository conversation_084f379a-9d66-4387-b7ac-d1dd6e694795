package api

import (
	"magnet-downloader/internal/api/handler"
	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/config"
	"magnet-downloader/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Router API路由器
type Router struct {
	engine   *gin.Engine
	config   *config.Config
	services *service.Services
}

// NewRouter 创建API路由器
func NewRouter(cfg *config.Config, services *service.Services) *Router {
	// 设置Gin模式
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()

	// 加载HTML模板
	engine.LoadHTMLGlob("templates/*")

	return &Router{
		engine:   engine,
		config:   cfg,
		services: services,
	}
}

// Setup 设置路由
func (r *Router) Setup() {
	// 全局中间件
	r.setupMiddleware()

	// 创建处理器
	taskHandler := handler.NewTaskHandler(r.services.Task)
	userHandler := handler.NewUserHandler(r.services.User)
	configHandler := handler.NewConfigHandler(r.services.Config)
	systemHandler := handler.NewSystemHandler(r.services)
	wsHandler := handler.NewWebSocketHandler(r.services.WebSocket.GetHub())
	fileProcessingHandler := handler.NewFileProcessingHandler(r.services.FileProcessing)
	autoUploadHandler := handler.NewAutoUploadHandler(r.services.AutoUpload)

	// 创建Imgur处理器
	imgurHandler := handler.NewImgurHandler(
		r.config.FileProcessing.Imgur.ClientID,
		r.config.FileProcessing.Imgur.ClientSecret,
		r.config.FileProcessing.Imgur.RedirectURI,
	)

	// API路由组
	api := r.engine.Group("/api")
	{
		// Imgur OAuth2回调路由（公开访问）
		r.setupImgurRoutes(api, imgurHandler)

		// v1版本路由
		v1 := api.Group("/v1")
		{
			// 公开路由
			r.setupPublicRoutes(v1, userHandler, systemHandler, configHandler)

			// 需要认证的路由
			auth := v1.Group("")
			auth.Use(middleware.AuthMiddleware(r.config.JWT.Secret))
			{
				r.setupAuthRoutes(auth, taskHandler, userHandler, configHandler, systemHandler, wsHandler, fileProcessingHandler, autoUploadHandler)
			}

			// 管理员路由
			admin := v1.Group("/admin")
			admin.Use(middleware.AuthMiddleware(r.config.JWT.Secret))
			admin.Use(middleware.RequireAdmin())
			{
				r.setupAdminRoutes(admin, userHandler, configHandler, wsHandler, autoUploadHandler)
			}
		}
	}

	// Swagger文档
	if r.config.Server.Mode != "production" {
		r.engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}

	// 健康检查
	r.engine.GET("/health", systemHandler.HealthCheck)
	r.engine.GET("/ping", systemHandler.Ping)
}

// setupMiddleware 设置中间件
func (r *Router) setupMiddleware() {
	// 恢复中间件
	r.engine.Use(gin.Recovery())

	// 请求ID中间件
	r.engine.Use(middleware.RequestIDMiddleware())

	// CORS中间件
	r.engine.Use(middleware.CORSMiddleware())

	// 安全头中间件
	r.engine.Use(middleware.SecurityHeaders())

	// 日志中间件
	r.engine.Use(gin.Logger())
}

// setupPublicRoutes 设置公开路由
func (r *Router) setupPublicRoutes(rg *gin.RouterGroup, userHandler *handler.UserHandler, systemHandler *handler.SystemHandler, configHandler *handler.ConfigHandler) {
	// 认证相关
	auth := rg.Group("/auth")
	{
		auth.POST("/login", userHandler.Login)
		auth.POST("/refresh", userHandler.RefreshToken)
	}

	// 系统信息
	system := rg.Group("/system")
	{
		system.GET("/version", systemHandler.GetVersion)
	}

	// 公开配置
	rg.GET("/config/public", configHandler.GetPublicConfigs)

	// Ping
	rg.GET("/ping", systemHandler.Ping)
}

// setupAuthRoutes 设置需要认证的路由
func (r *Router) setupAuthRoutes(rg *gin.RouterGroup, taskHandler *handler.TaskHandler, userHandler *handler.UserHandler, configHandler *handler.ConfigHandler, systemHandler *handler.SystemHandler, wsHandler *handler.WebSocketHandler, fileProcessingHandler *handler.FileProcessingHandler, autoUploadHandler *handler.AutoUploadHandler) {
	// 认证相关
	auth := rg.Group("/auth")
	{
		auth.POST("/logout", userHandler.Logout)
	}

	// 用户相关
	users := rg.Group("/users")
	{
		users.GET("/profile", userHandler.GetProfile)
		users.PUT("/profile", userHandler.UpdateProfile)
		users.PUT("/password", userHandler.ChangePassword)
	}

	// 任务相关
	tasks := rg.Group("/tasks")
	{
		tasks.POST("", taskHandler.CreateTask)
		tasks.GET("", taskHandler.ListTasks)
		tasks.GET("/stats", taskHandler.GetTaskStats)
		tasks.GET("/:id", taskHandler.GetTask)
		tasks.PUT("/:id", taskHandler.UpdateTask)
		tasks.DELETE("/:id", taskHandler.DeleteTask)

		// 任务操作
		tasks.POST("/:id/start", taskHandler.StartTask)
		tasks.POST("/:id/pause", taskHandler.PauseTask)
		tasks.POST("/:id/resume", taskHandler.ResumeTask)
		tasks.POST("/:id/cancel", taskHandler.CancelTask)
		tasks.POST("/:id/retry", taskHandler.RetryTask)
	}

	// 文件处理相关
	processing := rg.Group("/processing")
	{
		// 处理控制
		processing.POST("/:id/start", fileProcessingHandler.StartProcessing)
		processing.POST("/:id/pause", fileProcessingHandler.PauseProcessing)
		processing.POST("/:id/resume", fileProcessingHandler.ResumeProcessing)
		processing.POST("/:id/cancel", fileProcessingHandler.CancelProcessing)
		processing.POST("/:id/retry", fileProcessingHandler.RetryProcessing)

		// 状态查询
		processing.GET("/:id/status", fileProcessingHandler.GetProcessingStatus)
		processing.GET("/:id/progress", fileProcessingHandler.GetProcessingProgress)
		processing.GET("/:id/playlist", fileProcessingHandler.GetPlaylist)

		// 任务管理
		processing.GET("/tasks", fileProcessingHandler.ListProcessingTasks)
		processing.GET("/stats", fileProcessingHandler.GetProcessingStats)

		// 批量操作
		processing.POST("/batch/start", fileProcessingHandler.BatchStartProcessing)
		processing.POST("/batch/cancel", fileProcessingHandler.BatchCancelProcessing)

		// 文件管理
		processing.POST("/:id/cleanup", fileProcessingHandler.CleanupProcessingFiles)
	}

	// 自动上传相关
	autoUpload := rg.Group("/auto-upload")
	{
		autoUpload.GET("/status", autoUploadHandler.GetStatus)
		autoUpload.GET("/stats", autoUploadHandler.GetStats)
		autoUpload.GET("/info", autoUploadHandler.GetServiceInfo)
		autoUpload.GET("/queue", autoUploadHandler.GetUploadQueue)
		autoUpload.GET("/history", autoUploadHandler.GetUploadHistory)
		autoUpload.POST("/scan", autoUploadHandler.TriggerScan)
	}

	// 配置相关
	config := rg.Group("/config")
	{
		config.GET("", configHandler.ListConfigs)
		config.GET("/:key", configHandler.GetConfig)
		config.GET("/category/:category", configHandler.GetConfigsByCategory)
	}

	// 系统信息
	system := rg.Group("/system")
	{
		system.GET("/info", systemHandler.GetSystemInfo)
		system.GET("/stats", systemHandler.GetStats)
	}

	// WebSocket相关
	rg.GET("/ws", wsHandler.HandleWebSocket)

	websocket := rg.Group("/websocket")
	{
		websocket.GET("/user/:user_id/online", wsHandler.CheckUserOnline)
	}
}

// setupAdminRoutes 设置管理员路由
func (r *Router) setupAdminRoutes(rg *gin.RouterGroup, userHandler *handler.UserHandler, configHandler *handler.ConfigHandler, wsHandler *handler.WebSocketHandler, autoUploadHandler *handler.AutoUploadHandler) {
	// 用户管理
	users := rg.Group("/users")
	{
		users.POST("", userHandler.CreateUser)
		users.GET("", userHandler.ListUsers)
		users.GET("/stats", userHandler.GetUserStats)
		users.GET("/:id", userHandler.GetUser)
		users.PUT("/:id", userHandler.UpdateUser)
		users.DELETE("/:id", userHandler.DeleteUser)
	}

	// 配置管理
	config := rg.Group("/config")
	{
		config.PUT("/:key", configHandler.SetConfig)
		config.DELETE("/:key", configHandler.DeleteConfig)
		config.PUT("/batch", configHandler.BatchSetConfigs)
		config.DELETE("/batch", configHandler.BatchDeleteConfigs)
		config.POST("/cache/refresh", configHandler.RefreshConfigCache)
	}

	// WebSocket管理
	websocket := rg.Group("/websocket")
	{
		websocket.GET("/stats", wsHandler.GetWebSocketStats)
		websocket.GET("/clients", wsHandler.GetWebSocketClients)
		websocket.GET("/online-users", wsHandler.GetOnlineUsers)
		websocket.POST("/broadcast", wsHandler.SendBroadcastMessage)
		websocket.POST("/user-message", wsHandler.SendUserMessage)
	}

	// 连接测试
	test := rg.Group("/test")
	{
		test.POST("/doodstream", configHandler.TestDoodStreamConnection)
		test.POST("/imgbb", configHandler.TestImgBBConnection)
	}

	// 自动上传管理
	autoUpload := rg.Group("/auto-upload")
	{
		autoUpload.POST("/start", autoUploadHandler.StartService)
		autoUpload.POST("/stop", autoUploadHandler.StopService)
	}
}

// setupImgurRoutes 设置Imgur路由
func (r *Router) setupImgurRoutes(rg *gin.RouterGroup, imgurHandler *handler.ImgurHandler) {
	imgur := rg.Group("/imgur")
	{
		// OAuth2认证相关
		imgur.GET("/auth", imgurHandler.GetAuthURL)
		imgur.GET("/callback", imgurHandler.AuthCallback)

		// API测试
		imgur.GET("/test", imgurHandler.TestConnection)
		imgur.GET("/status", imgurHandler.GetUploadStatus)

		// 图片上传（需要访问令牌）
		imgur.POST("/upload", imgurHandler.UploadImage)
	}
}

// GetEngine 获取Gin引擎
func (r *Router) GetEngine() *gin.Engine {
	return r.engine
}

// Start 启动服务器
func (r *Router) Start() error {
	addr := r.config.Server.Host + ":" + strconv.Itoa(r.config.Server.Port)
	return r.engine.Run(addr)
}
