# Makefile for Magnet Downloader

# 变量定义
APP_NAME = magnet-downloader
VERSION = 1.0.0
BUILD_DIR = build
BINARY_NAME = $(APP_NAME)
MAIN_PATH = ./cmd/server

# Go相关变量
GOCMD = go
GOBUILD = $(GOCMD) build
GOCLEAN = $(GOCMD) clean
GOTEST = $(GOCMD) test
GOGET = $(GOCMD) get
GOMOD = $(GOCMD) mod
GOFMT = $(GOCMD) fmt

# 构建标志
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(shell date +%Y-%m-%d_%H:%M:%S)"

# 默认目标
.PHONY: all
all: clean deps fmt test build

# 安装依赖
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# 格式化代码
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GOFMT) ./...

# 运行测试
.PHONY: test
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# 构建应用
.PHONY: build
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# 构建Linux版本
.PHONY: build-linux
build-linux:
	@echo "Building $(APP_NAME) for Linux..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux $(MAIN_PATH)

# 构建Windows版本
.PHONY: build-windows
build-windows:
	@echo "Building $(APP_NAME) for Windows..."
	@mkdir -p $(BUILD_DIR)
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows.exe $(MAIN_PATH)

# 构建macOS版本
.PHONY: build-darwin
build-darwin:
	@echo "Building $(APP_NAME) for macOS..."
	@mkdir -p $(BUILD_DIR)
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin $(MAIN_PATH)

# 构建所有平台版本
.PHONY: build-all
build-all: build-linux build-windows build-darwin

# 运行应用
.PHONY: run
run:
	@echo "Running $(APP_NAME)..."
	$(GOCMD) run $(MAIN_PATH)

# 开发模式运行（需要安装air）
.PHONY: dev
dev:
	@echo "Running in development mode..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "Air not found. Installing..."; \
		$(GOGET) -u github.com/cosmtrek/air; \
		air; \
	fi

# 清理构建文件
.PHONY: clean
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)

# 安装到系统
.PHONY: install
install: build
	@echo "Installing $(APP_NAME)..."
	@cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/

# 卸载
.PHONY: uninstall
uninstall:
	@echo "Uninstalling $(APP_NAME)..."
	@rm -f /usr/local/bin/$(BINARY_NAME)

# Docker相关
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 $(APP_NAME):latest

# 代码检查
.PHONY: lint
lint:
	@echo "Running linter..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not found. Please install it first."; \
	fi

# 生成模拟数据
.PHONY: mock
mock:
	@echo "Generating mocks..."
	@if command -v mockgen > /dev/null; then \
		$(GOCMD) generate ./...; \
	else \
		echo "mockgen not found. Installing..."; \
		$(GOGET) github.com/golang/mock/mockgen; \
		$(GOCMD) generate ./...; \
	fi

# 显示帮助
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean, install deps, format, test and build"
	@echo "  deps         - Install dependencies"
	@echo "  fmt          - Format code"
	@echo "  test         - Run tests"
	@echo "  build        - Build application"
	@echo "  build-all    - Build for all platforms"
	@echo "  run          - Run application"
	@echo "  dev          - Run in development mode with hot reload"
	@echo "  clean        - Clean build files"
	@echo "  install      - Install to system"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  lint         - Run linter"
	@echo "  mock         - Generate mocks"
	@echo "  help         - Show this help"
