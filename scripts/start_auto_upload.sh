#!/bin/bash

# 自动上传监控服务启动脚本
# 监控 /www/wwwroot/JAVAPI.COM/downloads 目录
# 自动上传视频文件到DoodStream
# 上传完成后删除文件夹节省硬盘空间

set -e

echo "🚀 启动自动上传监控服务"
echo "======================="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go环境未安装"
    exit 1
fi

# 进入项目目录
cd "$(dirname "$0")/.."

# 设置环境变量
export DOWNLOAD_DIR="/www/wwwroot/JAVAPI.COM/downloads"

# 检查下载目录
if [ ! -d "$DOWNLOAD_DIR" ]; then
    echo "📁 创建下载目录: $DOWNLOAD_DIR"
    mkdir -p "$DOWNLOAD_DIR"
fi

echo "📁 监控目录: $DOWNLOAD_DIR"

# 检查配置文件
if [ ! -f "config.yaml" ]; then
    echo "❌ 配置文件不存在: config.yaml"
    echo "💡 请先创建配置文件"
    exit 1
fi

# 检查DoodStream API密钥
if [ -z "$DOODSTREAM_API_KEY" ]; then
    echo "⚠️  未设置DoodStream API密钥"
    echo "💡 请设置环境变量: export DOODSTREAM_API_KEY=\"your_api_key\""
    echo "💡 或在config.yaml中配置"
fi

# 显示当前配置
echo ""
echo "⚙️ 当前配置:"
echo "   监控目录: $DOWNLOAD_DIR"
echo "   扫描间隔: 5分钟"
echo "   最大并发: 3个上传"
echo "   上传后删除: 启用"
echo "   最小文件大小: 10MB"

echo ""
echo "🎯 功能说明:"
echo "   ✅ 自动扫描下载目录的子文件夹"
echo "   ✅ 智能选择视频文件（最大文件或分段文件）"
echo "   ✅ 并发上传到DoodStream"
echo "   ✅ 上传完成后自动删除文件夹"
echo "   ✅ 实时WebSocket通知"
echo "   ✅ 完整的错误处理和重试"

echo ""
echo "📋 支持的文件格式:"
echo "   .mp4, .avi, .mkv, .mov, .wmv, .flv, .webm, .m4v, .3gp, .ts, .m2ts, .vob"

echo ""
echo "🔄 工作流程:"
echo "   1. 每5分钟扫描 $DOWNLOAD_DIR"
echo "   2. 发现新的视频文件"
echo "   3. 自动上传到DoodStream"
echo "   4. 保存播放链接到数据库"
echo "   5. 删除原文件夹释放空间"

echo ""
echo "🌐 API端点:"
echo "   GET  /api/v1/auto-upload/status   - 获取服务状态"
echo "   GET  /api/v1/auto-upload/stats    - 获取统计信息"
echo "   POST /api/v1/auto-upload/scan     - 手动触发扫描"

echo ""
echo "📊 监控方法:"
echo "   # 查看服务状态"
echo "   curl http://localhost:8080/api/v1/auto-upload/status"
echo ""
echo "   # 手动触发扫描"
echo "   curl -X POST http://localhost:8080/api/v1/auto-upload/scan"
echo ""
echo "   # 查看上传统计"
echo "   curl http://localhost:8080/api/v1/auto-upload/stats"

echo ""
echo "⚠️  重要提醒:"
echo "   - 上传完成后会自动删除原文件夹"
echo "   - 请确保重要文件已备份"
echo "   - 建议定期检查日志文件"

echo ""
read -p "确认启动自动上传监控服务? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 用户取消启动"
    exit 1
fi

echo ""
echo "🚀 启动服务..."

# 创建日志目录
mkdir -p logs

# 启动主服务
echo "📝 日志文件: logs/auto_upload.log"
echo "🔄 服务正在启动，请稍候..."

# 启动服务并记录日志
go run cmd/server/main.go 2>&1 | tee logs/auto_upload.log