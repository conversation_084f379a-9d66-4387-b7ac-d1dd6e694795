#!/bin/bash

# 检查磁力下载进度
GID="a0ebfe27ef2783ad"
API_URL="http://localhost:6800/jsonrpc"

echo "🎯 检查磁力链接下载进度"
echo "📋 GID: $GID"
echo "🔗 magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5"
echo "================================================"

# 获取下载状态
RESPONSE=$(curl -s -X POST "$API_URL" \
    -H "Content-Type: application/json" \
    -d "{\"jsonrpc\":\"2.0\",\"id\":\"1\",\"method\":\"aria2.tellStatus\",\"params\":[\"$GID\"]}")

echo "📊 当前状态:"
echo "$RESPONSE" | python3 -m json.tool

# 解析关键信息
STATUS=$(echo "$RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
TOTAL_LENGTH=$(echo "$RESPONSE" | grep -o '"totalLength":"[^"]*"' | cut -d'"' -f4)
COMPLETED_LENGTH=$(echo "$RESPONSE" | grep -o '"completedLength":"[^"]*"' | cut -d'"' -f4)
DOWNLOAD_SPEED=$(echo "$RESPONSE" | grep -o '"downloadSpeed":"[^"]*"' | cut -d'"' -f4)
CONNECTIONS=$(echo "$RESPONSE" | grep -o '"connections":"[^"]*"' | cut -d'"' -f4)
NUM_SEEDERS=$(echo "$RESPONSE" | grep -o '"numSeeders":"[^"]*"' | cut -d'"' -f4)

echo ""
echo "📈 关键指标:"
echo "  状态: $STATUS"
echo "  总大小: $TOTAL_LENGTH bytes"
echo "  已完成: $COMPLETED_LENGTH bytes"
echo "  下载速度: $DOWNLOAD_SPEED bytes/s"
echo "  连接数: $CONNECTIONS"
echo "  种子数: $NUM_SEEDERS"

# 计算进度
if [ "$TOTAL_LENGTH" != "0" ] && [ -n "$TOTAL_LENGTH" ]; then
    PROGRESS=$(echo "scale=2; $COMPLETED_LENGTH * 100 / $TOTAL_LENGTH" | bc -l 2>/dev/null || echo "0")
    TOTAL_MB=$(echo "scale=2; $TOTAL_LENGTH / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
    COMPLETED_MB=$(echo "scale=2; $COMPLETED_LENGTH / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
    echo "  进度: ${PROGRESS}%"
    echo "  大小: ${COMPLETED_MB}MB / ${TOTAL_MB}MB"
else
    echo "  进度: 获取元数据中..."
fi

# 计算速度
if [ "$DOWNLOAD_SPEED" != "0" ] && [ -n "$DOWNLOAD_SPEED" ]; then
    SPEED_MB=$(echo "scale=2; $DOWNLOAD_SPEED / 1024 / 1024" | bc -l 2>/dev/null || echo "0")
    echo "  速度: ${SPEED_MB} MB/s"
fi

echo ""
echo "📁 下载目录内容:"
ls -la /tmp/downloads/ 2>/dev/null || echo "  目录为空或不存在"

echo ""
echo "🔄 如需持续监控，运行: watch -n 5 ./scripts/check_download.sh"
