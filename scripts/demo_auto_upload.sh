#!/bin/bash

# 自动上传功能演示脚本
# 用于演示智能视频文件选择和自动上传功能

set -e

echo "🎬 自动上传功能演示"
echo "=================="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go环境未安装"
    exit 1
fi

# 进入项目目录
cd "$(dirname "$0")/.."

# 设置演示目录
DEMO_DIR="/tmp/auto_upload_demo"
export DOWNLOAD_DIR="$DEMO_DIR"

echo "📁 创建演示目录: $DEMO_DIR"

# 清理旧的演示目录
if [ -d "$DEMO_DIR" ]; then
    rm -rf "$DEMO_DIR"
fi

# 创建演示目录结构
mkdir -p "$DEMO_DIR"/{movie1,movie2,series1,mixed_content}

echo "🎥 创建演示视频文件..."

# 创建不同类型的演示文件
create_demo_file() {
    local file_path="$1"
    local size_mb="$2"
    
    # 创建指定大小的文件（用随机数据填充）
    dd if=/dev/urandom of="$file_path" bs=1M count="$size_mb" 2>/dev/null
    echo "   ✅ 创建文件: $(basename "$file_path") (${size_mb}MB)"
}

# 场景1: 单个大文件 + 小文件（应该选择大文件）
echo "📂 场景1: movie1/ - 单个大文件选择"
create_demo_file "$DEMO_DIR/movie1/main_movie.mp4" 150
create_demo_file "$DEMO_DIR/movie1/trailer.mp4" 5      # 小文件，会被忽略
create_demo_file "$DEMO_DIR/movie1/behind_scenes.mp4" 80
echo "   💡 预期选择: main_movie.mp4 (最大文件)"

# 场景2: 分段文件（应该选择所有分段）
echo "📂 场景2: movie2/ - 分段文件处理"
create_demo_file "$DEMO_DIR/movie2/epic_movie.part1.mp4" 100
create_demo_file "$DEMO_DIR/movie2/epic_movie.part2.mp4" 100
create_demo_file "$DEMO_DIR/movie2/epic_movie.part3.mp4" 100
echo "   💡 预期选择: 所有 epic_movie.partX.mp4 文件"

# 场景3: 系列文件（应该选择所有）
echo "📂 场景3: series1/ - 系列文件处理"
create_demo_file "$DEMO_DIR/series1/episode.001.mkv" 120
create_demo_file "$DEMO_DIR/series1/episode.002.mkv" 115
create_demo_file "$DEMO_DIR/series1/episode.003.mkv" 125
echo "   💡 预期选择: 所有 episode.XXX.mkv 文件"

# 场景4: 混合内容（应该智能选择）
echo "📂 场景4: mixed_content/ - 混合内容处理"
create_demo_file "$DEMO_DIR/mixed_content/main_video.avi" 200
create_demo_file "$DEMO_DIR/mixed_content/bonus.part1.mp4" 50
create_demo_file "$DEMO_DIR/mixed_content/bonus.part2.mp4" 50
create_demo_file "$DEMO_DIR/mixed_content/subtitle.srt" 1   # 非视频文件
echo "   💡 预期选择: main_video.avi + 所有 bonus.partX.mp4"

# 创建一些非视频文件（应该被忽略）
echo "📄 创建非视频文件（应该被忽略）..."
echo "This is a subtitle file" > "$DEMO_DIR/movie1/subtitle.srt"
echo "This is a readme file" > "$DEMO_DIR/movie2/README.txt"
dd if=/dev/urandom of="$DEMO_DIR/series1/poster.jpg" bs=1K count=500 2>/dev/null

echo ""
echo "🧪 运行视频文件选择测试..."
echo "=========================="

# 运行测试程序
go run cmd/auto_upload_test/main.go

echo ""
echo "📊 演示结果分析"
echo "=============="

echo "🔍 扫描结果应该显示:"
echo "   ✅ movie1/: 选择 main_movie.mp4 (150MB, 最大文件)"
echo "   ✅ movie2/: 选择所有 epic_movie.partX.mp4 (3个分段文件)"
echo "   ✅ series1/: 选择所有 episode.XXX.mkv (3个系列文件)"
echo "   ✅ mixed_content/: 选择 main_video.avi + bonus.partX.mp4"
echo ""
echo "❌ 应该被忽略的文件:"
echo "   - movie1/trailer.mp4 (5MB, 小于10MB阈值)"
echo "   - 所有 .srt, .txt, .jpg 文件 (非视频格式)"

echo ""
echo "⚙️ 配置验证"
echo "=========="

echo "检查当前配置:"
echo "   - 自动上传启用状态"
echo "   - 扫描间隔设置"
echo "   - 最大并发上传数"
echo "   - 最小文件大小阈值"
echo "   - DoodStream配置状态"

echo ""
echo "🚀 下一步操作建议"
echo "==============="

echo "1. 📋 检查配置文件:"
echo "   编辑 config.yaml 确保以下设置:"
echo "   file_processing:"
echo "     upload_provider: \"doodstream\""
echo "     auto_upload:"
echo "       enabled: true"
echo "       scan_interval: 30"
echo "       max_concurrent_uploads: 3"

echo ""
echo "2. 🔑 设置DoodStream API密钥:"
echo "   export DOODSTREAM_API_KEY=\"your_api_key\""
echo "   或在config.yaml中配置"

echo ""
echo "3. 🏃 启动主服务:"
echo "   go run cmd/server/main.go"

echo ""
echo "4. 🌐 通过API管理:"
echo "   # 获取状态"
echo "   curl http://localhost:8080/api/v1/auto-upload/status"
echo "   # 手动触发扫描"
echo "   curl -X POST http://localhost:8080/api/v1/auto-upload/scan"

echo ""
echo "5. 📁 使用真实文件:"
echo "   将下载的视频文件放入 /downloads/ 的子目录中"
echo "   系统会自动检测并上传到DoodStream"

echo ""
echo "🧹 清理演示文件"
echo "=============="

read -p "是否删除演示文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  删除演示目录: $DEMO_DIR"
    rm -rf "$DEMO_DIR"
    echo "✅ 清理完成"
else
    echo "📁 演示文件保留在: $DEMO_DIR"
    echo "💡 您可以手动删除: rm -rf $DEMO_DIR"
fi

echo ""
echo "✨ 自动上传功能演示完成！"
echo ""
echo "📚 更多信息请参考:"
echo "   - docs/auto-upload-guide.md"
echo "   - docs/doodstream-integration.md"
echo "   - docs/api.md"