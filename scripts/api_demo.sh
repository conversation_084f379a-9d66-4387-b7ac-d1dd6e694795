#!/bin/bash

# API演示脚本 - 完整的磁力下载和文件处理流程
# 目标磁力链接: magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5

set -e

API_BASE="http://localhost:8080/api/v1"
MAGNET_URI="magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5"

echo "🚀 磁力下载和文件处理 API 演示"
echo "=================================================="
echo "📋 目标磁力链接: $MAGNET_URI"
echo "🌐 API 基础地址: $API_BASE"
echo ""

# 检查服务是否运行
echo "🔍 检查服务状态..."
if ! curl -s "$API_BASE/../ping" > /dev/null; then
    echo "❌ 服务未运行，请先启动服务器"
    echo "💡 运行命令: go run cmd/server/main.go"
    exit 1
fi
echo "✅ 服务运行正常"

# 用户登录
echo ""
echo "🔐 用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
    -H "Content-Type: application/json" \
    -d '{
        "username": "admin",
        "password": "admin123"
    }')

if echo "$LOGIN_RESPONSE" | grep -q '"success":true'; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo "✅ 登录成功，获取到 Token"
else
    echo "❌ 登录失败，请检查用户名密码"
    echo "响应: $LOGIN_RESPONSE"
    exit 1
fi

# 创建下载任务
echo ""
echo "📥 创建下载任务..."
TASK_RESPONSE=$(curl -s -X POST "$API_BASE/tasks" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "{
        \"magnet_uri\": \"$MAGNET_URI\",
        \"task_name\": \"SSIS-936-C_GG5 API演示任务\",
        \"save_path\": \"/tmp/downloads\",
        \"priority\": 2
    }")

if echo "$TASK_RESPONSE" | grep -q '"success":true'; then
    TASK_ID=$(echo "$TASK_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
    echo "✅ 任务创建成功，任务ID: $TASK_ID"
else
    echo "❌ 任务创建失败"
    echo "响应: $TASK_RESPONSE"
    exit 1
fi

# 启动下载任务
echo ""
echo "▶️ 启动下载任务..."
START_RESPONSE=$(curl -s -X POST "$API_BASE/tasks/$TASK_ID/start" \
    -H "Authorization: Bearer $TOKEN")

if echo "$START_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 任务启动成功"
else
    echo "❌ 任务启动失败"
    echo "响应: $START_RESPONSE"
fi

# 监控下载进度
echo ""
echo "📊 监控下载进度..."
for i in {1..10}; do
    echo "  检查进度 ($i/10)..."
    
    TASK_INFO=$(curl -s -X GET "$API_BASE/tasks/$TASK_ID" \
        -H "Authorization: Bearer $TOKEN")
    
    if echo "$TASK_INFO" | grep -q '"status":"completed"'; then
        echo "  🎉 下载完成！"
        break
    elif echo "$TASK_INFO" | grep -q '"status":"downloading"'; then
        PROGRESS=$(echo "$TASK_INFO" | grep -o '"progress":[0-9.]*' | cut -d':' -f2)
        echo "  📈 下载进度: ${PROGRESS}%"
    else
        STATUS=$(echo "$TASK_INFO" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        echo "  📋 任务状态: $STATUS"
    fi
    
    sleep 2
done

# 检查文件处理状态
echo ""
echo "🔄 检查文件处理状态..."
PROCESSING_STATUS=$(curl -s -X GET "$API_BASE/processing/$TASK_ID/status" \
    -H "Authorization: Bearer $TOKEN")

if echo "$PROCESSING_STATUS" | grep -q '"success":true'; then
    echo "✅ 文件处理状态查询成功"
    echo "$PROCESSING_STATUS" | grep -o '"status":"[^"]*"' | cut -d'"' -f4
else
    echo "⚠️ 文件处理尚未开始或查询失败"
fi

# 手动启动文件处理（如果需要）
echo ""
echo "🔄 手动启动文件处理..."
PROCESSING_START=$(curl -s -X POST "$API_BASE/processing/$TASK_ID/start" \
    -H "Authorization: Bearer $TOKEN")

if echo "$PROCESSING_START" | grep -q '"success":true'; then
    echo "✅ 文件处理启动成功"
else
    echo "⚠️ 文件处理启动失败或已在进行中"
fi

# 监控文件处理进度
echo ""
echo "📊 监控文件处理进度..."
for i in {1..5}; do
    echo "  检查处理进度 ($i/5)..."
    
    PROGRESS_RESPONSE=$(curl -s -X GET "$API_BASE/processing/$TASK_ID/progress" \
        -H "Authorization: Bearer $TOKEN")
    
    if echo "$PROGRESS_RESPONSE" | grep -q '"success":true'; then
        PROGRESS=$(echo "$PROGRESS_RESPONSE" | grep -o '"progress":[0-9.]*' | cut -d':' -f2)
        echo "  🔄 处理进度: ${PROGRESS}%"
        
        if [ "$PROGRESS" = "100" ]; then
            echo "  🎉 文件处理完成！"
            break
        fi
    else
        echo "  ⚠️ 进度查询失败"
    fi
    
    sleep 3
done

# 获取播放列表
echo ""
echo "📋 获取播放列表..."
PLAYLIST_RESPONSE=$(curl -s -X GET "$API_BASE/processing/$TASK_ID/playlist" \
    -H "Authorization: Bearer $TOKEN")

if echo "$PLAYLIST_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 播放列表获取成功"
    echo "📋 播放列表内容预览:"
    echo "$PLAYLIST_RESPONSE" | grep -o '"content":"[^"]*"' | cut -d'"' -f4 | head -10
else
    echo "⚠️ 播放列表获取失败或尚未生成"
fi

# 获取处理统计
echo ""
echo "📊 获取处理统计..."
STATS_RESPONSE=$(curl -s -X GET "$API_BASE/processing/stats" \
    -H "Authorization: Bearer $TOKEN")

if echo "$STATS_RESPONSE" | grep -q '"success":true'; then
    echo "✅ 统计信息获取成功"
    echo "$STATS_RESPONSE"
else
    echo "⚠️ 统计信息获取失败"
fi

# 获取任务列表
echo ""
echo "📋 获取任务列表..."
TASKS_RESPONSE=$(curl -s -X GET "$API_BASE/tasks" \
    -H "Authorization: Bearer $TOKEN")

if echo "$TASKS_RESPONSE" | grep -q '"success":true'; then
    TASK_COUNT=$(echo "$TASKS_RESPONSE" | grep -o '"total":[0-9]*' | cut -d':' -f2)
    echo "✅ 任务列表获取成功，共 $TASK_COUNT 个任务"
else
    echo "⚠️ 任务列表获取失败"
fi

echo ""
echo "=================================================="
echo "🎉 API 演示完成！"
echo ""
echo "📊 演示总结:"
echo "  ✅ 用户认证和 Token 获取"
echo "  ✅ 磁力下载任务创建和启动"
echo "  ✅ 下载进度实时监控"
echo "  ✅ 文件处理自动/手动启动"
echo "  ✅ 处理进度实时监控"
echo "  ✅ HLS 播放列表获取"
echo "  ✅ 统计信息和任务管理"
echo ""
echo "🔗 完整的 API 文档请查看:"
echo "  📖 Swagger 文档: http://localhost:8080/swagger/index.html"
echo "  📋 README.md: 项目根目录"
echo ""
echo "🎯 目标磁力链接处理完成:"
echo "  📋 $MAGNET_URI"
echo "=================================================="
