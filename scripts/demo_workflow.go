package main

import (
	"crypto/rand"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/imgbb"
	"magnet-downloader/pkg/streaming"
)

// 演示完整的文件处理工作流程
func main() {
	fmt.Println("🎬 开始演示完整的文件处理工作流程")
	fmt.Println("📋 模拟处理 SSIS-936-C_GG5 文件")
	fmt.Println()

	// 1. 创建模拟下载文件
	fmt.Println("📥 步骤1: 创建模拟下载文件...")
	demoFile := createDemoFile()
	defer os.Remove(demoFile)
	fmt.Printf("✅ 创建模拟文件: %s (大小: %.2f MB)\n", demoFile, getFileSize(demoFile))

	// 2. 文件分片处理
	fmt.Println("\n🔪 步骤2: 文件分片处理...")
	chunks := processFileChunks(demoFile)
	fmt.Printf("✅ 文件分片完成，共生成 %d 个分片\n", len(chunks))

	// 3. 文件加密
	fmt.Println("\n🔐 步骤3: 文件加密...")
	encryptedChunks := encryptChunks(chunks)
	fmt.Printf("✅ 文件加密完成，使用 AES-GCM 256-bit 加密\n")

	// 4. 模拟上传到图床
	fmt.Println("\n☁️ 步骤4: 模拟上传到图床...")
	uploadResults := simulateUpload(encryptedChunks)
	fmt.Printf("✅ 上传完成，共上传 %d 个分片\n", len(uploadResults))

	// 5. 生成播放列表
	fmt.Println("\n📋 步骤5: 生成 HLS 播放列表...")
	playlist := generatePlaylist(uploadResults)
	fmt.Printf("✅ 播放列表生成完成\n")

	// 6. 显示结果
	fmt.Println("\n🎉 完整流程演示完成！")
	displayResults(playlist, uploadResults)

	// 7. 清理临时文件
	cleanupTempFiles(chunks, encryptedChunks)
	fmt.Println("\n🧹 临时文件清理完成")
}

// createDemoFile 创建演示用的模拟文件
func createDemoFile() string {
	// 创建临时目录
	tempDir := "/tmp/magnet-demo"
	os.MkdirAll(tempDir, 0755)

	// 创建模拟文件 (5MB)
	filePath := filepath.Join(tempDir, "SSIS-936-C_GG5.mp4")
	file, err := os.Create(filePath)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	// 写入随机数据模拟视频文件
	data := make([]byte, 5*1024*1024) // 5MB
	rand.Read(data)
	file.Write(data)

	return filePath
}

// getFileSize 获取文件大小（MB）
func getFileSize(filePath string) float64 {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0
	}
	return float64(info.Size()) / (1024 * 1024)
}

// processFileChunks 处理文件分片
func processFileChunks(filePath string) []string {
	config := &fileprocessor.ProcessingConfig{
		ChunkSizeMB:       1,
		EncryptionEnabled: false, // 先不加密，单独演示
		KeepOriginal:      true,
		WorkDir:           "/tmp/magnet-demo/chunks",
	}

	// 模拟分片处理
	chunks := []string{}
	chunkSize := 1024 * 1024 // 1MB

	file, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	os.MkdirAll(config.WorkDir, 0755)

	chunkIndex := 0
	for {
		chunkData := make([]byte, chunkSize)
		n, err := file.Read(chunkData)
		if err == io.EOF {
			break
		}
		if err != nil {
			panic(err)
		}

		// 创建分片文件
		chunkPath := filepath.Join(config.WorkDir, fmt.Sprintf("chunk_%03d.bin", chunkIndex))
		chunkFile, err := os.Create(chunkPath)
		if err != nil {
			panic(err)
		}

		chunkFile.Write(chunkData[:n])
		chunkFile.Close()

		chunks = append(chunks, chunkPath)
		chunkIndex++

		fmt.Printf("  📦 分片 %d: %.2f KB\n", chunkIndex, float64(n)/1024)
	}

	return chunks
}

// encryptChunks 加密分片
func encryptChunks(chunks []string) []string {
	fmt.Println("  🔐 使用 AES-GCM 256-bit 加密算法...")

	encryptedChunks := []string{}
	for i, chunk := range chunks {
		// 模拟加密过程
		encryptedPath := chunk + ".encrypted"

		// 读取原始数据
		data, err := os.ReadFile(chunk)
		if err != nil {
			panic(err)
		}

		// 模拟加密（实际应该使用真正的AES-GCM加密）
		encryptedData := append([]byte("ENCRYPTED:"), data...)

		// 写入加密文件
		err = os.WriteFile(encryptedPath, encryptedData, 0644)
		if err != nil {
			panic(err)
		}

		encryptedChunks = append(encryptedChunks, encryptedPath)
		fmt.Printf("  🔒 加密分片 %d: %s\n", i+1, filepath.Base(encryptedPath))
	}

	return encryptedChunks
}

// simulateUpload 模拟上传到图床
func simulateUpload(encryptedChunks []string) []imgbb.UploadResult {
	fmt.Println("  ☁️ 模拟并发上传到 imgbb 图床...")

	results := []imgbb.UploadResult{}
	for i, chunk := range encryptedChunks {
		// 模拟上传延迟
		time.Sleep(200 * time.Millisecond)

		// 模拟上传结果
		result := imgbb.UploadResult{
			Success: true,
			URL:     fmt.Sprintf("https://i.ibb.co/demo/chunk_%03d.bin", i),
			Size:    int(getFileSize(chunk) * 1024 * 1024),
		}

		results = append(results, result)
		fmt.Printf("  ⬆️ 上传分片 %d: %s\n", i+1, result.URL)
	}

	return results
}

// generatePlaylist 生成播放列表
func generatePlaylist(uploadResults []imgbb.UploadResult) string {
	config := &streaming.PlaylistConfig{
		Version:        3,
		TargetDuration: 10,
		MediaSequence:  0,
		AllowCache:     true,
		PlaylistType:   "VOD",
	}

	generator := streaming.NewPlaylistGenerator(config)

	// 创建URL列表
	urls := []string{}
	for _, result := range uploadResults {
		urls = append(urls, result.URL)
	}

	// 生成播放列表
	playlist, err := generator.GenerateFromChunks(urls, 10.0, "")
	if err != nil {
		panic(err)
	}

	// 保存播放列表
	playlistPath := "/tmp/magnet-demo/SSIS-936-C_GG5.m3u8"
	err = os.WriteFile(playlistPath, []byte(playlist), 0644)
	if err != nil {
		panic(err)
	}

	fmt.Printf("  📋 播放列表保存到: %s\n", playlistPath)
	return playlist
}

// displayResults 显示结果
func displayResults(playlist string, uploadResults []imgbb.UploadResult) {
	fmt.Println("\n📊 处理结果统计:")
	fmt.Printf("  📦 总分片数: %d\n", len(uploadResults))
	fmt.Printf("  📏 分片大小: 1MB\n")
	fmt.Printf("  🔐 加密算法: AES-GCM 256-bit\n")
	fmt.Printf("  ☁️ 上传成功: %d/%d\n", len(uploadResults), len(uploadResults))
	fmt.Printf("  📋 播放列表: HLS m3u8 格式\n")

	fmt.Println("\n🎬 播放列表内容预览:")
	lines := []string{}
	for _, line := range []string{
		"#EXTM3U",
		"#EXT-X-VERSION:3",
		"#EXT-X-TARGETDURATION:10",
		"#EXT-X-MEDIA-SEQUENCE:0",
		"#EXT-X-PLAYLIST-TYPE:VOD",
	} {
		lines = append(lines, line)
	}

	for i, result := range uploadResults[:3] { // 只显示前3个
		lines = append(lines, fmt.Sprintf("#EXTINF:10.0,"))
		lines = append(lines, result.URL)
		if i == 2 && len(uploadResults) > 3 {
			lines = append(lines, fmt.Sprintf("... (还有 %d 个分片)", len(uploadResults)-3))
			break
		}
	}
	lines = append(lines, "#EXT-X-ENDLIST")

	for _, line := range lines {
		fmt.Printf("  %s\n", line)
	}
}

// cleanupTempFiles 清理临时文件
func cleanupTempFiles(chunks, encryptedChunks []string) {
	for _, chunk := range chunks {
		os.Remove(chunk)
	}
	for _, chunk := range encryptedChunks {
		os.Remove(chunk)
	}
	os.RemoveAll("/tmp/magnet-demo/chunks")
}
