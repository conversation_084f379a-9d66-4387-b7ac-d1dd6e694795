#!/bin/bash

# 监控aria2下载进度
GID="a0ebfe27ef2783ad"
API_URL="http://localhost:6800/jsonrpc"

echo "🎯 监控磁力链接下载进度"
echo "📋 GID: $GID"
echo "🔗 magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5"
echo "================================================"

while true; do
    # 获取下载状态
    RESPONSE=$(curl -s -X POST "$API_URL" \
        -H "Content-Type: application/json" \
        -d "{\"jsonrpc\":\"2.0\",\"id\":\"1\",\"method\":\"aria2.tellStatus\",\"params\":[\"$GID\"]}")
    
    # 解析状态
    STATUS=$(echo "$RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    TOTAL_LENGTH=$(echo "$RESPONSE" | grep -o '"totalLength":"[^"]*"' | cut -d'"' -f4)
    COMPLETED_LENGTH=$(echo "$RESPONSE" | grep -o '"completedLength":"[^"]*"' | cut -d'"' -f4)
    DOWNLOAD_SPEED=$(echo "$RESPONSE" | grep -o '"downloadSpeed":"[^"]*"' | cut -d'"' -f4)
    CONNECTIONS=$(echo "$RESPONSE" | grep -o '"connections":"[^"]*"' | cut -d'"' -f4)
    NUM_SEEDERS=$(echo "$RESPONSE" | grep -o '"numSeeders":"[^"]*"' | cut -d'"' -f4)
    
    # 计算进度
    if [ "$TOTAL_LENGTH" != "0" ] && [ -n "$TOTAL_LENGTH" ]; then
        PROGRESS=$(echo "scale=2; $COMPLETED_LENGTH * 100 / $TOTAL_LENGTH" | bc -l)
        TOTAL_MB=$(echo "scale=2; $TOTAL_LENGTH / 1024 / 1024" | bc -l)
        COMPLETED_MB=$(echo "scale=2; $COMPLETED_LENGTH / 1024 / 1024" | bc -l)
    else
        PROGRESS="0.00"
        TOTAL_MB="未知"
        COMPLETED_MB="0.00"
    fi
    
    # 计算速度
    if [ "$DOWNLOAD_SPEED" != "0" ] && [ -n "$DOWNLOAD_SPEED" ]; then
        SPEED_MB=$(echo "scale=2; $DOWNLOAD_SPEED / 1024 / 1024" | bc -l)
    else
        SPEED_MB="0.00"
    fi
    
    # 显示状态
    echo "$(date '+%H:%M:%S') | 状态: $STATUS | 进度: ${PROGRESS}% | 大小: ${COMPLETED_MB}MB/${TOTAL_MB}MB | 速度: ${SPEED_MB}MB/s | 连接: $CONNECTIONS | 种子: $NUM_SEEDERS"
    
    # 检查是否完成
    if [ "$STATUS" = "complete" ]; then
        echo ""
        echo "🎉 下载完成！"
        echo "📁 下载目录: /tmp/downloads"
        echo "📋 文件列表:"
        ls -la /tmp/downloads/
        break
    elif [ "$STATUS" = "error" ]; then
        echo ""
        echo "❌ 下载失败！"
        ERROR_MESSAGE=$(echo "$RESPONSE" | grep -o '"errorMessage":"[^"]*"' | cut -d'"' -f4)
        echo "错误信息: $ERROR_MESSAGE"
        break
    elif [ "$STATUS" = "removed" ]; then
        echo ""
        echo "⚠️ 下载任务被移除"
        break
    fi
    
    sleep 5
done

echo ""
echo "监控结束"
