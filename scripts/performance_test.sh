#!/bin/bash

# DoodStream性能测试脚本
# 用于测试不同并发数下的上传性能，优化带宽利用率

set -e

echo "🚀 DoodStream性能测试和并发优化"
echo "=================================="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go环境未安装"
    exit 1
fi

# 进入项目目录
cd "$(dirname "$0")/.."

# 检查配置文件
if [ ! -f "config.yaml" ]; then
    echo "❌ 配置文件不存在"
    exit 1
fi

# 检查DoodStream配置
echo "📋 检查DoodStream配置..."
if ! grep -q "upload_provider.*doodstream" config.yaml; then
    echo "⚠️  当前配置不是DoodStream模式"
    echo "💡 建议在config.yaml中设置: upload_provider: doodstream"
fi

# 创建日志目录
mkdir -p logs/performance

# 获取当前时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/performance/test_${TIMESTAMP}.log"

echo "📝 测试日志将保存到: $LOG_FILE"

# 运行性能测试
echo "🧪 开始性能测试..."
echo "⏳ 这可能需要几分钟时间，请耐心等待..."

# 运行测试并保存日志
go run tests/performance_test.go 2>&1 | tee "$LOG_FILE"

# 检查测试结果
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo ""
    echo "✅ 性能测试完成！"
    echo "📊 详细结果已保存到: $LOG_FILE"
    
    # 提取关键指标
    echo ""
    echo "📈 关键性能指标摘要:"
    echo "===================="
    
    # 从日志中提取最佳配置
    if grep -q "推荐最优配置" "$LOG_FILE"; then
        echo "🏆 最优配置:"
        grep -A 10 "推荐最优配置" "$LOG_FILE" | head -10
    fi
    
    # 提取带宽利用率
    echo ""
    echo "📊 带宽利用率统计:"
    grep "带宽利用率" "$LOG_FILE" | tail -5
    
else
    echo ""
    echo "❌ 性能测试失败"
    echo "📝 请检查日志文件: $LOG_FILE"
    exit 1
fi

# 生成性能优化建议
echo ""
echo "💡 性能优化建议:"
echo "================"

# 检查是否有失败的上传
if grep -q "失败上传" "$LOG_FILE"; then
    echo "⚠️  检测到上传失败，建议:"
    echo "   1. 检查网络连接稳定性"
    echo "   2. 适当降低并发数"
    echo "   3. 增加重试次数和超时时间"
fi

# 检查带宽利用率
MAX_UTIL=$(grep "带宽利用率" "$LOG_FILE" | grep -o "[0-9.]*%" | grep -o "[0-9.]*" | sort -n | tail -1)
if [ ! -z "$MAX_UTIL" ]; then
    if (( $(echo "$MAX_UTIL > 80" | bc -l) )); then
        echo "🎯 带宽利用率良好 (${MAX_UTIL}%)"
        echo "   当前配置已充分利用网络资源"
    elif (( $(echo "$MAX_UTIL < 50" | bc -l) )); then
        echo "📈 带宽利用率偏低 (${MAX_UTIL}%)"
        echo "   建议增加并发数以提高性能"
    fi
fi

echo ""
echo "🔧 配置优化建议:"
echo "   1. 根据测试结果调整并发数"
echo "   2. 优化速率限制参数"
echo "   3. 考虑网络环境和API限制"
echo "   4. 定期进行性能测试"

echo ""
echo "📚 更多信息:"
echo "   - 完整测试日志: $LOG_FILE"
echo "   - 性能测试代码: tests/performance_test.go"
echo "   - 配置文件: config.yaml"

echo ""
echo "✨ 性能测试完成！"