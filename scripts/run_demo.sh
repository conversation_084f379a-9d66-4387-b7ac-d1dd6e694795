#!/bin/bash

# 磁力下载和文件处理演示脚本
# 演示目标: magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5

set -e

echo "🚀 磁力下载和文件处理系统演示"
echo "=================================================="
echo "📋 目标磁力链接: magnet:?xt=urn:btih:5219B49F5CF037D8CE9A8E0E0C7AD12EE2AC3C69&dn=SSIS-936-C_GG5"
echo "🎯 演示功能: 下载 -> 分片 -> 加密 -> 上传 -> 播放列表"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go 1.21+"
    exit 1
fi

echo "✅ Go 环境检查通过"

# 检查项目依赖
echo "📦 检查项目依赖..."
if [ ! -f "go.mod" ]; then
    echo "❌ 未找到 go.mod 文件，请在项目根目录运行此脚本"
    exit 1
fi

# 下载依赖
echo "📥 下载项目依赖..."
go mod download
echo "✅ 依赖下载完成"

# 创建必要的目录
echo "📁 创建工作目录..."
mkdir -p /tmp/magnet-demo
mkdir -p /tmp/fileprocessor
mkdir -p logs
echo "✅ 目录创建完成"

# 检查配置文件
if [ ! -f "config.yaml" ]; then
    echo "⚠️ 未找到 config.yaml，使用示例配置..."
    cp config.example.yaml config.yaml
fi

echo "✅ 配置文件检查完成"

# 运行文件处理工作流程演示
echo ""
echo "🎬 开始运行文件处理工作流程演示..."
echo "=================================================="

cd scripts
go run demo_workflow.go

echo ""
echo "=================================================="
echo "🎉 演示完成！"
echo ""
echo "📊 演示总结:"
echo "  ✅ 模拟文件下载 (5MB 演示文件)"
echo "  ✅ 1MB 精确分片处理"
echo "  ✅ AES-GCM 256-bit 加密"
echo "  ✅ 模拟 imgbb 图床上传"
echo "  ✅ HLS m3u8 播放列表生成"
echo ""
echo "📁 生成的文件:"
echo "  📋 播放列表: /tmp/magnet-demo/SSIS-936-C_GG5.m3u8"
echo "  📦 演示文件: /tmp/magnet-demo/SSIS-936-C_GG5.mp4"
echo ""
echo "🔗 在实际使用中:"
echo "  1. 配置真实的 imgbb API 密钥"
echo "  2. 启动 aria2 下载服务"
echo "  3. 启动完整的 Web 服务"
echo "  4. 通过 API 或 Web 界面创建下载任务"
echo ""
echo "📖 更多信息请查看 README.md"
echo "=================================================="
