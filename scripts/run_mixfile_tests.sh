#!/bin/bash

# MixFile测试运行脚本
echo "🧪 MixFile系统测试套件"
echo "========================================"

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="/www/wwwroot/JAVAPI.COM"
cd "$PROJECT_ROOT"

# 检查Go环境
echo -e "${BLUE}📋 检查环境...${NC}"
if ! command -v go &> /dev/null; then
    echo -e "${RED}❌ Go未安装或不在PATH中${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Go版本: $(go version)${NC}"

# 创建测试结果目录
TEST_RESULTS_DIR="$PROJECT_ROOT/test_results"
mkdir -p "$TEST_RESULTS_DIR"

# 运行函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local output_file="$TEST_RESULTS_DIR/${test_name}_$(date +%Y%m%d_%H%M%S).log"
    
    echo -e "${BLUE}🔄 运行 $test_name...${NC}"
    
    if eval "$test_command" > "$output_file" 2>&1; then
        echo -e "${GREEN}✅ $test_name 通过${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name 失败${NC}"
        echo -e "${YELLOW}📄 日志文件: $output_file${NC}"
        return 1
    fi
}

# 测试计数器
total_tests=0
passed_tests=0

echo -e "\n${BLUE}🧪 开始运行MixFile测试...${NC}"

# 1. 隐写术测试
echo -e "\n${YELLOW}📋 1. 隐写术功能测试${NC}"
total_tests=$((total_tests + 1))
if run_test "steganography" "go test -v ./tests/mixfile/steganography_test.go"; then
    passed_tests=$((passed_tests + 1))
fi

# 2. 集成测试
echo -e "\n${YELLOW}📋 2. MixFile集成测试${NC}"
total_tests=$((total_tests + 1))
if run_test "integration" "go test -v ./tests/mixfile/integration_test.go"; then
    passed_tests=$((passed_tests + 1))
fi

# 3. 性能测试
echo -e "\n${YELLOW}📋 3. 性能测试${NC}"
total_tests=$((total_tests + 1))
if run_test "performance" "go test -v ./tests/mixfile/performance_test.go -timeout=5m"; then
    passed_tests=$((passed_tests + 1))
fi

# 4. 基准测试
echo -e "\n${YELLOW}📋 4. 基准测试${NC}"
total_tests=$((total_tests + 1))
if run_test "benchmark" "go test -bench=. ./tests/mixfile/ -benchmem"; then
    passed_tests=$((passed_tests + 1))
fi

# 5. 包测试
echo -e "\n${YELLOW}📋 5. 包级别测试${NC}"
packages=(
    "./pkg/steganography"
    "./pkg/mixfile"
)

for package in "${packages[@]}"; do
    if [ -d "$package" ]; then
        total_tests=$((total_tests + 1))
        package_name=$(basename "$package")
        if run_test "package_$package_name" "go test -v $package"; then
            passed_tests=$((passed_tests + 1))
        fi
    fi
done

# 6. 竞态条件测试
echo -e "\n${YELLOW}📋 6. 竞态条件测试${NC}"
total_tests=$((total_tests + 1))
if run_test "race_condition" "go test -race -v ./tests/mixfile/"; then
    passed_tests=$((passed_tests + 1))
fi

# 7. 内存泄漏测试
echo -e "\n${YELLOW}📋 7. 内存泄漏测试${NC}"
total_tests=$((total_tests + 1))
if run_test "memory_leak" "go test -v ./tests/mixfile/performance_test.go -run=TestMixFileMemoryUsage"; then
    passed_tests=$((passed_tests + 1))
fi

# 8. 代码覆盖率测试
echo -e "\n${YELLOW}📋 8. 代码覆盖率测试${NC}"
total_tests=$((total_tests + 1))
coverage_file="$TEST_RESULTS_DIR/coverage.out"
if run_test "coverage" "go test -coverprofile=$coverage_file ./pkg/mixfile/ ./pkg/steganography/"; then
    passed_tests=$((passed_tests + 1))
    
    # 生成覆盖率报告
    if command -v go &> /dev/null; then
        coverage_html="$TEST_RESULTS_DIR/coverage.html"
        go tool cover -html="$coverage_file" -o "$coverage_html" 2>/dev/null
        if [ -f "$coverage_html" ]; then
            echo -e "${GREEN}📊 覆盖率报告: $coverage_html${NC}"
        fi
        
        # 显示覆盖率统计
        coverage_percent=$(go tool cover -func="$coverage_file" 2>/dev/null | tail -1 | awk '{print $3}')
        if [ -n "$coverage_percent" ]; then
            echo -e "${GREEN}📈 代码覆盖率: $coverage_percent${NC}"
        fi
    fi
fi

# 9. 演示程序测试
echo -e "\n${YELLOW}📋 9. 演示程序编译测试${NC}"
total_tests=$((total_tests + 1))
if run_test "demo_compile" "go build -o $TEST_RESULTS_DIR/mixfile_demo ./cmd/mixfile_demo/"; then
    passed_tests=$((passed_tests + 1))
    echo -e "${GREEN}🎯 演示程序编译成功: $TEST_RESULTS_DIR/mixfile_demo${NC}"
fi

# 10. 静态分析
echo -e "\n${YELLOW}📋 10. 静态分析${NC}"
if command -v golint &> /dev/null; then
    total_tests=$((total_tests + 1))
    if run_test "lint" "golint ./pkg/mixfile/ ./pkg/steganography/"; then
        passed_tests=$((passed_tests + 1))
    fi
else
    echo -e "${YELLOW}⚠️ golint未安装，跳过静态分析${NC}"
fi

# 生成测试报告
echo -e "\n${BLUE}📊 生成测试报告...${NC}"
report_file="$TEST_RESULTS_DIR/test_report_$(date +%Y%m%d_%H%M%S).md"

cat > "$report_file" << EOF
# MixFile系统测试报告

**测试时间**: $(date)
**测试环境**: $(go version)
**项目路径**: $PROJECT_ROOT

## 测试结果概览

- **总测试数**: $total_tests
- **通过测试**: $passed_tests
- **失败测试**: $((total_tests - passed_tests))
- **成功率**: $(echo "scale=2; $passed_tests * 100 / $total_tests" | bc -l)%

## 测试详情

### 功能测试
- [x] 隐写术功能测试
- [x] MixFile集成测试
- [x] 性能测试
- [x] 基准测试

### 质量测试
- [x] 包级别测试
- [x] 竞态条件测试
- [x] 内存泄漏测试
- [x] 代码覆盖率测试

### 构建测试
- [x] 演示程序编译测试
- [x] 静态分析

## 测试文件

EOF

# 添加测试文件列表
echo "### 测试日志文件" >> "$report_file"
for log_file in "$TEST_RESULTS_DIR"/*.log; do
    if [ -f "$log_file" ]; then
        echo "- $(basename "$log_file")" >> "$report_file"
    fi
done

echo -e "${GREEN}📄 测试报告: $report_file${NC}"

# 输出最终结果
echo -e "\n========================================"
echo -e "${BLUE}🎯 MixFile测试套件完成${NC}"
echo -e "📊 测试结果: ${GREEN}$passed_tests${NC}/${BLUE}$total_tests${NC}"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    exit 0
else
    echo -e "${RED}❌ 有 $((total_tests - passed_tests)) 个测试失败${NC}"
    echo -e "${YELLOW}📋 请查看测试日志了解详情${NC}"
    exit 1
fi