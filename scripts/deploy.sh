#!/bin/bash

# 磁力下载器部署脚本
# 使用方法: ./scripts/deploy.sh [environment] [action]
# 环境: dev, staging, prod
# 操作: up, down, restart, logs, build

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
ENVIRONMENT=${1:-prod}
ACTION=${2:-up}
PROJECT_NAME="magnet-downloader"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    case $ENVIRONMENT in
        dev)
            COMPOSE_FILE="docker-compose.dev.yml"
            ENV_FILE=".env.dev"
            ;;
        staging)
            COMPOSE_FILE="docker-compose.staging.yml"
            ENV_FILE=".env.staging"
            ;;
        prod)
            COMPOSE_FILE="docker-compose.yml"
            ENV_FILE=".env"
            ;;
        *)
            log_error "未知环境: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    # 检查环境文件
    if [ ! -f "$ENV_FILE" ]; then
        if [ -f ".env.example" ]; then
            log_warning "环境文件 $ENV_FILE 不存在，从 .env.example 复制"
            cp .env.example $ENV_FILE
        else
            log_error "环境文件 $ENV_FILE 和 .env.example 都不存在"
            exit 1
        fi
    fi
    
    export COMPOSE_FILE
    export COMPOSE_PROJECT_NAME="${PROJECT_NAME}-${ENVIRONMENT}"
    
    log_success "环境设置完成: $ENVIRONMENT"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p downloads
    mkdir -p logs
    mkdir -p config
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p data/prometheus
    mkdir -p data/grafana
    
    # 设置权限
    chmod 755 downloads logs config
    
    log_success "目录创建完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动基础服务
    docker-compose -f $COMPOSE_FILE up -d postgres redis aria2
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 启动主应用
    docker-compose -f $COMPOSE_FILE up -d magnet-downloader
    
    # 启动其他服务
    docker-compose -f $COMPOSE_FILE up -d
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    docker-compose -f $COMPOSE_FILE down
    
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    stop_services
    start_services
    
    log_success "服务重启完成"
}

# 查看日志
show_logs() {
    log_info "显示服务日志..."
    
    docker-compose -f $COMPOSE_FILE logs -f --tail=100
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    
    docker-compose -f $COMPOSE_FILE ps
    
    # 检查健康状态
    log_info "检查应用健康状态..."
    sleep 5
    
    if curl -f http://localhost:8080/health &> /dev/null; then
        log_success "应用健康检查通过"
    else
        log_warning "应用健康检查失败"
    fi
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    
    # 停止并删除容器
    docker-compose -f $COMPOSE_FILE down -v
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的卷
    docker volume prune -f
    
    log_success "清理完成"
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # 备份数据库
    docker-compose -f $COMPOSE_FILE exec -T postgres pg_dump -U postgres magnet_downloader > $BACKUP_DIR/database.sql
    
    # 备份下载文件
    tar -czf $BACKUP_DIR/downloads.tar.gz downloads/
    
    # 备份配置文件
    tar -czf $BACKUP_DIR/config.tar.gz config/
    
    log_success "数据备份完成: $BACKUP_DIR"
}

# 显示帮助信息
show_help() {
    echo "磁力下载器部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [environment] [action]"
    echo ""
    echo "环境:"
    echo "  dev      - 开发环境"
    echo "  staging  - 测试环境"
    echo "  prod     - 生产环境 (默认)"
    echo ""
    echo "操作:"
    echo "  up       - 启动服务 (默认)"
    echo "  down     - 停止服务"
    echo "  restart  - 重启服务"
    echo "  build    - 构建镜像"
    echo "  logs     - 查看日志"
    echo "  status   - 检查状态"
    echo "  cleanup  - 清理资源"
    echo "  backup   - 备份数据"
    echo "  help     - 显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 prod up          # 生产环境启动"
    echo "  $0 dev logs         # 开发环境查看日志"
    echo "  $0 staging restart  # 测试环境重启"
}

# 主函数
main() {
    case $ACTION in
        up)
            check_dependencies
            setup_environment
            create_directories
            start_services
            check_status
            ;;
        down)
            setup_environment
            stop_services
            ;;
        restart)
            check_dependencies
            setup_environment
            restart_services
            check_status
            ;;
        build)
            check_dependencies
            setup_environment
            build_images
            ;;
        logs)
            setup_environment
            show_logs
            ;;
        status)
            setup_environment
            check_status
            ;;
        cleanup)
            setup_environment
            cleanup
            ;;
        backup)
            setup_environment
            backup_data
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知操作: $ACTION"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main
