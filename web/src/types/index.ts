// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'user' | 'guest';
  status: 'active' | 'inactive' | 'banned';
  avatar?: string;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}

export interface UserProfile {
  id: number;
  username: string;
  email: string;
  role: string;
  status: string;
  avatar?: string;
}

// 任务相关类型
export interface DownloadTask {
  id: number;
  magnet_uri: string;
  task_name: string;
  status: TaskStatus;
  priority: TaskPriority;
  user_id: number;
  save_path: string;
  total_size: number;
  downloaded_size: number;
  upload_size: number;
  download_speed: number;
  upload_speed: number;
  progress: number;
  aria2_gid?: string;
  error_message?: string;
  max_retries: number;
  retry_count: number;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
}

export type TaskStatus = 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';
export type TaskPriority = 'low' | 'normal' | 'high' | 'urgent';

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  error?: any;
  timestamp: number;
  request_id?: string;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// 登录相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: UserProfile;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

// 任务创建请求
export interface CreateTaskRequest {
  magnet_uri: string;
  task_name?: string;
  save_path?: string;
  priority?: TaskPriority;
  options?: Record<string, any>;
}

// 任务列表请求
export interface ListTasksRequest {
  page?: number;
  page_size?: number;
  status?: TaskStatus;
  priority?: TaskPriority;
  search?: string;
  sort_by?: string;
  sort_desc?: boolean;
}

// 系统统计
export interface SystemStats {
  tasks: TaskStats;
  users: UserStats;
  runtime: RuntimeStats;
}

export interface TaskStats {
  total_tasks: number;
  active_tasks: number;
  pending_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  paused_tasks: number;
  cancelled_tasks: number;
  total_size: number;
  downloaded_size: number;
  download_speed: number;
  upload_speed: number;
  success_rate: number;
}

export interface UserStats {
  total_users: number;
  active_users: number;
  admin_users: number;
  banned_users: number;
  new_users_24h: number;
  login_users_24h: number;
}

export interface RuntimeStats {
  goroutines: number;
  memory_alloc: number;
  memory_sys: number;
  gc_count: number;
  uptime: number;
}

// WebSocket消息类型
export interface WebSocketMessage {
  id: string;
  type: string;
  data: any;
  user_id: number;
  timestamp: number;
  channel?: string;
}

export interface TaskProgressData {
  task_id: number;
  task_name: string;
  status: string;
  progress: number;
  download_speed: number;
  upload_speed: number;
  total_size: number;
  downloaded_size: number;
  eta: number;
  error_message?: string;
}

// 系统配置
export interface SystemConfig {
  id: number;
  key: string;
  value: string;
  type: string;
  category: string;
  description: string;
  is_public: boolean;
  is_editable: boolean;
  created_at: string;
  updated_at: string;
}

// 表格列配置
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

// 菜单项
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  path?: string;
}

// 面包屑项
export interface BreadcrumbItem {
  title: string;
  path?: string;
}
