import { WebSocketMessage, TaskProgressData } from '../types';

export type WebSocketEventHandler = (message: WebSocketMessage) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private isConnecting = false;
  private shouldReconnect = true;

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Already connecting'));
        return;
      }

      this.isConnecting = true;
      const token = localStorage.getItem('access_token');
      
      if (!token) {
        this.isConnecting = false;
        reject(new Error('No access token'));
        return;
      }

      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/api/v1/ws`;

      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // 发送认证信息
          this.send({
            type: 'auth',
            data: { token }
          });
          
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.onMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.ws = null;

          if (this.shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            setTimeout(() => {
              this.connect().catch(console.error);
            }, this.reconnectInterval);
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  disconnect(): void {
    this.shouldReconnect = false;
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  send(message: any): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  private onMessage(message: WebSocketMessage): void {
    // 触发通用事件处理器
    const handlers = this.eventHandlers.get('*') || [];
    handlers.forEach(handler => handler(message));

    // 触发特定类型的事件处理器
    const typeHandlers = this.eventHandlers.get(message.type) || [];
    typeHandlers.forEach(handler => handler(message));
  }

  // 订阅消息事件
  on(eventType: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  // 取消订阅
  off(eventType: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // 订阅任务进度更新
  onTaskProgress(handler: (data: TaskProgressData) => void): void {
    this.on('task_progress', (message) => {
      handler(message.data as TaskProgressData);
    });
  }

  // 订阅任务状态变更
  onTaskStatusChange(handler: (data: any) => void): void {
    const taskEvents = [
      'task_created',
      'task_started',
      'task_paused',
      'task_resumed',
      'task_completed',
      'task_failed',
      'task_cancelled',
      'task_deleted'
    ];

    taskEvents.forEach(eventType => {
      this.on(eventType, (message) => {
        handler({
          type: eventType,
          data: message.data
        });
      });
    });
  }

  // 订阅系统通知
  onSystemNotification(handler: (data: any) => void): void {
    this.on('system_notification', (message) => {
      handler(message.data);
    });
  }

  // 订阅系统告警
  onSystemAlert(handler: (data: any) => void): void {
    this.on('system_alert', (message) => {
      handler(message.data);
    });
  }

  // 获取连接状态
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  // 获取连接状态文本
  get connectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }
}

export const webSocketService = new WebSocketService();
export default webSocketService;
