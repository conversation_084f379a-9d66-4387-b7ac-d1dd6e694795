import { TaskStatus, TaskPriority } from '../types';

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化速度
export const formatSpeed = (bytesPerSecond: number): string => {
  return formatFileSize(bytesPerSecond) + '/s';
};

// 格式化时间
export const formatDuration = (seconds: number): string => {
  if (seconds === 0) return '0秒';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  const parts = [];
  if (hours > 0) parts.push(`${hours}小时`);
  if (minutes > 0) parts.push(`${minutes}分钟`);
  if (secs > 0) parts.push(`${secs}秒`);
  
  return parts.join('');
};

// 格式化相对时间
export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSeconds < 60) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString('zh-CN');
  }
};

// 格式化日期时间
export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 获取任务状态颜色
export const getTaskStatusColor = (status: TaskStatus): string => {
  switch (status) {
    case 'pending':
      return 'default';
    case 'running':
      return 'processing';
    case 'paused':
      return 'warning';
    case 'completed':
      return 'success';
    case 'failed':
      return 'error';
    case 'cancelled':
      return 'default';
    default:
      return 'default';
  }
};

// 获取任务状态文本
export const getTaskStatusText = (status: TaskStatus): string => {
  switch (status) {
    case 'pending':
      return '等待中';
    case 'running':
      return '下载中';
    case 'paused':
      return '已暂停';
    case 'completed':
      return '已完成';
    case 'failed':
      return '失败';
    case 'cancelled':
      return '已取消';
    default:
      return '未知';
  }
};

// 获取任务优先级颜色
export const getTaskPriorityColor = (priority: TaskPriority): string => {
  switch (priority) {
    case 'low':
      return 'default';
    case 'normal':
      return 'processing';
    case 'high':
      return 'warning';
    case 'urgent':
      return 'error';
    default:
      return 'default';
  }
};

// 获取任务优先级文本
export const getTaskPriorityText = (priority: TaskPriority): string => {
  switch (priority) {
    case 'low':
      return '低';
    case 'normal':
      return '普通';
    case 'high':
      return '高';
    case 'urgent':
      return '紧急';
    default:
      return '未知';
  }
};

// 验证磁力链接格式
export const validateMagnetURI = (uri: string): boolean => {
  const magnetRegex = /^magnet:\?xt=urn:btih:[a-fA-F0-9]{40}.*$/;
  return magnetRegex.test(uri);
};

// 从磁力链接提取哈希值
export const extractHashFromMagnet = (uri: string): string | null => {
  const match = uri.match(/xt=urn:btih:([a-fA-F0-9]{40})/);
  return match ? match[1] : null;
};

// 从磁力链接提取名称
export const extractNameFromMagnet = (uri: string): string | null => {
  const match = uri.match(/dn=([^&]+)/);
  return match ? decodeURIComponent(match[1]) : null;
};

// 计算进度百分比
export const calculateProgress = (downloaded: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((downloaded / total) * 100 * 100) / 100; // 保留两位小数
};

// 计算预计剩余时间
export const calculateETA = (downloaded: number, total: number, speed: number): number => {
  if (speed === 0 || total === 0 || downloaded >= total) return 0;
  return Math.floor((total - downloaded) / speed);
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// 复制到剪贴板
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    const success = document.execCommand('copy');
    document.body.removeChild(textArea);
    return success;
  }
};

// 生成随机ID
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

// 深度克隆对象
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

// 检查是否为移动设备
export const isMobile = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// 获取文件扩展名
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

// 获取文件类型图标
export const getFileTypeIcon = (filename: string): string => {
  const ext = getFileExtension(filename).toLowerCase();
  
  if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv'].includes(ext)) {
    return 'video';
  } else if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(ext)) {
    return 'audio';
  } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(ext)) {
    return 'image';
  } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(ext)) {
    return 'document';
  } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return 'archive';
  } else {
    return 'file';
  }
};
