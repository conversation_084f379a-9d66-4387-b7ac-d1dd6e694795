.App {
  text-align: left;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 布局样式 */
.layout-container {
  min-height: 100vh;
}

.layout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.layout-header.dark {
  background: #001529;
  border-bottom: 1px solid #303030;
}

.layout-logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
}

.layout-logo .anticon {
  margin-right: 8px;
  font-size: 24px;
}

.layout-user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.layout-content {
  padding: 24px;
  min-height: calc(100vh - 64px);
}

/* 登录页面样式 */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  width: 400px;
  padding: 40px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-title {
  text-align: center;
  margin-bottom: 32px;
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

/* 任务列表样式 */
.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.task-progress-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-progress-text {
  font-size: 12px;
  color: #666;
}

.task-speed-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

/* 仪表板样式 */
.dashboard-stats {
  margin-bottom: 24px;
}

.dashboard-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .dashboard-charts {
    grid-template-columns: 1fr;
  }
}

.dashboard-recent-tasks {
  margin-bottom: 24px;
}

/* 统计卡片样式 */
.stat-card {
  text-align: center;
}

.stat-card .ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.stat-card .ant-statistic-content {
  font-size: 24px;
  font-weight: bold;
}

/* 进度条样式 */
.progress-with-text {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  min-width: 50px;
  font-size: 12px;
  color: #666;
}

/* 状态标签样式 */
.status-tag {
  font-size: 12px;
}

.priority-tag {
  font-size: 12px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 4px;
}

.action-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .layout-content {
    padding: 16px;
  }
  
  .task-filters {
    flex-direction: column;
  }
  
  .task-list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .login-form {
    width: 90%;
    margin: 0 20px;
  }
}

/* 暗色主题适配 */
.ant-layout.dark {
  background: #141414;
}

.ant-layout-sider.dark {
  background: #001529;
}

.ant-menu.dark {
  background: #001529;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态样式 */
.empty-container {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

/* 错误状态样式 */
.error-container {
  text-align: center;
  padding: 40px 20px;
  color: #ff4d4f;
}

/* 工具提示样式 */
.tooltip-content {
  max-width: 300px;
  word-wrap: break-word;
}

/* 表格样式优化 */
.ant-table-tbody > tr > td {
  padding: 12px 16px;
}

.ant-table-thead > tr > th {
  padding: 16px;
  font-weight: 600;
}

/* 表单样式优化 */
.ant-form-item {
  margin-bottom: 20px;
}

.ant-form-item-label > label {
  font-weight: 500;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 页面标题样式 */
.page-title {
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.page-title.dark {
  color: #fff;
}
