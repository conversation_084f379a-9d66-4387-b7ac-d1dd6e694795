import React, { useState } from 'react';
import { Layout as AntLayout, Menu, Dropdown, Avatar, Button, Badge, Tooltip } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  DownloadOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  CloudDownloadOutlined,
  BulbOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  TeamOutlined,
  WifiOutlined
} from '@ant-design/icons';
import { UserProfile, MenuItem } from '../types';
import { webSocketService } from '../services/websocket';

const { Header, Sider, Content } = AntLayout;

interface LayoutProps {
  children: React.ReactNode;
  user: UserProfile;
  onLogout: () => void;
  darkMode: boolean;
  onToggleTheme: () => void;
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  user, 
  onLogout, 
  darkMode, 
  onToggleTheme 
}) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // 菜单项配置
  const menuItems: MenuItem[] = [
    {
      key: '/dashboard',
      label: '仪表板',
      icon: <DashboardOutlined />,
      path: '/dashboard'
    },
    {
      key: '/tasks',
      label: '下载任务',
      icon: <DownloadOutlined />,
      path: '/tasks'
    },
    ...(user.role === 'admin' ? [
      {
        key: '/users',
        label: '用户管理',
        icon: <TeamOutlined />,
        path: '/users'
      },
      {
        key: '/settings',
        label: '系统设置',
        icon: <SettingOutlined />,
        path: '/settings'
      }
    ] : [])
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      label: '个人资料',
      icon: <UserOutlined />,
      onClick: () => navigate('/profile')
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: onLogout
    }
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    const item = menuItems.find(item => item.key === key);
    if (item?.path) {
      navigate(item.path);
    }
  };

  const getConnectionStatus = () => {
    const isConnected = webSocketService.isConnected;
    return (
      <Tooltip title={isConnected ? 'WebSocket已连接' : 'WebSocket未连接'}>
        <Badge 
          status={isConnected ? 'success' : 'error'} 
          text={<WifiOutlined style={{ color: isConnected ? '#52c41a' : '#ff4d4f' }} />}
        />
      </Tooltip>
    );
  };

  return (
    <AntLayout className={`layout-container ${darkMode ? 'dark' : ''}`}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        theme={darkMode ? 'dark' : 'light'}
        style={{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
        }}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: darkMode ? '1px solid #303030' : '1px solid #f0f0f0'
        }}>
          <div className="layout-logo">
            <CloudDownloadOutlined />
            {!collapsed && <span style={{ marginLeft: 8 }}>磁力下载器</span>}
          </div>
        </div>
        
        <Menu
          theme={darkMode ? 'dark' : 'light'}
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ borderRight: 0 }}
        />
      </Sider>

      <AntLayout style={{ marginLeft: collapsed ? 80 : 200, transition: 'margin-left 0.2s' }}>
        <Header className={`layout-header ${darkMode ? 'dark' : ''}`}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: 16, width: 64, height: 64 }}
            />
          </div>

          <div className="layout-user-info">
            {getConnectionStatus()}
            
            <Button
              type="text"
              icon={<BulbOutlined />}
              onClick={onToggleTheme}
              title={darkMode ? '切换到亮色主题' : '切换到暗色主题'}
            />

            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                cursor: 'pointer',
                padding: '0 8px',
                borderRadius: 4,
                transition: 'background-color 0.3s'
              }}>
                <Avatar 
                  size="small" 
                  icon={<UserOutlined />} 
                  src={user.avatar}
                  style={{ marginRight: 8 }}
                />
                <span style={{ color: darkMode ? '#fff' : '#000' }}>
                  {user.username}
                </span>
              </div>
            </Dropdown>
          </div>
        </Header>

        <Content className="layout-content">
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
