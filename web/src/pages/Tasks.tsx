import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Tag, 
  Progress, 
  Space,
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { DownloadTask, CreateTaskRequest, ListTasksRequest, TaskStatus, TaskPriority } from '../types';
import { apiService } from '../services/api';
import { webSocketService } from '../services/websocket';
import { 
  formatFileSize, 
  formatSpeed, 
  getTaskStatusColor, 
  getTaskStatusText,
  getTaskPriorityColor,
  getTaskPriorityText,
  formatRelativeTime,
  validateMagnetURI,
  extractNameFromMagnet
} from '../utils';

const { Option } = Select;

const Tasks: React.FC = () => {
  const [tasks, setTasks] = useState<DownloadTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [filters, setFilters] = useState<ListTasksRequest>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();

  useEffect(() => {
    loadTasks();
    
    // 订阅WebSocket事件
    webSocketService.onTaskProgress(handleTaskProgress);
    webSocketService.onTaskStatusChange(handleTaskStatusChange);
  }, [currentPage, pageSize, filters]);

  const loadTasks = async () => {
    try {
      setLoading(true);
      const response = await apiService.getTasks({
        page: currentPage,
        page_size: pageSize,
        ...filters
      });
      setTasks(response.items);
      setTotal(response.total);
    } catch (error) {
      console.error('Failed to load tasks:', error);
      message.error('加载任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTaskProgress = (data: any) => {
    setTasks(prev => 
      prev.map(task => 
        task.id === data.task_id 
          ? { 
              ...task, 
              progress: data.progress, 
              download_speed: data.download_speed,
              downloaded_size: data.downloaded_size
            }
          : task
      )
    );
  };

  const handleTaskStatusChange = (data: any) => {
    loadTasks(); // 重新加载任务列表
  };

  const handleCreateTask = async (values: CreateTaskRequest) => {
    try {
      // 验证磁力链接
      if (!validateMagnetURI(values.magnet_uri)) {
        message.error('请输入有效的磁力链接');
        return;
      }

      // 如果没有提供任务名称，尝试从磁力链接提取
      if (!values.task_name) {
        const extractedName = extractNameFromMagnet(values.magnet_uri);
        if (extractedName) {
          values.task_name = extractedName;
        }
      }

      await apiService.createTask(values);
      message.success('任务创建成功');
      setCreateModalVisible(false);
      createForm.resetFields();
      loadTasks();
    } catch (error: any) {
      console.error('Failed to create task:', error);
      message.error(error.response?.data?.message || '创建任务失败');
    }
  };

  const handleTaskAction = async (taskId: number, action: string) => {
    try {
      switch (action) {
        case 'start':
          await apiService.startTask(taskId);
          break;
        case 'pause':
          await apiService.pauseTask(taskId);
          break;
        case 'resume':
          await apiService.resumeTask(taskId);
          break;
        case 'cancel':
          await apiService.cancelTask(taskId);
          break;
        case 'retry':
          await apiService.retryTask(taskId);
          break;
        case 'delete':
          await apiService.deleteTask(taskId);
          break;
      }
      
      const actionText = {
        start: '开始',
        pause: '暂停', 
        resume: '恢复',
        cancel: '取消',
        retry: '重试',
        delete: '删除'
      }[action];
      
      message.success(`任务${actionText}成功`);
      loadTasks();
    } catch (error: any) {
      console.error(`Failed to ${action} task:`, error);
      message.error(error.response?.data?.message || '操作失败');
    }
  };

  const columns = [
    {
      title: '任务名称',
      dataIndex: 'task_name',
      key: 'task_name',
      ellipsis: true,
      render: (text: string, record: DownloadTask) => (
        <div>
          <Tooltip title={text || '未命名任务'}>
            <div style={{ fontWeight: 500, marginBottom: 4 }}>
              {text || '未命名任务'}
            </div>
          </Tooltip>
          <div style={{ fontSize: 12, color: '#666' }}>
            {formatFileSize(record.total_size)}
          </div>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: TaskStatus) => (
        <Tag color={getTaskStatusColor(status)}>
          {getTaskStatusText(status)}
        </Tag>
      )
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: TaskPriority) => (
        <Tag color={getTaskPriorityColor(priority)} size="small">
          {getTaskPriorityText(priority)}
        </Tag>
      )
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 200,
      render: (progress: number, record: DownloadTask) => (
        <div>
          <Progress 
            percent={progress} 
            size="small" 
            status={record.status === 'failed' ? 'exception' : 'normal'}
            format={(percent) => `${percent}%`}
          />
          {record.status === 'running' && (
            <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
              <span>↓ {formatSpeed(record.download_speed)}</span>
              {record.upload_speed > 0 && (
                <span style={{ marginLeft: 8 }}>↑ {formatSpeed(record.upload_speed)}</span>
              )}
            </div>
          )}
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time: string) => (
        <Tooltip title={new Date(time).toLocaleString()}>
          {formatRelativeTime(time)}
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record: DownloadTask) => (
        <Space size="small">
          {record.status === 'pending' && (
            <Button 
              size="small" 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={() => handleTaskAction(record.id, 'start')}
            >
              开始
            </Button>
          )}
          {record.status === 'running' && (
            <Button 
              size="small" 
              icon={<PauseCircleOutlined />}
              onClick={() => handleTaskAction(record.id, 'pause')}
            >
              暂停
            </Button>
          )}
          {record.status === 'paused' && (
            <Button 
              size="small" 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={() => handleTaskAction(record.id, 'resume')}
            >
              恢复
            </Button>
          )}
          {record.status === 'failed' && (
            <Button 
              size="small" 
              type="primary" 
              icon={<ReloadOutlined />}
              onClick={() => handleTaskAction(record.id, 'retry')}
            >
              重试
            </Button>
          )}
          {['running', 'pending', 'paused'].includes(record.status) && (
            <Button 
              size="small" 
              danger 
              icon={<StopOutlined />}
              onClick={() => handleTaskAction(record.id, 'cancel')}
            >
              取消
            </Button>
          )}
          <Popconfirm
            title="确定要删除这个任务吗？"
            onConfirm={() => handleTaskAction(record.id, 'delete')}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              size="small" 
              danger 
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys as number[]),
  };

  return (
    <div>
      <h1 className="page-title">下载任务</h1>
      
      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索任务名称"
              allowClear
              onSearch={(value) => {
                setFilters({ ...filters, search: value });
                setCurrentPage(1);
              }}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Select
              placeholder="状态筛选"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => {
                setFilters({ ...filters, status: value });
                setCurrentPage(1);
              }}
            >
              <Option value="pending">等待中</Option>
              <Option value="running">下载中</Option>
              <Option value="paused">已暂停</Option>
              <Option value="completed">已完成</Option>
              <Option value="failed">失败</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
              style={{ width: '100%' }}
            >
              新建任务
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 任务表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 创建任务模态框 */}
      <Modal
        title="创建下载任务"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateTask}
        >
          <Form.Item
            name="magnet_uri"
            label="磁力链接"
            rules={[
              { required: true, message: '请输入磁力链接' },
              { 
                validator: (_, value) => {
                  if (value && !validateMagnetURI(value)) {
                    return Promise.reject('请输入有效的磁力链接');
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Input.TextArea 
              rows={3} 
              placeholder="magnet:?xt=urn:btih:..."
              onChange={(e) => {
                const value = e.target.value;
                if (value && !createForm.getFieldValue('task_name')) {
                  const extractedName = extractNameFromMagnet(value);
                  if (extractedName) {
                    createForm.setFieldsValue({ task_name: extractedName });
                  }
                }
              }}
            />
          </Form.Item>
          
          <Form.Item
            name="task_name"
            label="任务名称"
          >
            <Input placeholder="留空将自动从磁力链接提取" />
          </Form.Item>
          
          <Form.Item
            name="save_path"
            label="保存路径"
          >
            <Input placeholder="留空使用默认路径" />
          </Form.Item>
          
          <Form.Item
            name="priority"
            label="优先级"
            initialValue="normal"
          >
            <Select>
              <Option value="low">低</Option>
              <Option value="normal">普通</Option>
              <Option value="high">高</Option>
              <Option value="urgent">紧急</Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建任务
              </Button>
              <Button onClick={() => {
                setCreateModalVisible(false);
                createForm.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Tasks;
