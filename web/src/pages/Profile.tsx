import React, { useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Avatar, 
  Row, 
  Col, 
  Divider,
  message,
  Modal
} from 'antd';
import { UserOutlined, EditOutlined, LockOutlined } from '@ant-design/icons';
import { UserProfile } from '../types';
import { apiService } from '../services/api';
import { formatDateTime } from '../utils';

interface ProfileProps {
  user: UserProfile;
  onUpdate: (user: UserProfile) => void;
}

const Profile: React.FC<ProfileProps> = ({ user, onUpdate }) => {
  const [loading, setLoading] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();

  const handleUpdateProfile = async (values: any) => {
    try {
      setLoading(true);
      const updatedUser = await apiService.updateUserProfile(values);
      onUpdate(updatedUser);
      message.success('个人资料更新成功');
    } catch (error: any) {
      console.error('Failed to update profile:', error);
      message.error(error.response?.data?.message || '更新个人资料失败');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePassword = async (values: any) => {
    try {
      await apiService.changePassword({
        old_password: values.old_password,
        new_password: values.new_password
      });
      message.success('密码修改成功');
      setPasswordModalVisible(false);
      passwordForm.resetFields();
    } catch (error: any) {
      console.error('Failed to change password:', error);
      message.error(error.response?.data?.message || '密码修改失败');
    }
  };

  return (
    <div>
      <h1 className="page-title">个人资料</h1>
      
      <Row gutter={[24, 24]}>
        {/* 基本信息 */}
        <Col xs={24} lg={8}>
          <Card title="基本信息">
            <div style={{ textAlign: 'center', marginBottom: 24 }}>
              <Avatar 
                size={80} 
                icon={<UserOutlined />} 
                src={user.avatar}
                style={{ marginBottom: 16 }}
              />
              <h3>{user.username}</h3>
              <p style={{ color: '#666' }}>{user.email}</p>
            </div>
            
            <Divider />
            
            <div>
              <Row style={{ marginBottom: 8 }}>
                <Col span={8}>用户ID:</Col>
                <Col span={16}>{user.id}</Col>
              </Row>
              <Row style={{ marginBottom: 8 }}>
                <Col span={8}>角色:</Col>
                <Col span={16}>
                  {user.role === 'admin' ? '管理员' : '用户'}
                </Col>
              </Row>
              <Row style={{ marginBottom: 8 }}>
                <Col span={8}>状态:</Col>
                <Col span={16}>
                  {user.status === 'active' ? '活跃' : 
                   user.status === 'inactive' ? '非活跃' : '已禁用'}
                </Col>
              </Row>
            </div>
          </Card>
        </Col>

        {/* 编辑资料 */}
        <Col xs={24} lg={16}>
          <Card 
            title="编辑资料" 
            extra={
              <Button 
                type="primary" 
                icon={<LockOutlined />}
                onClick={() => setPasswordModalVisible(true)}
              >
                修改密码
              </Button>
            }
          >
            <Form
              form={profileForm}
              layout="vertical"
              initialValues={{
                username: user.username,
                email: user.email
              }}
              onFinish={handleUpdateProfile}
            >
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="username"
                    label="用户名"
                    rules={[
                      { required: true, message: '请输入用户名' },
                      { min: 3, max: 50, message: '用户名长度为3-50个字符' }
                    ]}
                  >
                    <Input 
                      placeholder="请输入用户名" 
                      disabled={user.role !== 'admin'} // 非管理员不能修改用户名
                    />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="email"
                    label="邮箱"
                    rules={[
                      { required: true, message: '请输入邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="请输入邮箱" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading}
                  icon={<EditOutlined />}
                >
                  更新资料
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          passwordForm.resetFields();
        }}
        footer={null}
        width={400}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            name="old_password"
            label="当前密码"
            rules={[
              { required: true, message: '请输入当前密码' }
            ]}
          >
            <Input.Password placeholder="请输入当前密码" />
          </Form.Item>
          
          <Form.Item
            name="new_password"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>
          
          <Form.Item
            name="confirm_password"
            label="确认新密码"
            dependencies={['new_password']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('new_password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请确认新密码" />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              修改密码
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Profile;
