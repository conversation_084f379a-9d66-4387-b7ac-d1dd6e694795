import React, { useState, useEffect } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Table, 
  Tag, 
  Progress, 
  Button,
  message,
  Spin
} from 'antd';
import {
  DownloadOutlined,
  CloudDownloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { DownloadTask, SystemStats } from '../types';
import { apiService } from '../services/api';
import { webSocketService } from '../services/websocket';
import { 
  formatFileSize, 
  formatSpeed, 
  getTaskStatusColor, 
  getTaskStatusText,
  formatRelativeTime 
} from '../utils';

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [recentTasks, setRecentTasks] = useState<DownloadTask[]>([]);
  const [taskStats, setTaskStats] = useState<any>(null);

  useEffect(() => {
    loadDashboardData();
    
    // 订阅WebSocket事件
    webSocketService.onTaskProgress(handleTaskProgress);
    webSocketService.onTaskStatusChange(handleTaskStatusChange);
    
    // 定期刷新数据
    const interval = setInterval(loadDashboardData, 30000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // 并行加载数据
      const [systemStats, tasksResponse, taskStatsData] = await Promise.all([
        apiService.getSystemStats(),
        apiService.getTasks({ page: 1, page_size: 10 }),
        apiService.getTaskStats()
      ]);
      
      setStats(systemStats);
      setRecentTasks(tasksResponse.items);
      setTaskStats(taskStatsData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      message.error('加载仪表板数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTaskProgress = (data: any) => {
    setRecentTasks(prev => 
      prev.map(task => 
        task.id === data.task_id 
          ? { ...task, progress: data.progress, download_speed: data.download_speed }
          : task
      )
    );
  };

  const handleTaskStatusChange = (data: any) => {
    if (data.type === 'task_completed' || data.type === 'task_failed') {
      // 刷新数据
      loadDashboardData();
    }
  };

  const handleTaskAction = async (taskId: number, action: string) => {
    try {
      switch (action) {
        case 'start':
          await apiService.startTask(taskId);
          break;
        case 'pause':
          await apiService.pauseTask(taskId);
          break;
        case 'resume':
          await apiService.resumeTask(taskId);
          break;
        case 'cancel':
          await apiService.cancelTask(taskId);
          break;
      }
      message.success(`任务${action === 'start' ? '开始' : action === 'pause' ? '暂停' : action === 'resume' ? '恢复' : '取消'}成功`);
      loadDashboardData();
    } catch (error) {
      message.error(`操作失败`);
    }
  };

  const columns = [
    {
      title: '任务名称',
      dataIndex: 'task_name',
      key: 'task_name',
      ellipsis: true,
      render: (text: string, record: DownloadTask) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text || '未命名任务'}</div>
          <div style={{ fontSize: 12, color: '#666' }}>
            {formatFileSize(record.total_size)}
          </div>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getTaskStatusColor(status as any)}>
          {getTaskStatusText(status as any)}
        </Tag>
      )
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 150,
      render: (progress: number, record: DownloadTask) => (
        <div>
          <Progress 
            percent={progress} 
            size="small" 
            status={record.status === 'failed' ? 'exception' : 'normal'}
          />
          {record.status === 'running' && (
            <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
              {formatSpeed(record.download_speed)}
            </div>
          )}
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time: string) => formatRelativeTime(time)
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: DownloadTask) => (
        <div className="action-buttons">
          {record.status === 'pending' && (
            <Button 
              size="small" 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={() => handleTaskAction(record.id, 'start')}
            >
              开始
            </Button>
          )}
          {record.status === 'running' && (
            <Button 
              size="small" 
              icon={<PauseCircleOutlined />}
              onClick={() => handleTaskAction(record.id, 'pause')}
            >
              暂停
            </Button>
          )}
          {record.status === 'paused' && (
            <Button 
              size="small" 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={() => handleTaskAction(record.id, 'resume')}
            >
              恢复
            </Button>
          )}
        </div>
      )
    }
  ];

  if (loading && !stats) {
    return (
      <div className="loading-container">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <h1 className="page-title">仪表板</h1>
      
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="dashboard-stats">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={taskStats?.total_tasks || 0}
              prefix={<DownloadOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃任务"
              value={taskStats?.active_tasks || 0}
              prefix={<CloudDownloadOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成"
              value={taskStats?.completed_tasks || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="失败任务"
              value={taskStats?.failed_tasks || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="下载统计" size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="总下载量"
                  value={formatFileSize(taskStats?.downloaded_size || 0)}
                  valueStyle={{ fontSize: 16 }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="当前速度"
                  value={formatSpeed(taskStats?.download_speed || 0)}
                  valueStyle={{ fontSize: 16 }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="系统状态" size="small">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="在线用户"
                  value={stats?.users?.active_users || 0}
                  valueStyle={{ fontSize: 16 }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="运行时间"
                  value={Math.floor((stats?.runtime?.uptime || 0) / 3600)}
                  suffix="小时"
                  valueStyle={{ fontSize: 16 }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 最近任务 */}
      <Card 
        title="最近任务" 
        className="dashboard-recent-tasks"
        extra={
          <Button type="link" onClick={() => window.location.href = '/tasks'}>
            查看全部
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={recentTasks}
          rowKey="id"
          pagination={false}
          size="small"
          loading={loading}
        />
      </Card>
    </div>
  );
};

export default Dashboard;
