import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Form, 
  Input, 
  InputNumber, 
  Switch, 
  Button, 
  Table,
  message,
  Row,
  Col,
  Statistic,
  Tag,
  Space,
  Select,
  Divider
} from 'antd';
import { 
  SettingOutlined, 
  DatabaseOutlined, 
  WifiOutlined,
  UserOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { SystemConfig, SystemStats } from '../types';
import { apiService } from '../services/api';
import { formatFileSize, formatRelativeTime } from '../utils';

const { TabPane } = Tabs;

const Settings: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [configs, setConfigs] = useState<SystemConfig[]>([]);
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [wsStats, setWsStats] = useState<any>(null);
  const [onlineUsers, setOnlineUsers] = useState<any>(null);
  const [testingConnection, setTestingConnection] = useState(false);
  const [configForm] = Form.useForm();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [configsResponse, statsData, wsStatsData, onlineUsersData] = await Promise.all([
        apiService.getConfigs(),
        apiService.getSystemStats(),
        apiService.getWebSocketStats(),
        apiService.getOnlineUsers()
      ]);
      
      setConfigs(configsResponse.items);
      setSystemStats(statsData);
      setWsStats(wsStatsData);
      setOnlineUsers(onlineUsersData);
      
      // 设置表单初始值
      const formValues: any = {};
      configsResponse.items.forEach(config => {
        formValues[config.key] = config.value;
      });
      configForm.setFieldsValue(formValues);
    } catch (error) {
      console.error('Failed to load settings data:', error);
      message.error('加载设置数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveConfigs = async (values: any) => {
    try {
      // 这里应该调用批量更新配置的API
      message.success('配置保存成功');
    } catch (error) {
      console.error('Failed to save configs:', error);
      message.error('保存配置失败');
    }
  };

  const handleSendBroadcast = async () => {
    try {
      await apiService.sendBroadcastMessage({
        title: '系统通知',
        message: '这是一条测试广播消息',
        level: 'info'
      });
      message.success('广播消息发送成功');
    } catch (error) {
      console.error('Failed to send broadcast:', error);
      message.error('发送广播消息失败');
    }
  };

  const handleTestDoodStreamConnection = async () => {
    setTestingConnection(true);
    try {
      const values = configForm.getFieldsValue();
      const testConfig = {
        api_key: values.doodstream_api_key,
        base_url: values.doodstream_base_url || 'https://doodapi.co',
        timeout: values.doodstream_timeout || 300,
        max_retries: values.doodstream_max_retries || 3
      };

      // 调用测试连接API
      await apiService.testDoodStreamConnection(testConfig);
      message.success('DoodStream连接测试成功！');
    } catch (error) {
      console.error('DoodStream connection test failed:', error);
      message.error('DoodStream连接测试失败，请检查配置');
    } finally {
      setTestingConnection(false);
    }
  };

  const handleTestImgBBConnection = async () => {
    setTestingConnection(true);
    try {
      const values = configForm.getFieldsValue();
      const testConfig = {
        base_url: values.imgbb_base_url || 'http://localhost:3000',
        timeout: values.imgbb_timeout || 30
      };

      // 调用测试连接API
      await apiService.testImgBBConnection(testConfig);
      message.success('ImgBB连接测试成功！');
    } catch (error) {
      console.error('ImgBB connection test failed:', error);
      message.error('ImgBB连接测试失败，请检查配置');
    } finally {
      setTestingConnection(false);
    }
  };

  const configColumns = [
    {
      title: '配置项',
      dataIndex: 'key',
      key: 'key',
      width: 200
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '当前值',
      dataIndex: 'value',
      key: 'value',
      width: 150,
      render: (value: string, record: SystemConfig) => {
        if (record.type === 'bool') {
          return <Tag color={value === 'true' ? 'green' : 'red'}>
            {value === 'true' ? '启用' : '禁用'}
          </Tag>;
        }
        return value;
      }
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100
    }
  ];

  const onlineUserColumns = [
    {
      title: '用户ID',
      dataIndex: 'user_id',
      key: 'user_id',
      width: 80
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 80,
      render: (role: string) => (
        <Tag color={role === 'admin' ? 'red' : 'blue'}>
          {role === 'admin' ? '管理员' : '用户'}
        </Tag>
      )
    },
    {
      title: '连接数',
      dataIndex: 'connections',
      key: 'connections',
      width: 80
    },
    {
      title: '连接时间',
      dataIndex: 'connected_at',
      key: 'connected_at',
      width: 120,
      render: (time: string) => formatRelativeTime(time)
    }
  ];

  return (
    <div>
      <h1 className="page-title">系统设置</h1>
      
      <Tabs defaultActiveKey="system" type="card">
        {/* 系统信息 */}
        <TabPane tab={<span><DatabaseOutlined />系统信息</span>} key="system">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="CPU使用率"
                  value={85}
                  suffix="%"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="内存使用"
                  value={formatFileSize(systemStats?.runtime?.memory_alloc || 0)}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="协程数量"
                  value={systemStats?.runtime?.goroutines || 0}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="GC次数"
                  value={systemStats?.runtime?.gc_count || 0}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
          </Row>
          
          <Card title="系统配置" style={{ marginTop: 16 }}>
            <Table
              columns={configColumns}
              dataSource={configs}
              rowKey="id"
              loading={loading}
              pagination={false}
              size="small"
            />
          </Card>
        </TabPane>

        {/* WebSocket状态 */}
        <TabPane tab={<span><WifiOutlined />WebSocket</span>} key="websocket">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="当前连接"
                  value={wsStats?.current_connections || 0}
                  prefix={<WifiOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="总连接数"
                  value={wsStats?.total_connections || 0}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="已发送消息"
                  value={wsStats?.messages_sent || 0}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="在线用户"
                  value={onlineUsers?.total || 0}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
          </Row>

          <Card 
            title="在线用户" 
            style={{ marginTop: 16 }}
            extra={
              <Button type="primary" onClick={handleSendBroadcast}>
                发送测试广播
              </Button>
            }
          >
            <Table
              columns={onlineUserColumns}
              dataSource={onlineUsers?.users || []}
              rowKey="user_id"
              loading={loading}
              pagination={false}
              size="small"
            />
          </Card>
        </TabPane>

        {/* 系统配置 */}
        <TabPane tab={<span><SettingOutlined />配置管理</span>} key="config">
          <Card title="系统配置">
            <Form
              form={configForm}
              layout="vertical"
              onFinish={handleSaveConfigs}
            >
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="max_concurrent_downloads"
                    label="最大并发下载数"
                  >
                    <InputNumber min={1} max={100} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="download_speed_limit"
                    label="下载速度限制 (KB/s)"
                  >
                    <InputNumber min={0} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="default_download_path"
                    label="默认下载路径"
                  >
                    <Input />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="max_retries"
                    label="最大重试次数"
                  >
                    <InputNumber min={0} max={10} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="auto_clean_completed"
                    label="自动清理已完成任务"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="enable_notifications"
                    label="启用通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              {/* 文件处理配置 */}
              <Divider orientation="left">文件处理配置</Divider>
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="upload_provider"
                    label="上传服务提供商"
                    tooltip="选择视频上传的目标服务"
                  >
                    <Select placeholder="请选择上传服务">
                      <Select.Option value="imgbb">ImgBB (分片上传)</Select.Option>
                      <Select.Option value="doodstream">DoodStream (直接上传)</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="auto_start_processing"
                    label="下载完成后自动处理"
                    valuePropName="checked"
                    tooltip="下载完成后自动开始文件处理和上传"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              {/* DoodStream配置 */}
              <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => 
                prevValues.upload_provider !== currentValues.upload_provider
              }>
                {({ getFieldValue }) => {
                  const uploadProvider = getFieldValue('upload_provider');
                  return uploadProvider === 'doodstream' ? (
                    <>
                      <Divider orientation="left">DoodStream配置</Divider>
                      <Row gutter={16}>
                        <Col xs={24} sm={12}>
                          <Form.Item
                            name="doodstream_api_key"
                            label="DoodStream API密钥"
                            tooltip="从DoodStream获取的API密钥"
                            rules={[
                              { required: true, message: '请输入DoodStream API密钥' }
                            ]}
                          >
                            <Input.Password placeholder="请输入API密钥" />
                          </Form.Item>
                        </Col>
                        
                        <Col xs={24} sm={12}>
                          <Form.Item
                            name="doodstream_base_url"
                            label="DoodStream API地址"
                            tooltip="DoodStream API的基础URL"
                          >
                            <Input placeholder="https://doodapi.co" />
                          </Form.Item>
                        </Col>
                        
                        <Col xs={24} sm={12}>
                          <Form.Item
                            name="doodstream_timeout"
                            label="上传超时时间 (秒)"
                            tooltip="大文件上传的超时时间，建议300秒以上"
                          >
                            <InputNumber min={60} max={600} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        
                        <Col xs={24} sm={12}>
                          <Form.Item
                            name="doodstream_max_retries"
                            label="最大重试次数"
                            tooltip="上传失败时的最大重试次数"
                          >
                            <InputNumber min={0} max={10} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        
                        <Col xs={24}>
                          <Form.Item>
                            <Button 
                              type="default" 
                              onClick={handleTestDoodStreamConnection}
                              loading={testingConnection}
                              icon={<WifiOutlined />}
                            >
                              测试DoodStream连接
                            </Button>
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  ) : null;
                }}
              </Form.Item>

              {/* ImgBB配置 */}
              <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => 
                prevValues.upload_provider !== currentValues.upload_provider
              }>
                {({ getFieldValue }) => {
                  const uploadProvider = getFieldValue('upload_provider');
                  return uploadProvider === 'imgbb' ? (
                    <>
                      <Divider orientation="left">ImgBB配置</Divider>
                      <Row gutter={16}>
                        <Col xs={24} sm={12}>
                          <Form.Item
                            name="imgbb_base_url"
                            label="ImgBB服务地址"
                            tooltip="本地Telegraph-Image Express服务地址"
                          >
                            <Input placeholder="http://localhost:3000" />
                          </Form.Item>
                        </Col>
                        
                        <Col xs={24} sm={12}>
                          <Form.Item
                            name="imgbb_timeout"
                            label="上传超时时间 (秒)"
                          >
                            <InputNumber min={10} max={300} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        
                        <Col xs={24}>
                          <Form.Item>
                            <Button 
                              type="default" 
                              onClick={handleTestImgBBConnection}
                              loading={testingConnection}
                              icon={<WifiOutlined />}
                            >
                              测试ImgBB连接
                            </Button>
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  ) : null;
                }}
              </Form.Item>
              
              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  保存配置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Settings;
