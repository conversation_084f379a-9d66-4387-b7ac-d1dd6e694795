-- 添加实际文件信息和工作目录字段
-- 创建时间: 2025-06-24
-- 描述: 为download_tasks表添加actual_files和processing_work_dir字段，解决aria2下载文件路径不匹配问题

-- 添加新字段
ALTER TABLE download_tasks 
ADD COLUMN IF NOT EXISTS actual_files JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS processing_work_dir VARCHAR(500) DEFAULT '';

-- 添加字段注释
COMMENT ON COLUMN download_tasks.actual_files IS '实际下载的文件列表(JSONB格式)';
COMMENT ON COLUMN download_tasks.processing_work_dir IS '文件处理工作目录';

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_download_tasks_actual_files ON download_tasks USING GIN (actual_files);
CREATE INDEX IF NOT EXISTS idx_download_tasks_processing_work_dir ON download_tasks(processing_work_dir);

-- 为现有任务设置默认的actual_files值（基于task_name推测）
-- 注意：这只是一个临时解决方案，实际的文件信息需要通过aria2 API获取
UPDATE download_tasks 
SET actual_files = jsonb_build_array(
    jsonb_build_object(
        'index', 0,
        'path', CASE 
            WHEN save_path IS NOT NULL AND task_name IS NOT NULL 
            THEN save_path || '/' || task_name
            ELSE task_name
        END,
        'name', task_name,
        'size', total_size,
        'completed_length', downloaded_size,
        'selected', true
    )
)
WHERE actual_files = '[]'::jsonb 
  AND task_name IS NOT NULL 
  AND task_name != ''
  AND status IN ('completed', 'processing', 'uploading', 'ready');

-- 更新统计视图以包含新字段
DROP VIEW IF EXISTS task_statistics;
CREATE OR REPLACE VIEW task_statistics AS
SELECT 
    status,
    COUNT(*) as count,
    AVG(progress) as avg_progress,
    AVG(processing_progress) as avg_processing_progress,
    SUM(total_size) as total_size,
    SUM(downloaded_size) as downloaded_size,
    SUM(chunk_count) as total_chunks,
    SUM(uploaded_chunks) as total_uploaded_chunks,
    COUNT(CASE WHEN jsonb_array_length(actual_files) > 0 THEN 1 END) as tasks_with_actual_files
FROM download_tasks 
WHERE deleted_at IS NULL
GROUP BY status;

-- 添加用于查询实际文件信息的函数
CREATE OR REPLACE FUNCTION get_task_file_count(task_id INTEGER)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COALESCE(jsonb_array_length(actual_files), 0)
        FROM download_tasks 
        WHERE id = task_id AND deleted_at IS NULL
    );
END;
$$ LANGUAGE plpgsql;

-- 添加用于获取选中文件数量的函数
CREATE OR REPLACE FUNCTION get_task_selected_file_count(task_id INTEGER)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER
        FROM download_tasks,
             jsonb_array_elements(actual_files) AS file_info
        WHERE id = task_id 
          AND deleted_at IS NULL
          AND (file_info->>'selected')::boolean = true
    );
END;
$$ LANGUAGE plpgsql;