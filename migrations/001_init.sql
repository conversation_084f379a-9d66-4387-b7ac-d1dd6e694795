-- 初始化数据库表结构
-- 创建时间: 2025-06-22

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user' NOT NULL,
    status VARCHAR(20) DEFAULT 'active' NOT NULL,
    avatar VARCHAR(500),
    last_login_at TIMESTAMP,
    login_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at);

-- 下载任务表
CREATE TABLE IF NOT EXISTS download_tasks (
    id SERIAL PRIMARY KEY,
    magnet_uri TEXT NOT NULL,
    task_name VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' NOT NULL,
    priority INTEGER DEFAULT 2,
    progress DECIMAL(5,2) DEFAULT 0,
    download_speed BIGINT DEFAULT 0,
    upload_speed BIGINT DEFAULT 0,
    total_size BIGINT DEFAULT 0,
    downloaded_size BIGINT DEFAULT 0,
    uploaded_size BIGINT DEFAULT 0,
    eta BIGINT,
    aria2_gid VARCHAR(64),
    save_path VARCHAR(500),
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- 下载任务表索引
CREATE INDEX IF NOT EXISTS idx_download_tasks_status ON download_tasks(status);
CREATE INDEX IF NOT EXISTS idx_download_tasks_user_id ON download_tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_download_tasks_priority ON download_tasks(priority);
CREATE INDEX IF NOT EXISTS idx_download_tasks_aria2_gid ON download_tasks(aria2_gid);
CREATE INDEX IF NOT EXISTS idx_download_tasks_created_at ON download_tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_download_tasks_deleted_at ON download_tasks(deleted_at);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    type VARCHAR(20) DEFAULT 'string' NOT NULL,
    description VARCHAR(500),
    category VARCHAR(50),
    is_public BOOLEAN DEFAULT false,
    is_editable BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- 系统配置表索引
CREATE INDEX IF NOT EXISTS idx_system_configs_key ON system_configs(key);
CREATE INDEX IF NOT EXISTS idx_system_configs_category ON system_configs(category);
CREATE INDEX IF NOT EXISTS idx_system_configs_is_public ON system_configs(is_public);
CREATE INDEX IF NOT EXISTS idx_system_configs_deleted_at ON system_configs(deleted_at);

-- 插入默认管理员用户（密码: admin123）
INSERT INTO users (username, email, password_hash, role, status) 
VALUES ('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active')
ON CONFLICT (username) DO NOTHING;

-- 插入默认系统配置
INSERT INTO system_configs (key, value, type, description, category, is_public, is_editable) VALUES
('max_concurrent_downloads', '5', 'int', '最大并发下载任务数', 'download', true, true),
('default_download_path', '/downloads', 'string', '默认下载保存路径', 'download', true, true),
('max_retries', '3', 'int', '下载失败最大重试次数', 'download', true, true),
('download_speed_limit', '0', 'int', '下载速度限制(KB/s，0表示无限制)', 'download', true, true),
('upload_speed_limit', '0', 'int', '上传速度限制(KB/s，0表示无限制)', 'download', true, true),
('auto_clean_completed', 'false', 'bool', '自动清理已完成的下载任务', 'system', true, true),
('clean_after_days', '30', 'int', '已完成任务保留天数', 'system', true, true),
('enable_notifications', 'true', 'bool', '启用系统通知', 'system', true, true),
('system_maintenance', 'false', 'bool', '系统维护模式', 'system', false, true)
ON CONFLICT (key) DO NOTHING;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_download_tasks_updated_at BEFORE UPDATE ON download_tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建统计视图
CREATE OR REPLACE VIEW task_statistics AS
SELECT 
    status,
    COUNT(*) as count,
    AVG(progress) as avg_progress,
    SUM(total_size) as total_size,
    SUM(downloaded_size) as downloaded_size
FROM download_tasks 
WHERE deleted_at IS NULL
GROUP BY status;

CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    role,
    status,
    COUNT(*) as count,
    AVG(login_count) as avg_login_count
FROM users 
WHERE deleted_at IS NULL
GROUP BY role, status;
