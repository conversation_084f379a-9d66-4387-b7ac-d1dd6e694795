-- 添加文件处理相关字段
-- 创建时间: 2025-06-24
-- 描述: 为download_tasks表添加文件分片、加密、上传相关字段

-- 添加新的任务状态
ALTER TABLE download_tasks 
ALTER COLUMN status TYPE VARCHAR(20);

-- 添加文件处理相关字段
ALTER TABLE download_tasks 
ADD COLUMN IF NOT EXISTS processing_status VARCHAR(50) DEFAULT '',
ADD COLUMN IF NOT EXISTS chunk_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS uploaded_chunks INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS processing_progress DECIMAL(5,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS playlist_url TEXT,
ADD COLUMN IF NOT EXISTS encryption_key VARCHAR(255),
ADD COLUMN IF NOT EXISTS processing_started_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS processing_completed_at TIMESTAMP;

-- 添加字段注释
COMMENT ON COLUMN download_tasks.processing_status IS '文件处理状态';
COMMENT ON COLUMN download_tasks.chunk_count IS '分片总数';
COMMENT ON COLUMN download_tasks.uploaded_chunks IS '已上传分片数';
COMMENT ON COLUMN download_tasks.processing_progress IS '处理进度(0-100)';
COMMENT ON COLUMN download_tasks.playlist_url IS '播放列表URL';
COMMENT ON COLUMN download_tasks.encryption_key IS '加密密钥(加密存储)';
COMMENT ON COLUMN download_tasks.processing_started_at IS '处理开始时间';
COMMENT ON COLUMN download_tasks.processing_completed_at IS '处理完成时间';

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_download_tasks_processing_status ON download_tasks(processing_status);
CREATE INDEX IF NOT EXISTS idx_download_tasks_processing_started_at ON download_tasks(processing_started_at);
CREATE INDEX IF NOT EXISTS idx_download_tasks_processing_completed_at ON download_tasks(processing_completed_at);

-- 更新统计视图以包含新状态
DROP VIEW IF EXISTS task_statistics;
CREATE OR REPLACE VIEW task_statistics AS
SELECT 
    status,
    COUNT(*) as count,
    AVG(progress) as avg_progress,
    AVG(processing_progress) as avg_processing_progress,
    SUM(total_size) as total_size,
    SUM(downloaded_size) as downloaded_size,
    SUM(chunk_count) as total_chunks,
    SUM(uploaded_chunks) as total_uploaded_chunks
FROM download_tasks 
WHERE deleted_at IS NULL
GROUP BY status;

-- 添加文件处理相关的系统配置
INSERT INTO system_configs (key, value, type, description, category, is_public, is_editable) VALUES
('file_processing_enabled', 'true', 'bool', '启用文件处理功能', 'processing', true, true),
('chunk_size_mb', '1', 'int', '文件分片大小(MB)', 'processing', true, true),
('max_concurrent_uploads', '5', 'int', '最大并发上传数', 'processing', true, true),
('encryption_algorithm', 'aes-gcm-256', 'string', '加密算法', 'processing', false, true),
('imgbb_api_key', '', 'string', 'ImgBB API密钥', 'processing', false, true),
('processing_retry_attempts', '3', 'int', '处理失败重试次数', 'processing', true, true),
('auto_start_processing', 'true', 'bool', '下载完成后自动开始处理', 'processing', true, true),
('keep_original_files', 'false', 'bool', '保留原始文件', 'processing', true, true)
ON CONFLICT (key) DO NOTHING;
