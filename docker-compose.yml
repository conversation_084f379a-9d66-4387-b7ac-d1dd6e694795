version: '3.8'

services:
  # 主应用服务
  magnet-downloader:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: magnet-downloader
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8080}:8080"
    environment:
      - APP_ENV=${APP_ENV:-production}
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=${DB_NAME:-magnet_downloader}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-postgres123}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
      - ARIA2_HOST=aria2
      - ARIA2_PORT=6800
      - ARIA2_SECRET=${ARIA2_SECRET:-aria2secret}
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-key}
    volumes:
      - ./downloads:/app/downloads
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      aria2:
        condition: service_started
    networks:
      - magnet-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_NAME:-magnet_downloader}
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres123}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./deployments/docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - magnet-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-magnet_downloader}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis123} --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - magnet-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

  # Aria2下载引擎
  aria2:
    image: p3terx/aria2-pro:latest
    container_name: aria2
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - UMASK_SET=022
      - RPC_SECRET=${ARIA2_SECRET:-aria2secret}
      - RPC_PORT=6800
      - LISTEN_PORT=6888
      - DISK_CACHE=64M
      - IPV6_MODE=false
      - UPDATE_TRACKERS=true
      - CUSTOM_TRACKER_URL=
      - TZ=Asia/Shanghai
    volumes:
      - ./downloads:/downloads
      - aria2_config:/config
    ports:
      - "${ARIA2_RPC_PORT:-6800}:6800"
      - "${ARIA2_LISTEN_PORT:-6888}:6888"
      - "${ARIA2_LISTEN_PORT:-6888}:6888/udp"
    networks:
      - magnet-network
    logging:
      driver: json-file
      options:
        max-size: "1m"

  # AriaNg Web管理面板
  ariang:
    image: p3terx/ariang:latest
    container_name: ariang
    restart: unless-stopped
    ports:
      - "${ARIANG_PORT:-6880}:6880"
    networks:
      - magnet-network
    depends_on:
      - aria2

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./deployments/docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./web/build:/usr/share/nginx/html:ro
      - ./logs/nginx:/var/log/nginx
      - nginx_cache:/var/cache/nginx
    depends_on:
      - magnet-downloader
    networks:
      - magnet-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./deployments/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./deployments/monitoring/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - magnet-network
    profiles:
      - monitoring

  # Grafana可视化（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployments/monitoring/grafana-dashboard.json:/var/lib/grafana/dashboards/magnet-downloader.json:ro
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - magnet-network
    profiles:
      - monitoring
    depends_on:
      - prometheus

# 数据卷定义
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  aria2_config:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_cache:
    driver: local

# 网络定义
networks:
  magnet-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
