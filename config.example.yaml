app:
  name: "magnet-downloader"
  version: "1.0.0"
  env: "development"

server:
  host: "0.0.0.0"
  port: 8080
  mode: "debug"  # debug, release
  read_timeout: 60
  write_timeout: 60

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "magnet_downloader"
  sslmode: "disable"
  max_open_conns: 100
  max_idle_conns: 10

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10

aria2:
  host: "localhost"
  port: 6800
  secret: ""

log:
  level: "info"  # debug, info, warn, error, fatal, panic
  format: "json"  # json, text
  output: "stdout"  # stdout, file

jwt:
  secret: "your-secret-key-change-this-in-production"
  expire_time: 3600  # seconds

# 调度器配置
scheduler:
  enabled: true
  timezone: "Asia/Shanghai"

# 文件处理配置
file_processing:
  enabled: true                    # 是否启用文件处理
  chunk_size_mb: 1                 # 分片大小(MB)
  max_concurrent_uploads: 3        # 最大并发上传数
  encryption_algorithm: "aes-gcm-256"  # 加密算法
  encryption_enabled: true         # 是否启用加密
  keep_original_files: false       # 是否保留原始文件
  work_dir: "/tmp/fileprocessor"   # 工作目录
  retry_attempts: 3                # 重试次数
  auto_start_processing: true      # 下载完成后自动开始处理
  cleanup_after_days: 30           # 多少天后清理文件

  # 本地图床配置 (Telegraph-Image Express)
  imgbb:
    api_key: ""                    # API密钥 (本地图床不需要，保持兼容性)
    base_url: "http://localhost:3000"  # 本地图床API基础URL
    timeout: 30                    # 请求超时时间(秒)
    max_retries: 3                 # 最大重试次数

  # Imgur图床配置
  imgur:
    client_id: ""                  # Imgur客户端ID (必须配置)
    client_secret: ""              # Imgur客户端密钥 (必须配置)
    redirect_uri: "http://************:8080/api/imgur/callback"  # OAuth2回调URL
    base_url: "https://api.imgur.com/3"  # API基础URL
    timeout: 30                    # 请求超时时间(秒)
    max_retries: 3                 # 最大重试次数

  # 播放列表配置
  playlist:
    version: 3                     # HLS版本
    target_duration: 10            # 目标时长(秒)
    media_sequence: 0              # 媒体序列号
    allow_cache: true              # 是否允许缓存
    playlist_type: "VOD"           # 播放列表类型
