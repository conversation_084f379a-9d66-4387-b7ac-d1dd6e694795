# 开发环境Dockerfile
FROM golang:1.21-alpine

# 安装必要的工具
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    curl \
    make \
    gcc \
    musl-dev

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装Air热重载工具
RUN go install github.com/cosmtrek/air@latest

# 安装Delve调试器
RUN go install github.com/go-delve/delve/cmd/dlv@latest

# 设置工作目录
WORKDIR /app

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 8080 2345

# 启动命令（使用Air热重载）
CMD ["air", "-c", ".air.toml"]
