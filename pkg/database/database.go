package database

import (
	"fmt"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/logger"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

var DB *gorm.DB

// Init 初始化数据库连接
func Init(cfg *config.DatabaseConfig) error {
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.DBName, cfg.SSLMode)

	// 配置GORM日志
	gormConfig := &gorm.Config{
		Logger: gormLogger.New(
			logger.GetLogger(),
			gormLogger.Config{
				SlowThreshold:             time.Second,
				LogLevel:                  gormLogger.Info,
				IgnoreRecordNotFoundError: true,
				Colorful:                  false,
			},
		),
		NamingStrategy: nil, // 使用默认命名策略
	}

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB对象
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// 配置连接池
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	DB = db
	logger.Info("Database connected successfully")
	return nil
}

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// 迁移所有模型
	err := DB.AutoMigrate(
		&model.User{},
		&model.DownloadTask{},
		&model.SystemConfig{},
	)
	if err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	logger.Info("Database migration completed successfully")
	return nil
}

// SeedDefaultData 初始化默认数据
func SeedDefaultData() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// 创建默认管理员用户
	if err := createDefaultAdmin(); err != nil {
		return fmt.Errorf("failed to create default admin: %w", err)
	}

	// 初始化系统配置
	if err := initSystemConfigs(); err != nil {
		return fmt.Errorf("failed to init system configs: %w", err)
	}

	logger.Info("Default data seeded successfully")
	return nil
}

// createDefaultAdmin 创建默认管理员用户
func createDefaultAdmin() error {
	var count int64
	if err := DB.Model(&model.User{}).Count(&count).Error; err != nil {
		return err
	}

	// 如果已有用户，跳过创建
	if count > 0 {
		return nil
	}

	admin := &model.User{
		Username: "admin",
		Email:    "<EMAIL>",
		Role:     model.UserRoleAdmin,
		Status:   model.UserStatusActive,
	}

	if err := admin.SetPassword("admin123"); err != nil {
		return err
	}

	if err := DB.Create(admin).Error; err != nil {
		return err
	}

	logger.Info("Default admin user created: username=admin, password=admin123")
	return nil
}

// initSystemConfigs 初始化系统配置
func initSystemConfigs() error {
	for _, config := range model.DefaultConfigs {
		var existing model.SystemConfig
		err := DB.Where("key = ?", config.Key).First(&existing).Error

		if err == gorm.ErrRecordNotFound {
			// 配置不存在，创建新配置
			if err := DB.Create(&config).Error; err != nil {
				return fmt.Errorf("failed to create config %s: %w", config.Key, err)
			}
			logger.Infof("Created default config: %s", config.Key)
		} else if err != nil {
			return fmt.Errorf("failed to check config %s: %w", config.Key, err)
		}
		// 配置已存在，跳过
	}

	return nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// Close 关闭数据库连接
func Close() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// Transaction 执行事务
func Transaction(fn func(*gorm.DB) error) error {
	return DB.Transaction(fn)
}

// IsHealthy 检查数据库健康状态
func IsHealthy() bool {
	if DB == nil {
		return false
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return false
	}

	return sqlDB.Ping() == nil
}

// GetStats 获取数据库统计信息
func GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if DB == nil {
		stats["status"] = "disconnected"
		return stats
	}

	sqlDB, err := DB.DB()
	if err != nil {
		stats["status"] = "error"
		stats["error"] = err.Error()
		return stats
	}

	dbStats := sqlDB.Stats()
	stats["status"] = "connected"
	stats["max_open_connections"] = dbStats.MaxOpenConnections
	stats["open_connections"] = dbStats.OpenConnections
	stats["in_use"] = dbStats.InUse
	stats["idle"] = dbStats.Idle
	stats["wait_count"] = dbStats.WaitCount
	stats["wait_duration"] = dbStats.WaitDuration.String()
	stats["max_idle_closed"] = dbStats.MaxIdleClosed
	stats["max_idle_time_closed"] = dbStats.MaxIdleTimeClosed
	stats["max_lifetime_closed"] = dbStats.MaxLifetimeClosed

	return stats
}
