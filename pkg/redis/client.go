package redis

import (
	"context"
	"fmt"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/logger"

	"github.com/go-redis/redis/v8"
)

var (
	Client *redis.Client
	ctx    = context.Background()
)

// GetContext 获取Redis上下文
func GetContext() context.Context {
	return ctx
}

// Init 初始化Redis客户端
func Init(cfg *config.RedisConfig) error {
	Client = redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.PoolSize / 4,
		MaxRetries:   3,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolTimeout:  4 * time.Second,
		IdleTimeout:  5 * time.Minute,
	})

	// 测试连接
	if err := Client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	logger.Info("Redis connected successfully")
	return nil
}

// GetClient 获取Redis客户端
func GetClient() *redis.Client {
	return Client
}

// Close 关闭Redis连接
func Close() error {
	if Client == nil {
		return nil
	}
	return Client.Close()
}

// IsHealthy 检查Redis健康状态
func IsHealthy() bool {
	if Client == nil {
		return false
	}
	return Client.Ping(ctx).Err() == nil
}

// GetStats 获取Redis统计信息
func GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if Client == nil {
		stats["status"] = "disconnected"
		return stats
	}

	// 检查连接状态
	if err := Client.Ping(ctx).Err(); err != nil {
		stats["status"] = "error"
		stats["error"] = err.Error()
		return stats
	}

	// 获取连接池统计
	poolStats := Client.PoolStats()
	stats["status"] = "connected"
	stats["hits"] = poolStats.Hits
	stats["misses"] = poolStats.Misses
	stats["timeouts"] = poolStats.Timeouts
	stats["total_conns"] = poolStats.TotalConns
	stats["idle_conns"] = poolStats.IdleConns
	stats["stale_conns"] = poolStats.StaleConns

	// 获取Redis信息
	info, err := Client.Info(ctx).Result()
	if err == nil {
		stats["redis_info"] = info
	}

	return stats
}

// FlushDB 清空当前数据库（仅用于测试）
func FlushDB() error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.FlushDB(ctx).Err()
}

// Set 设置键值对
func Set(key string, value interface{}, expiration time.Duration) error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.Set(ctx, key, value, expiration).Err()
}

// Get 获取值
func Get(key string) (string, error) {
	if Client == nil {
		return "", fmt.Errorf("Redis client not initialized")
	}
	return Client.Get(ctx, key).Result()
}

// Del 删除键
func Del(keys ...string) error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.Del(ctx, keys...).Err()
}

// Exists 检查键是否存在
func Exists(keys ...string) (int64, error) {
	if Client == nil {
		return 0, fmt.Errorf("Redis client not initialized")
	}
	return Client.Exists(ctx, keys...).Result()
}

// Expire 设置过期时间
func Expire(key string, expiration time.Duration) error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.Expire(ctx, key, expiration).Err()
}

// TTL 获取剩余生存时间
func TTL(key string) (time.Duration, error) {
	if Client == nil {
		return 0, fmt.Errorf("Redis client not initialized")
	}
	return Client.TTL(ctx, key).Result()
}

// HSet 设置哈希字段
func HSet(key string, values ...interface{}) error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.HSet(ctx, key, values...).Err()
}

// HGet 获取哈希字段值
func HGet(key, field string) (string, error) {
	if Client == nil {
		return "", fmt.Errorf("Redis client not initialized")
	}
	return Client.HGet(ctx, key, field).Result()
}

// HGetAll 获取所有哈希字段
func HGetAll(key string) (map[string]string, error) {
	if Client == nil {
		return nil, fmt.Errorf("Redis client not initialized")
	}
	return Client.HGetAll(ctx, key).Result()
}

// HDel 删除哈希字段
func HDel(key string, fields ...string) error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.HDel(ctx, key, fields...).Err()
}

// LPush 从左侧推入列表
func LPush(key string, values ...interface{}) error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.LPush(ctx, key, values...).Err()
}

// RPush 从右侧推入列表
func RPush(key string, values ...interface{}) error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.RPush(ctx, key, values...).Err()
}

// LPop 从左侧弹出列表元素
func LPop(key string) (string, error) {
	if Client == nil {
		return "", fmt.Errorf("Redis client not initialized")
	}
	return Client.LPop(ctx, key).Result()
}

// RPop 从右侧弹出列表元素
func RPop(key string) (string, error) {
	if Client == nil {
		return "", fmt.Errorf("Redis client not initialized")
	}
	return Client.RPop(ctx, key).Result()
}

// LLen 获取列表长度
func LLen(key string) (int64, error) {
	if Client == nil {
		return 0, fmt.Errorf("Redis client not initialized")
	}
	return Client.LLen(ctx, key).Result()
}

// ZAdd 添加有序集合成员
func ZAdd(key string, members ...*redis.Z) error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.ZAdd(ctx, key, members...).Err()
}

// ZRangeByScore 按分数范围获取有序集合成员
func ZRangeByScore(key string, opt *redis.ZRangeBy) ([]string, error) {
	if Client == nil {
		return nil, fmt.Errorf("Redis client not initialized")
	}
	return Client.ZRangeByScore(ctx, key, opt).Result()
}

// ZRem 删除有序集合成员
func ZRem(key string, members ...interface{}) error {
	if Client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return Client.ZRem(ctx, key, members...).Err()
}

// ZCard 获取有序集合成员数量
func ZCard(key string) (int64, error) {
	if Client == nil {
		return 0, fmt.Errorf("Redis client not initialized")
	}
	return Client.ZCard(ctx, key).Result()
}

// Incr 递增
func Incr(key string) (int64, error) {
	if Client == nil {
		return 0, fmt.Errorf("Redis client not initialized")
	}
	return Client.Incr(ctx, key).Result()
}

// Decr 递减
func Decr(key string) (int64, error) {
	if Client == nil {
		return 0, fmt.Errorf("Redis client not initialized")
	}
	return Client.Decr(ctx, key).Result()
}
