package metrics

import (
	"runtime"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Metrics 指标收集器
type Metrics struct {
	// HTTP请求指标
	HTTPRequestsTotal    *prometheus.CounterVec
	HTTPRequestDuration  *prometheus.HistogramVec
	HTTPRequestsInFlight prometheus.Gauge

	// 任务相关指标
	TasksTotal       *prometheus.CounterVec
	TasksActive      prometheus.Gauge
	TasksQueueLength prometheus.Gauge
	TaskDuration     *prometheus.HistogramVec
	TaskSuccessRate  prometheus.Gauge
	DownloadSpeed    prometheus.Gauge
	UploadSpeed      prometheus.Gauge
	TotalDownloaded  prometheus.Counter
	TotalUploaded    prometheus.Counter

	// 用户相关指标
	UsersTotal     prometheus.Gauge
	UsersActive    prometheus.Gauge
	UserSessions   prometheus.Gauge
	UserLoginTotal *prometheus.CounterVec

	// WebSocket指标
	WebSocketConnections prometheus.Gauge
	WebSocketMessages    *prometheus.CounterVec

	// 系统资源指标
	CPUUsage        prometheus.Gauge
	MemoryUsage     prometheus.Gauge
	MemoryTotal     prometheus.Gauge
	GoroutinesCount prometheus.Gauge
	GCDuration      prometheus.Gauge

	// 数据库指标
	DatabaseConnections   *prometheus.GaugeVec
	DatabaseQueries       *prometheus.CounterVec
	DatabaseQueryDuration *prometheus.HistogramVec

	// Redis指标
	RedisConnections     prometheus.Gauge
	RedisCommands        *prometheus.CounterVec
	RedisCommandDuration *prometheus.HistogramVec

	// 业务指标
	DownloadSuccessRate prometheus.Gauge
	SystemUptime        prometheus.Gauge
	ErrorRate           prometheus.Gauge
}

// NewMetrics 创建指标收集器
func NewMetrics() *Metrics {
	return &Metrics{
		// HTTP请求指标
		HTTPRequestsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "http_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"method", "endpoint", "status"},
		),
		HTTPRequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "endpoint"},
		),
		HTTPRequestsInFlight: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "http_requests_in_flight",
				Help: "Number of HTTP requests currently being processed",
			},
		),

		// 任务相关指标
		TasksTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "tasks_total",
				Help: "Total number of tasks",
			},
			[]string{"status", "priority"},
		),
		TasksActive: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "tasks_active",
				Help: "Number of active tasks",
			},
		),
		TasksQueueLength: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "tasks_queue_length",
				Help: "Number of tasks in queue",
			},
		),
		TaskDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "task_duration_seconds",
				Help:    "Task execution duration in seconds",
				Buckets: []float64{1, 5, 10, 30, 60, 300, 600, 1800, 3600},
			},
			[]string{"status"},
		),
		TaskSuccessRate: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "task_success_rate",
				Help: "Task success rate percentage",
			},
		),
		DownloadSpeed: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "download_speed_bytes_per_second",
				Help: "Current download speed in bytes per second",
			},
		),
		UploadSpeed: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "upload_speed_bytes_per_second",
				Help: "Current upload speed in bytes per second",
			},
		),
		TotalDownloaded: promauto.NewCounter(
			prometheus.CounterOpts{
				Name: "total_downloaded_bytes",
				Help: "Total bytes downloaded",
			},
		),
		TotalUploaded: promauto.NewCounter(
			prometheus.CounterOpts{
				Name: "total_uploaded_bytes",
				Help: "Total bytes uploaded",
			},
		),

		// 用户相关指标
		UsersTotal: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "users_total",
				Help: "Total number of users",
			},
		),
		UsersActive: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "users_active",
				Help: "Number of active users",
			},
		),
		UserSessions: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "user_sessions",
				Help: "Number of active user sessions",
			},
		),
		UserLoginTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "user_login_total",
				Help: "Total number of user logins",
			},
			[]string{"status"},
		),

		// WebSocket指标
		WebSocketConnections: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "websocket_connections",
				Help: "Number of active WebSocket connections",
			},
		),
		WebSocketMessages: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "websocket_messages_total",
				Help: "Total number of WebSocket messages",
			},
			[]string{"type", "direction"},
		),

		// 系统资源指标
		CPUUsage: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "cpu_usage_percent",
				Help: "CPU usage percentage",
			},
		),
		MemoryUsage: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "memory_usage_bytes",
				Help: "Memory usage in bytes",
			},
		),
		MemoryTotal: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "memory_total_bytes",
				Help: "Total memory in bytes",
			},
		),
		GoroutinesCount: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "goroutines_count",
				Help: "Number of goroutines",
			},
		),
		GCDuration: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "gc_duration_seconds",
				Help: "Time spent in garbage collection",
			},
		),

		// 数据库指标
		DatabaseConnections: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "database_connections",
				Help: "Number of database connections",
			},
			[]string{"state"},
		),
		DatabaseQueries: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "database_queries_total",
				Help: "Total number of database queries",
			},
			[]string{"operation", "status"},
		),
		DatabaseQueryDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "database_query_duration_seconds",
				Help:    "Database query duration in seconds",
				Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5},
			},
			[]string{"operation"},
		),

		// Redis指标
		RedisConnections: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "redis_connections",
				Help: "Number of Redis connections",
			},
		),
		RedisCommands: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "redis_commands_total",
				Help: "Total number of Redis commands",
			},
			[]string{"command", "status"},
		),
		RedisCommandDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "redis_command_duration_seconds",
				Help:    "Redis command duration in seconds",
				Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1},
			},
			[]string{"command"},
		),

		// 业务指标
		DownloadSuccessRate: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "download_success_rate",
				Help: "Download success rate percentage",
			},
		),
		SystemUptime: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "system_uptime_seconds",
				Help: "System uptime in seconds",
			},
		),
		ErrorRate: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "error_rate",
				Help: "Error rate percentage",
			},
		),
	}
}

// UpdateSystemMetrics 更新系统指标
func (m *Metrics) UpdateSystemMetrics() {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	m.MemoryUsage.Set(float64(memStats.Alloc))
	m.MemoryTotal.Set(float64(memStats.Sys))
	m.GoroutinesCount.Set(float64(runtime.NumGoroutine()))
	m.GCDuration.Set(float64(memStats.PauseTotalNs) / 1e9)
}

// RecordHTTPRequest 记录HTTP请求
func (m *Metrics) RecordHTTPRequest(method, endpoint, status string, duration time.Duration) {
	m.HTTPRequestsTotal.WithLabelValues(method, endpoint, status).Inc()
	m.HTTPRequestDuration.WithLabelValues(method, endpoint).Observe(duration.Seconds())
}

// RecordTaskEvent 记录任务事件
func (m *Metrics) RecordTaskEvent(status, priority string) {
	m.TasksTotal.WithLabelValues(status, priority).Inc()
}

// RecordTaskDuration 记录任务执行时间
func (m *Metrics) RecordTaskDuration(status string, duration time.Duration) {
	m.TaskDuration.WithLabelValues(status).Observe(duration.Seconds())
}

// RecordUserLogin 记录用户登录
func (m *Metrics) RecordUserLogin(status string) {
	m.UserLoginTotal.WithLabelValues(status).Inc()
}

// RecordWebSocketMessage 记录WebSocket消息
func (m *Metrics) RecordWebSocketMessage(msgType, direction string) {
	m.WebSocketMessages.WithLabelValues(msgType, direction).Inc()
}

// RecordDatabaseQuery 记录数据库查询
func (m *Metrics) RecordDatabaseQuery(operation, status string, duration time.Duration) {
	m.DatabaseQueries.WithLabelValues(operation, status).Inc()
	m.DatabaseQueryDuration.WithLabelValues(operation).Observe(duration.Seconds())
}

// RecordRedisCommand 记录Redis命令
func (m *Metrics) RecordRedisCommand(command, status string, duration time.Duration) {
	m.RedisCommands.WithLabelValues(command, status).Inc()
	m.RedisCommandDuration.WithLabelValues(command).Observe(duration.Seconds())
}

// Collector 指标收集器
type Collector struct {
	metrics   *Metrics
	startTime time.Time
}

// NewCollector 创建指标收集器
func NewCollector() *Collector {
	return &Collector{
		metrics:   NewMetrics(),
		startTime: time.Now(),
	}
}

// GetMetrics 获取指标实例
func (c *Collector) GetMetrics() *Metrics {
	return c.metrics
}

// StartPeriodicCollection 启动定期指标收集
func (c *Collector) StartPeriodicCollection() {
	ticker := time.NewTicker(15 * time.Second)
	go func() {
		for range ticker.C {
			c.collectSystemMetrics()
		}
	}()
}

// collectSystemMetrics 收集系统指标
func (c *Collector) collectSystemMetrics() {
	// 更新系统指标
	c.metrics.UpdateSystemMetrics()

	// 更新系统运行时间
	uptime := time.Since(c.startTime).Seconds()
	c.metrics.SystemUptime.Set(uptime)
}
