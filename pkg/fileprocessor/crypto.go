package fileprocessor

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"sync"

	"magnet-downloader/pkg/logger"
)

// CryptoConfig 加密配置
type CryptoConfig struct {
	Algorithm string `json:"algorithm"` // 加密算法
	KeySize   int    `json:"key_size"`  // 密钥长度
}

// Crypto 加密器
type Crypto struct {
	config *CryptoConfig
}

// NewCrypto 创建新的加密器
func NewCrypto(config *CryptoConfig) *Crypto {
	if config == nil {
		config = &CryptoConfig{
			Algorithm: "aes-gcm-256",
			KeySize:   32, // 256 bits
		}
	}
	return &Crypto{config: config}
}

// GenerateKey 生成随机密钥
func (c *Crypto) GenerateKey() ([]byte, error) {
	key := make([]byte, c.config.KeySize)
	if _, err := rand.Read(key); err != nil {
		return nil, fmt.Errorf("failed to generate random key: %w", err)
	}
	return key, nil
}

// GenerateKeyFromPassword 从密码生成密钥
func (c *Crypto) GenerateKeyFromPassword(password string, salt []byte) []byte {
	if salt == nil {
		salt = make([]byte, 16)
		rand.Read(salt)
	}

	// 使用SHA256生成密钥
	hash := sha256.New()
	hash.Write([]byte(password))
	hash.Write(salt)
	return hash.Sum(nil)
}

// EncryptFile 加密文件
func (c *Crypto) EncryptFile(inputPath, outputPath string, key []byte) error {
	// 打开输入文件
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %w", err)
	}
	defer inputFile.Close()

	// 创建输出文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer outputFile.Close()

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return fmt.Errorf("failed to create GCM: %w", err)
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return fmt.Errorf("failed to generate nonce: %w", err)
	}

	// 写入nonce到文件开头
	if _, err := outputFile.Write(nonce); err != nil {
		return fmt.Errorf("failed to write nonce: %w", err)
	}

	// 读取并加密文件内容
	plaintext, err := io.ReadAll(inputFile)
	if err != nil {
		return fmt.Errorf("failed to read input file: %w", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

	// 写入加密数据
	if _, err := outputFile.Write(ciphertext); err != nil {
		return fmt.Errorf("failed to write encrypted data: %w", err)
	}

	logger.Debugf("File encrypted: input=%s, output=%s, size=%d", inputPath, outputPath, len(ciphertext))
	return nil
}

// DecryptFile 解密文件
func (c *Crypto) DecryptFile(inputPath, outputPath string, key []byte) error {
	// 打开输入文件
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %w", err)
	}
	defer inputFile.Close()

	// 创建输出文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer outputFile.Close()

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return fmt.Errorf("failed to create GCM: %w", err)
	}

	// 读取nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := inputFile.Read(nonce); err != nil {
		return fmt.Errorf("failed to read nonce: %w", err)
	}

	// 读取加密数据
	ciphertext, err := io.ReadAll(inputFile)
	if err != nil {
		return fmt.Errorf("failed to read encrypted data: %w", err)
	}

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt data: %w", err)
	}

	// 写入解密数据
	if _, err := outputFile.Write(plaintext); err != nil {
		return fmt.Errorf("failed to write decrypted data: %w", err)
	}

	logger.Debugf("File decrypted: input=%s, output=%s, size=%d", inputPath, outputPath, len(plaintext))
	return nil
}

// EncryptData 加密数据
func (c *Crypto) EncryptData(data []byte, key []byte) ([]byte, error) {
	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// DecryptData 解密数据
func (c *Crypto) DecryptData(encryptedData []byte, key []byte) ([]byte, error) {
	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// 提取nonce
	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	nonce := encryptedData[:nonceSize]
	ciphertext := encryptedData[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt data: %w", err)
	}

	return plaintext, nil
}

// KeyToHex 将密钥转换为十六进制字符串
func (c *Crypto) KeyToHex(key []byte) string {
	return hex.EncodeToString(key)
}

// KeyFromHex 从十六进制字符串恢复密钥
func (c *Crypto) KeyFromHex(hexKey string) ([]byte, error) {
	key, err := hex.DecodeString(hexKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode hex key: %w", err)
	}
	if len(key) != c.config.KeySize {
		return nil, fmt.Errorf("invalid key size: expected %d, got %d", c.config.KeySize, len(key))
	}
	return key, nil
}

// CalculateFileHash 计算文件的SHA256哈希
func (c *Crypto) CalculateFileHash(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", fmt.Errorf("failed to calculate hash: %w", err)
	}

	hashBytes := hash.Sum(nil)
	hashString := hex.EncodeToString(hashBytes)

	logger.Debugf("Calculated file hash: %s -> %s", filePath, hashString)
	return hashString, nil
}

// CalculateDataHash 计算数据的SHA256哈希
func (c *Crypto) CalculateDataHash(data []byte) string {
	hash := sha256.Sum256(data)
	hashString := hex.EncodeToString(hash[:])

	logger.Debugf("Calculated data hash: %d bytes -> %s", len(data), hashString)
	return hashString
}

// VerifyFileHash 验证文件哈希
func (c *Crypto) VerifyFileHash(filePath, expectedHash string) bool {
	actualHash, err := c.CalculateFileHash(filePath)
	if err != nil {
		logger.Errorf("Failed to calculate file hash for verification: %v", err)
		return false
	}

	match := actualHash == expectedHash
	logger.Debugf("Hash verification: %s, expected=%s, actual=%s, match=%t",
		filePath, expectedHash, actualHash, match)
	return match
}

// VerifyDataHash 验证数据哈希
func (c *Crypto) VerifyDataHash(data []byte, expectedHash string) bool {
	actualHash := c.CalculateDataHash(data)
	match := actualHash == expectedHash

	logger.Debugf("Data hash verification: %d bytes, expected=%s, actual=%s, match=%t",
		len(data), expectedHash, actualHash, match)
	return match
}

// HashResult 哈希计算结果
type HashResult struct {
	FilePath string `json:"file_path"`
	Hash     string `json:"hash"`
	Error    string `json:"error,omitempty"`
	Size     int64  `json:"size"`
}

// ProgressCallback 进度回调函数类型
type HashProgressCallback func(completed, total int, currentFile string)

// CalculateChunkHashes 批量计算分片哈希
func (c *Crypto) CalculateChunkHashes(chunkPaths []string) ([]string, error) {
	results := make([]string, len(chunkPaths))

	for i, chunkPath := range chunkPaths {
		hash, err := c.CalculateFileHash(chunkPath)
		if err != nil {
			return nil, fmt.Errorf("failed to calculate hash for chunk %d (%s): %w", i, chunkPath, err)
		}
		results[i] = hash
	}

	logger.Infof("Calculated hashes for %d chunks", len(chunkPaths))
	return results, nil
}

// CalculateChunkHashesConcurrent 并发计算分片哈希
func (c *Crypto) CalculateChunkHashesConcurrent(chunkPaths []string, maxConcurrent int, progressCallback HashProgressCallback) ([]HashResult, error) {
	if maxConcurrent <= 0 {
		maxConcurrent = 3 // 默认并发数
	}

	results := make([]HashResult, len(chunkPaths))

	// 创建工作通道
	jobChan := make(chan int, len(chunkPaths))
	resultChan := make(chan struct {
		index  int
		result HashResult
	}, len(chunkPaths))

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < maxConcurrent; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for index := range jobChan {
				filePath := chunkPaths[index]

				// 获取文件信息
				fileInfo, err := os.Stat(filePath)
				var size int64
				if err == nil {
					size = fileInfo.Size()
				}

				// 计算哈希
				hash, hashErr := c.CalculateFileHash(filePath)

				result := HashResult{
					FilePath: filePath,
					Size:     size,
				}

				if hashErr != nil {
					result.Error = hashErr.Error()
				} else {
					result.Hash = hash
				}

				resultChan <- struct {
					index  int
					result HashResult
				}{index, result}
			}
		}()
	}

	// 发送任务
	go func() {
		for i := range chunkPaths {
			jobChan <- i
		}
		close(jobChan)
	}()

	// 等待工作协程完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	completed := 0
	for res := range resultChan {
		results[res.index] = res.result
		completed++

		if progressCallback != nil {
			progressCallback(completed, len(chunkPaths), res.result.FilePath)
		}
	}

	// 检查是否有错误
	errorCount := 0
	for _, result := range results {
		if result.Error != "" {
			errorCount++
		}
	}

	if errorCount > 0 {
		logger.Warnf("Hash calculation completed with %d errors out of %d files", errorCount, len(chunkPaths))
	} else {
		logger.Infof("Successfully calculated hashes for all %d files", len(chunkPaths))
	}

	return results, nil
}

// CalculateFileHashWithEncryption 计算文件哈希并同时加密
func (c *Crypto) CalculateFileHashWithEncryption(inputPath, outputPath string, key []byte) (string, error) {
	// 打开输入文件
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return "", fmt.Errorf("failed to open input file: %w", err)
	}
	defer inputFile.Close()

	// 创建输出文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("failed to create output file: %w", err)
	}
	defer outputFile.Close()

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	// 写入nonce到文件开头
	if _, err := outputFile.Write(nonce); err != nil {
		return "", fmt.Errorf("failed to write nonce: %w", err)
	}

	// 同时计算哈希和读取文件内容
	hash := sha256.New()
	plaintext, err := io.ReadAll(io.TeeReader(inputFile, hash))
	if err != nil {
		return "", fmt.Errorf("failed to read input file: %w", err)
	}

	// 计算哈希
	hashBytes := hash.Sum(nil)
	hashString := hex.EncodeToString(hashBytes)

	// 加密数据
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

	// 写入加密数据
	if _, err := outputFile.Write(ciphertext); err != nil {
		return "", fmt.Errorf("failed to write encrypted data: %w", err)
	}

	logger.Debugf("File encrypted with hash: input=%s, output=%s, hash=%s, size=%d",
		inputPath, outputPath, hashString, len(ciphertext))
	return hashString, nil
}

// ValidateChunkIntegrity 验证分片完整性
func (c *Crypto) ValidateChunkIntegrity(chunkPaths []string, expectedHashes []string) ([]bool, error) {
	if len(chunkPaths) != len(expectedHashes) {
		return nil, fmt.Errorf("chunk paths and hashes count mismatch: %d vs %d",
			len(chunkPaths), len(expectedHashes))
	}

	results := make([]bool, len(chunkPaths))

	for i, chunkPath := range chunkPaths {
		results[i] = c.VerifyFileHash(chunkPath, expectedHashes[i])
	}

	// 统计验证结果
	validCount := 0
	for _, valid := range results {
		if valid {
			validCount++
		}
	}

	logger.Infof("Chunk integrity validation: %d/%d chunks valid", validCount, len(chunkPaths))
	return results, nil
}

// HashSummary 哈希摘要信息
type HashSummary struct {
	TotalFiles     int    `json:"total_files"`
	ValidFiles     int    `json:"valid_files"`
	InvalidFiles   int    `json:"invalid_files"`
	TotalSize      int64  `json:"total_size"`
	ProcessingTime string `json:"processing_time"`
}

// GenerateHashSummary 生成哈希摘要
func (c *Crypto) GenerateHashSummary(results []HashResult) *HashSummary {
	summary := &HashSummary{
		TotalFiles: len(results),
	}

	for _, result := range results {
		summary.TotalSize += result.Size
		if result.Error == "" {
			summary.ValidFiles++
		} else {
			summary.InvalidFiles++
		}
	}

	logger.Debugf("Generated hash summary: %d total, %d valid, %d invalid, %d bytes",
		summary.TotalFiles, summary.ValidFiles, summary.InvalidFiles, summary.TotalSize)

	return summary
}
