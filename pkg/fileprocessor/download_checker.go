package fileprocessor

import (
	"os"
	"path/filepath"
	"time"
	"magnet-downloader/pkg/logger"
)

// DownloadChecker 检查下载是否完成
type DownloadChecker struct {
	stableTime time.Duration // 文件稳定时间阈值
}

// NewDownloadChecker 创建下载检查器
func NewDownloadChecker() *DownloadChecker {
	return &DownloadChecker{
		stableTime: 2 * time.Minute, // 文件2分钟内没有修改才认为下载完成
	}
}

// IsDownloadComplete 检查目录中的下载是否完成
func (dc *DownloadChecker) IsDownloadComplete(taskDir string) bool {
	// 检查是否有aria2临时文件
	if dc.hasAria2TempFiles(taskDir) {
		logger.Debugf("目录 %s 包含aria2临时文件，下载未完成", taskDir)
		return false
	}

	// 检查视频文件是否稳定（没有被写入）
	if !dc.areVideoFilesStable(taskDir) {
		logger.Debugf("目录 %s 中的视频文件仍在被修改，下载未完成", taskDir)
		return false
	}

	logger.Debugf("目录 %s 下载已完成", taskDir)
	return true
}

// hasAria2TempFiles 检查是否有aria2临时文件
func (dc *DownloadChecker) hasAria2TempFiles(taskDir string) bool {
	err := filepath.Walk(taskDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // 忽略错误，继续检查
		}

		// 检查aria2临时文件
		if filepath.Ext(path) == ".aria2" {
			return filepath.SkipDir // 找到临时文件，停止检查
		}

		return nil
	})

	// 如果Walk被SkipDir中断，说明找到了临时文件
	return err == filepath.SkipDir
}

// areVideoFilesStable 检查视频文件是否稳定
func (dc *DownloadChecker) areVideoFilesStable(taskDir string) bool {
	now := time.Now()
	videoExtensions := map[string]bool{
		".mp4": true, ".avi": true, ".mkv": true, ".mov": true,
		".wmv": true, ".flv": true, ".webm": true, ".m4v": true,
	}

	stable := true
	err := filepath.Walk(taskDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // 忽略错误，继续检查
		}

		// 只检查视频文件
		ext := filepath.Ext(path)
		if !videoExtensions[ext] {
			return nil
		}

		// 检查文件修改时间
		if now.Sub(info.ModTime()) < dc.stableTime {
			logger.Debugf("视频文件 %s 最近被修改 (%v)，可能仍在下载", 
				path, now.Sub(info.ModTime()))
			stable = false
			return filepath.SkipDir // 找到不稳定的文件，停止检查
		}

		// 检查文件大小是否合理（大于1MB）
		if info.Size() < 1024*1024 {
			logger.Debugf("视频文件 %s 大小过小 (%d bytes)，可能下载未完成", path, info.Size())
			stable = false
			return filepath.SkipDir
		}

		return nil
	})

	return stable && err != filepath.SkipDir
}

// IsFileBeingDownloaded 检查单个文件是否正在被下载
func (dc *DownloadChecker) IsFileBeingDownloaded(filePath string) bool {
	// 检查是否有对应的aria2临时文件
	aria2File := filePath + ".aria2"
	if _, err := os.Stat(aria2File); err == nil {
		return true // 存在.aria2文件，正在下载
	}

	// 检查文件是否最近被修改
	info, err := os.Stat(filePath)
	if err != nil {
		return false // 文件不存在或无法访问
	}

	return time.Since(info.ModTime()) < dc.stableTime
}