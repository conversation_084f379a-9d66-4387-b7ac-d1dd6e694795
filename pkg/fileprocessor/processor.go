package fileprocessor

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"magnet-downloader/pkg/logger"
)

// ProcessingStatus 处理状态
type ProcessingStatus string

const (
	StatusIdle       ProcessingStatus = "idle"       // 空闲
	StatusChunking   ProcessingStatus = "chunking"   // 分片中
	StatusEncrypting ProcessingStatus = "encrypting" // 加密中
	StatusHashing    ProcessingStatus = "hashing"    // 计算哈希中
	StatusCompleted  ProcessingStatus = "completed"  // 完成
	StatusFailed     ProcessingStatus = "failed"     // 失败
)

// ProcessingConfig 处理配置
type ProcessingConfig struct {
	ChunkSizeMB       int    `json:"chunk_size_mb"`      // 分片大小(MB)
	EncryptionEnabled bool   `json:"encryption_enabled"` // 是否启用加密
	KeepOriginal      bool   `json:"keep_original"`      // 是否保留原文件
	WorkDir           string `json:"work_dir"`           // 工作目录
}

// ProcessingResult 处理结果
type ProcessingResult struct {
	Status         ProcessingStatus `json:"status"`          // 处理状态
	Chunks         []ChunkInfo      `json:"chunks"`          // 分片信息
	EncryptionKey  string           `json:"encryption_key"`  // 加密密钥(十六进制)
	TotalSize      int64            `json:"total_size"`      // 原文件大小
	ChunkCount     int              `json:"chunk_count"`     // 分片数量
	ProcessingTime time.Duration    `json:"processing_time"` // 处理耗时
	ErrorMessage   string           `json:"error_message"`   // 错误信息
	WorkDir        string           `json:"work_dir"`        // 工作目录路径

	// SHA256哈希相关字段
	OriginalFileHash string       `json:"original_file_hash"` // 原文件SHA256哈希
	ChunkHashes      []string     `json:"chunk_hashes"`       // 分片SHA256哈希列表
	HashSummary      *HashSummary `json:"hash_summary"`       // 哈希摘要信息

	// MixFile相关字段
	ChunkURLs   []string `json:"chunk_urls,omitempty"`   // 分片上传URL列表
	IndexURL    string   `json:"index_url,omitempty"`    // 索引文件URL
	ShareCode   string   `json:"share_code,omitempty"`   // MixFile分享码
	IndexHash   string   `json:"index_hash,omitempty"`   // 索引文件哈希
	MixFileMode bool     `json:"mixfile_mode,omitempty"` // 是否为MixFile模式
}

// ProgressCallback 进度回调函数
type ProgressCallback func(stage string, progress float64, message string)

// Processor 文件处理器
type Processor struct {
	config  *ProcessingConfig
	chunker *Chunker
	crypto  *Crypto
}

// NewProcessor 创建新的文件处理器
func NewProcessor(config *ProcessingConfig) *Processor {
	if config == nil {
		config = &ProcessingConfig{
			ChunkSizeMB:       1,
			EncryptionEnabled: true,
			KeepOriginal:      false,
			WorkDir:           "/tmp/fileprocessor",
		}
	}

	return &Processor{
		config:  config,
		chunker: NewChunker(config.ChunkSizeMB),
		crypto:  NewCrypto(nil),
	}
}

// ProcessFile 处理文件
func (p *Processor) ProcessFile(inputPath string, callback ProgressCallback) (*ProcessingResult, error) {
	startTime := time.Now()

	result := &ProcessingResult{
		Status: StatusIdle,
	}

	logger.Infof("Starting file processing: file=%s", inputPath)

	// 检查输入文件
	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		result.Status = StatusFailed
		result.ErrorMessage = "input file does not exist"
		return result, fmt.Errorf("input file does not exist: %s", inputPath)
	}

	// 创建工作目录
	workDir := filepath.Join(p.config.WorkDir, fmt.Sprintf("processing_%d", time.Now().Unix()))
	if err := os.MkdirAll(workDir, 0755); err != nil {
		result.Status = StatusFailed
		result.ErrorMessage = "failed to create work directory"
		return result, fmt.Errorf("failed to create work directory: %w", err)
	}

	// 设置工作目录到结果中
	result.WorkDir = workDir

	// 清理工作目录
	defer func() {
		if !p.config.KeepOriginal {
			os.RemoveAll(workDir)
		}
	}()

	// 第一阶段：文件分片
	result.Status = StatusChunking
	if callback != nil {
		callback("chunking", 0, "开始文件分片")
	}

	chunkDir := filepath.Join(workDir, "chunks")
	chunkResult, err := p.chunker.ChunkFile(inputPath, chunkDir, func(progress float64) {
		if callback != nil {
			callback("chunking", progress, fmt.Sprintf("分片进度: %.1f%%", progress))
		}
	})

	if err != nil {
		result.Status = StatusFailed
		result.ErrorMessage = fmt.Sprintf("chunking failed: %v", err)
		return result, fmt.Errorf("chunking failed: %w", err)
	}

	result.Chunks = chunkResult.Chunks
	result.TotalSize = chunkResult.TotalSize
	result.ChunkCount = len(chunkResult.Chunks)

	logger.Infof("File chunking completed: chunks=%d, total_size=%d", result.ChunkCount, result.TotalSize)

	// 第二阶段：加密分片（如果启用）
	if p.config.EncryptionEnabled {
		result.Status = StatusEncrypting
		if callback != nil {
			callback("encrypting", 0, "开始加密分片")
		}

		// 生成加密密钥
		key, err := p.crypto.GenerateKey()
		if err != nil {
			result.Status = StatusFailed
			result.ErrorMessage = fmt.Sprintf("key generation failed: %v", err)
			return result, fmt.Errorf("key generation failed: %w", err)
		}

		result.EncryptionKey = p.crypto.KeyToHex(key)

		// 加密每个分片
		encryptedDir := filepath.Join(workDir, "encrypted")
		if err := os.MkdirAll(encryptedDir, 0755); err != nil {
			result.Status = StatusFailed
			result.ErrorMessage = "failed to create encrypted directory"
			return result, fmt.Errorf("failed to create encrypted directory: %w", err)
		}

		for i, chunk := range result.Chunks {
			chunkPath := filepath.Join(chunkDir, chunk.Filename)
			encryptedPath := filepath.Join(encryptedDir, chunk.Filename+".enc")

			if err := p.crypto.EncryptFile(chunkPath, encryptedPath, key); err != nil {
				result.Status = StatusFailed
				result.ErrorMessage = fmt.Sprintf("encryption failed for chunk %d: %v", i, err)
				return result, fmt.Errorf("encryption failed for chunk %d: %w", i, err)
			}

			// 更新分片信息
			result.Chunks[i].Filename = chunk.Filename + ".enc"

			// 更新进度
			progress := float64(i+1) / float64(len(result.Chunks)) * 100.0
			if callback != nil {
				callback("encrypting", progress, fmt.Sprintf("加密进度: %d/%d", i+1, len(result.Chunks)))
			}
		}

		logger.Infof("File encryption completed: chunks=%d", len(result.Chunks))
	}

	// 第三阶段：计算哈希
	result.Status = StatusHashing
	if callback != nil {
		callback("hashing", 0, "开始计算哈希")
	}

	// 计算原文件哈希
	originalHash, err := p.crypto.CalculateFileHash(inputPath)
	if err != nil {
		logger.Warnf("Failed to calculate original file hash: %v", err)
	} else {
		result.OriginalFileHash = originalHash
		logger.Debugf("Original file hash: %s", originalHash)
	}

	// 计算分片哈希
	chunkPaths := p.GetAllChunkPaths(result)
	hashResults, err := p.crypto.CalculateChunkHashesConcurrent(chunkPaths, 3, func(completed, total int, currentFile string) {
		progress := float64(completed) / float64(total) * 100.0
		if callback != nil {
			callback("hashing", progress, fmt.Sprintf("计算哈希: %d/%d", completed, total))
		}
	})

	if err != nil {
		logger.Warnf("Failed to calculate chunk hashes: %v", err)
	} else {
		// 提取哈希值
		result.ChunkHashes = make([]string, len(hashResults))
		for i, hashResult := range hashResults {
			if hashResult.Error == "" {
				result.ChunkHashes[i] = hashResult.Hash
			} else {
				logger.Warnf("Failed to calculate hash for chunk %d: %s", i, hashResult.Error)
			}
		}

		// 生成哈希摘要
		result.HashSummary = p.crypto.GenerateHashSummary(hashResults)
		logger.Infof("Hash calculation completed: %d/%d chunks successful",
			result.HashSummary.ValidFiles, result.HashSummary.TotalFiles)
	}

	// 处理完成
	result.Status = StatusCompleted
	result.ProcessingTime = time.Since(startTime)

	if callback != nil {
		callback("completed", 100, "文件处理完成")
	}

	logger.Infof("File processing completed: file=%s, chunks=%d, time=%v",
		inputPath, result.ChunkCount, result.ProcessingTime)

	return result, nil
}

// GetChunkPath 获取分片文件路径
func (p *Processor) GetChunkPath(workDir string, chunk ChunkInfo) string {
	if p.config.EncryptionEnabled {
		return filepath.Join(workDir, "encrypted", chunk.Filename)
	}
	return filepath.Join(workDir, "chunks", chunk.Filename)
}

// GetChunkPathFromResult 从处理结果中获取分片文件路径
func (p *Processor) GetChunkPathFromResult(result *ProcessingResult, chunk ChunkInfo) string {
	if result.WorkDir == "" {
		logger.Warnf("ProcessingResult has empty WorkDir, using current config WorkDir")
		return p.GetChunkPath(p.config.WorkDir, chunk)
	}
	return p.GetChunkPath(result.WorkDir, chunk)
}

// GetAllChunkPaths 获取所有分片文件路径
func (p *Processor) GetAllChunkPaths(result *ProcessingResult) []string {
	var paths []string
	for _, chunk := range result.Chunks {
		path := p.GetChunkPathFromResult(result, chunk)
		paths = append(paths, path)
	}
	return paths
}

// ValidateResult 验证处理结果
func (p *Processor) ValidateResult(result *ProcessingResult) error {
	if result == nil {
		return fmt.Errorf("result is nil")
	}

	if result.Status != StatusCompleted {
		return fmt.Errorf("processing not completed: status=%s", result.Status)
	}

	if len(result.Chunks) == 0 {
		return fmt.Errorf("no chunks found")
	}

	if result.TotalSize <= 0 {
		return fmt.Errorf("invalid total size: %d", result.TotalSize)
	}

	if p.config.EncryptionEnabled && result.EncryptionKey == "" {
		return fmt.Errorf("encryption key is empty")
	}

	return nil
}

// CleanupWorkDir 清理工作目录
func (p *Processor) CleanupWorkDir(workDir string) error {
	if workDir == "" {
		return nil
	}

	logger.Infof("Cleaning up work directory: %s", workDir)
	return os.RemoveAll(workDir)
}
