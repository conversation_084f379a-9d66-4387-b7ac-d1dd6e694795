package fileprocessor

import (
	"fmt"
	"io"
	"os"
	"path/filepath"

	"magnet-downloader/pkg/logger"
)

// ChunkInfo 分片信息
type ChunkInfo struct {
	Index    int    `json:"index"`    // 分片索引
	Filename string `json:"filename"` // 分片文件名
	Size     int64  `json:"size"`     // 分片大小
	Offset   int64  `json:"offset"`   // 在原文件中的偏移量
}

// ChunkResult 分片结果
type ChunkResult struct {
	Chunks    []ChunkInfo `json:"chunks"`     // 分片列表
	TotalSize int64       `json:"total_size"` // 原文件总大小
	ChunkSize int64       `json:"chunk_size"` // 分片大小
}

// Chunker 文件分片器
type Chunker struct {
	chunkSize int64 // 分片大小（字节）
}

// NewChunker 创建新的分片器
func NewChunker(chunkSizeMB int) *Chunker {
	return &Chunker{
		chunkSize: int64(chunkSizeMB) * 1024 * 1024, // 转换为字节
	}
}

// ChunkFile 将文件分片
func (c *Chunker) ChunkFile(inputPath, outputDir string, progressCallback func(progress float64)) (*ChunkResult, error) {
	// 打开输入文件
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open input file: %w", err)
	}
	defer inputFile.Close()

	// 获取文件信息
	fileInfo, err := inputFile.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	totalSize := fileInfo.Size()
	if totalSize == 0 {
		return nil, fmt.Errorf("input file is empty")
	}

	// 创建输出目录
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create output directory: %w", err)
	}

	// 计算分片数量
	chunkCount := (totalSize + c.chunkSize - 1) / c.chunkSize
	chunks := make([]ChunkInfo, 0, chunkCount)

	logger.Infof("Starting file chunking: file=%s, size=%d, chunk_size=%d, chunk_count=%d",
		inputPath, totalSize, c.chunkSize, chunkCount)

	// 分片处理
	var offset int64 = 0
	for i := 0; i < int(chunkCount); i++ {
		// 计算当前分片大小
		currentChunkSize := c.chunkSize
		if offset+currentChunkSize > totalSize {
			currentChunkSize = totalSize - offset
		}

		// 生成分片文件名
		chunkFilename := fmt.Sprintf("chunk_%04d.bin", i)
		chunkPath := filepath.Join(outputDir, chunkFilename)

		// 创建分片文件
		chunkFile, err := os.Create(chunkPath)
		if err != nil {
			return nil, fmt.Errorf("failed to create chunk file %s: %w", chunkPath, err)
		}

		// 复制数据到分片文件
		_, err = inputFile.Seek(offset, io.SeekStart)
		if err != nil {
			chunkFile.Close()
			return nil, fmt.Errorf("failed to seek input file: %w", err)
		}

		written, err := io.CopyN(chunkFile, inputFile, currentChunkSize)
		chunkFile.Close()

		if err != nil && err != io.EOF {
			return nil, fmt.Errorf("failed to copy data to chunk file: %w", err)
		}

		if written != currentChunkSize {
			return nil, fmt.Errorf("chunk size mismatch: expected %d, got %d", currentChunkSize, written)
		}

		// 记录分片信息
		chunks = append(chunks, ChunkInfo{
			Index:    i,
			Filename: chunkFilename,
			Size:     written,
			Offset:   offset,
		})

		offset += written

		// 更新进度
		if progressCallback != nil {
			progress := float64(i+1) / float64(chunkCount) * 100.0
			progressCallback(progress)
		}

		logger.Debugf("Created chunk %d/%d: file=%s, size=%d", i+1, chunkCount, chunkFilename, written)
	}

	result := &ChunkResult{
		Chunks:    chunks,
		TotalSize: totalSize,
		ChunkSize: c.chunkSize,
	}

	logger.Infof("File chunking completed: chunks=%d, total_size=%d", len(chunks), totalSize)
	return result, nil
}

// ReassembleFile 重新组装文件
func (c *Chunker) ReassembleFile(chunkDir, outputPath string, chunks []ChunkInfo) error {
	// 创建输出文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer outputFile.Close()

	logger.Infof("Starting file reassembly: output=%s, chunks=%d", outputPath, len(chunks))

	// 按索引顺序组装分片
	for _, chunk := range chunks {
		chunkPath := filepath.Join(chunkDir, chunk.Filename)
		
		chunkFile, err := os.Open(chunkPath)
		if err != nil {
			return fmt.Errorf("failed to open chunk file %s: %w", chunkPath, err)
		}

		_, err = io.Copy(outputFile, chunkFile)
		chunkFile.Close()

		if err != nil {
			return fmt.Errorf("failed to copy chunk data: %w", err)
		}

		logger.Debugf("Reassembled chunk %d: file=%s, size=%d", chunk.Index, chunk.Filename, chunk.Size)
	}

	logger.Infof("File reassembly completed: output=%s", outputPath)
	return nil
}

// CleanupChunks 清理分片文件
func (c *Chunker) CleanupChunks(chunkDir string, chunks []ChunkInfo) error {
	logger.Infof("Cleaning up chunks: dir=%s, count=%d", chunkDir, len(chunks))

	for _, chunk := range chunks {
		chunkPath := filepath.Join(chunkDir, chunk.Filename)
		if err := os.Remove(chunkPath); err != nil {
			logger.Warnf("Failed to remove chunk file %s: %v", chunkPath, err)
		}
	}

	// 尝试删除空目录
	if err := os.Remove(chunkDir); err != nil {
		logger.Debugf("Failed to remove chunk directory %s: %v", chunkDir, err)
	}

	return nil
}
