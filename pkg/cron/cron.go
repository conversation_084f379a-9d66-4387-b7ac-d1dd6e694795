package cron

import (
	"context"
	"fmt"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"

	"github.com/robfig/cron/v3"
)

// Job 定时任务接口
type Job interface {
	// Execute 执行任务
	Execute(ctx context.Context) error
	// GetName 获取任务名称
	GetName() string
	// GetDescription 获取任务描述
	GetDescription() string
}

// JobWrapper 任务包装器
type JobWrapper struct {
	job         Job
	lastRun     time.Time
	nextRun     time.Time
	runCount    int64
	errorCount  int64
	isRunning   bool
	mutex       sync.RWMutex
}

// NewJobWrapper 创建任务包装器
func NewJobWrapper(job Job) *JobWrapper {
	return &JobWrapper{
		job: job,
	}
}

// Run 执行任务
func (jw *JobWrapper) Run() {
	jw.mutex.Lock()
	if jw.isRunning {
		logger.Warnf("Job %s is already running, skipping", jw.job.GetName())
		jw.mutex.Unlock()
		return
	}
	jw.isRunning = true
	jw.lastRun = time.Now()
	jw.runCount++
	jw.mutex.Unlock()

	defer func() {
		jw.mutex.Lock()
		jw.isRunning = false
		jw.mutex.Unlock()
	}()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	logger.Infof("Starting job: %s", jw.job.GetName())
	start := time.Now()

	if err := jw.job.Execute(ctx); err != nil {
		jw.mutex.Lock()
		jw.errorCount++
		jw.mutex.Unlock()
		logger.Errorf("Job %s failed: %v", jw.job.GetName(), err)
	} else {
		logger.Infof("Job %s completed successfully in %v", jw.job.GetName(), time.Since(start))
	}
}

// GetStats 获取任务统计信息
func (jw *JobWrapper) GetStats() map[string]interface{} {
	jw.mutex.RLock()
	defer jw.mutex.RUnlock()

	return map[string]interface{}{
		"name":        jw.job.GetName(),
		"description": jw.job.GetDescription(),
		"last_run":    jw.lastRun,
		"next_run":    jw.nextRun,
		"run_count":   jw.runCount,
		"error_count": jw.errorCount,
		"is_running":  jw.isRunning,
	}
}

// SetNextRun 设置下次运行时间
func (jw *JobWrapper) SetNextRun(t time.Time) {
	jw.mutex.Lock()
	defer jw.mutex.Unlock()
	jw.nextRun = t
}

// Manager Cron任务管理器
type Manager struct {
	cron     *cron.Cron
	jobs     map[string]*JobWrapper
	mutex    sync.RWMutex
	timezone *time.Location
}

// NewManager 创建Cron管理器
func NewManager(timezone string) (*Manager, error) {
	var tz *time.Location
	var err error

	if timezone == "" {
		tz = time.Local
	} else {
		tz, err = time.LoadLocation(timezone)
		if err != nil {
			return nil, fmt.Errorf("invalid timezone %s: %w", timezone, err)
		}
	}

	c := cron.New(
		cron.WithLocation(tz),
		cron.WithSeconds(), // 支持秒级精度
		cron.WithLogger(cron.VerbosePrintfLogger(logger.GetLogger())),
	)

	return &Manager{
		cron:     c,
		jobs:     make(map[string]*JobWrapper),
		timezone: tz,
	}, nil
}

// AddJob 添加定时任务
func (m *Manager) AddJob(spec string, job Job) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	jobName := job.GetName()
	if _, exists := m.jobs[jobName]; exists {
		return fmt.Errorf("job %s already exists", jobName)
	}

	wrapper := NewJobWrapper(job)
	entryID, err := m.cron.AddJob(spec, wrapper)
	if err != nil {
		return fmt.Errorf("failed to add job %s: %w", jobName, err)
	}

	// 设置下次运行时间
	entries := m.cron.Entries()
	for _, entry := range entries {
		if entry.ID == entryID {
			wrapper.SetNextRun(entry.Next)
			break
		}
	}

	m.jobs[jobName] = wrapper
	logger.Infof("Added cron job: %s with spec: %s", jobName, spec)
	return nil
}

// RemoveJob 移除定时任务
func (m *Manager) RemoveJob(jobName string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.jobs[jobName]; !exists {
		return fmt.Errorf("job %s not found", jobName)
	}

	// 从cron中移除任务需要通过EntryID，这里简化处理
	delete(m.jobs, jobName)
	logger.Infof("Removed cron job: %s", jobName)
	return nil
}

// Start 启动Cron调度器
func (m *Manager) Start() {
	m.cron.Start()
	logger.Info("Cron scheduler started")
}

// Stop 停止Cron调度器
func (m *Manager) Stop() {
	ctx := m.cron.Stop()
	<-ctx.Done()
	logger.Info("Cron scheduler stopped")
}

// GetJobs 获取所有任务信息
func (m *Manager) GetJobs() []map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	jobs := make([]map[string]interface{}, 0, len(m.jobs))
	for _, wrapper := range m.jobs {
		jobs = append(jobs, wrapper.GetStats())
	}
	return jobs
}

// GetJobStats 获取指定任务统计信息
func (m *Manager) GetJobStats(jobName string) (map[string]interface{}, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	wrapper, exists := m.jobs[jobName]
	if !exists {
		return nil, fmt.Errorf("job %s not found", jobName)
	}

	return wrapper.GetStats(), nil
}

// IsRunning 检查调度器是否运行中
func (m *Manager) IsRunning() bool {
	return len(m.cron.Entries()) > 0
}

// GetTimezone 获取时区
func (m *Manager) GetTimezone() *time.Location {
	return m.timezone
}

// ValidateCronSpec 验证Cron表达式
func ValidateCronSpec(spec string) error {
	parser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	_, err := parser.Parse(spec)
	return err
}
