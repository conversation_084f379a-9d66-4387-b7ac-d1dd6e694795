package mixfile

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"magnet-downloader/pkg/logger"
)

// ShareCodeVersion 分享码版本
const (
	ShareCodeVersion = "1.0"
	ShareCodePrefix  = "mf://"
)

// ShareCode 分享码结构
type ShareCode struct {
	Version       string    `json:"v"`          // 分享码版本
	IndexURL      string    `json:"index_url"`  // 索引文件URL
	IndexHash     string    `json:"index_hash"` // 索引文件哈希
	EncryptionKey string    `json:"key"`        // 加密密钥
	FileSize      int64     `json:"size"`       // 文件大小
	FileName      string    `json:"name"`       // 文件名
	CreatedAt     time.Time `json:"created"`    // 创建时间
	
	// 扩展字段
	Algorithm    string            `json:"algo,omitempty"`     // 加密算法
	ChunkCount   int               `json:"chunks,omitempty"`   // 分片数量
	Metadata     map[string]string `json:"meta,omitempty"`     // 元数据
	Description  string            `json:"desc,omitempty"`     // 描述
}

// NewShareCode 创建新的分享码
func NewShareCode(indexURL, indexHash, encryptionKey string) *ShareCode {
	return &ShareCode{
		Version:       ShareCodeVersion,
		IndexURL:      indexURL,
		IndexHash:     indexHash,
		EncryptionKey: encryptionKey,
		CreatedAt:     time.Now(),
		Metadata:      make(map[string]string),
		Algorithm:     "aes-gcm-256",
	}
}

// Validate 验证分享码的完整性
func (sc *ShareCode) Validate() error {
	if sc.Version == "" {
		return fmt.Errorf("version is required")
	}
	
	if sc.IndexURL == "" {
		return fmt.Errorf("index URL is required")
	}
	
	// 验证URL格式
	if _, err := url.Parse(sc.IndexURL); err != nil {
		return fmt.Errorf("invalid index URL: %w", err)
	}
	
	if sc.IndexHash == "" {
		return fmt.Errorf("index hash is required")
	}
	
	// 验证哈希长度（SHA256应该是64字符）
	if len(sc.IndexHash) != 64 {
		return fmt.Errorf("invalid index hash length: expected 64, got %d", len(sc.IndexHash))
	}
	
	if sc.EncryptionKey == "" {
		return fmt.Errorf("encryption key is required")
	}
	
	// 验证加密密钥长度（AES-256应该是64字符的十六进制）
	if len(sc.EncryptionKey) != 64 {
		return fmt.Errorf("invalid encryption key length: expected 64, got %d", len(sc.EncryptionKey))
	}
	
	if sc.FileSize < 0 {
		return fmt.Errorf("invalid file size: %d", sc.FileSize)
	}
	
	if sc.ChunkCount < 0 {
		return fmt.Errorf("invalid chunk count: %d", sc.ChunkCount)
	}
	
	return nil
}

// ToJSON 序列化为JSON
func (sc *ShareCode) ToJSON() ([]byte, error) {
	data, err := json.Marshal(sc)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal share code to JSON: %w", err)
	}
	
	logger.Debugf("Serialized share code to JSON: %d bytes", len(data))
	return data, nil
}

// FromJSON 从JSON反序列化
func ShareCodeFromJSON(data []byte) (*ShareCode, error) {
	var sc ShareCode
	if err := json.Unmarshal(data, &sc); err != nil {
		return nil, fmt.Errorf("failed to unmarshal share code from JSON: %w", err)
	}
	
	// 验证反序列化后的数据
	if err := sc.Validate(); err != nil {
		return nil, fmt.Errorf("invalid share code: %w", err)
	}
	
	logger.Debugf("Deserialized share code from JSON: %s", sc.FileName)
	return &sc, nil
}

// SetMetadata 设置元数据
func (sc *ShareCode) SetMetadata(key, value string) {
	if sc.Metadata == nil {
		sc.Metadata = make(map[string]string)
	}
	sc.Metadata[key] = value
}

// GetMetadata 获取元数据
func (sc *ShareCode) GetMetadata(key string) (string, bool) {
	if sc.Metadata == nil {
		return "", false
	}
	value, exists := sc.Metadata[key]
	return value, exists
}

// GetSummary 获取分享码摘要信息
func (sc *ShareCode) GetSummary() *ShareCodeSummary {
	return &ShareCodeSummary{
		Version:     sc.Version,
		FileName:    sc.FileName,
		FileSize:    sc.FileSize,
		ChunkCount:  sc.ChunkCount,
		CreatedAt:   sc.CreatedAt,
		Algorithm:   sc.Algorithm,
		Description: sc.Description,
	}
}

// ShareCodeSummary 分享码摘要
type ShareCodeSummary struct {
	Version     string    `json:"version"`
	FileName    string    `json:"filename"`
	FileSize    int64     `json:"filesize"`
	ChunkCount  int       `json:"chunk_count"`
	CreatedAt   time.Time `json:"created_at"`
	Algorithm   string    `json:"algorithm"`
	Description string    `json:"description"`
}

// IsExpired 检查分享码是否过期
func (sc *ShareCode) IsExpired(maxAge time.Duration) bool {
	if maxAge <= 0 {
		return false // 永不过期
	}
	return time.Since(sc.CreatedAt) > maxAge
}

// Clone 克隆分享码
func (sc *ShareCode) Clone() *ShareCode {
	clone := &ShareCode{
		Version:       sc.Version,
		IndexURL:      sc.IndexURL,
		IndexHash:     sc.IndexHash,
		EncryptionKey: sc.EncryptionKey,
		FileSize:      sc.FileSize,
		FileName:      sc.FileName,
		CreatedAt:     sc.CreatedAt,
		Algorithm:     sc.Algorithm,
		ChunkCount:    sc.ChunkCount,
		Description:   sc.Description,
		Metadata:      make(map[string]string),
	}
	
	// 复制元数据
	for k, v := range sc.Metadata {
		clone.Metadata[k] = v
	}
	
	return clone
}

// GetProtocol 获取协议前缀
func (sc *ShareCode) GetProtocol() string {
	return ShareCodePrefix
}

// IsValidProtocol 检查是否为有效的MixFile协议
func IsValidProtocol(shareCodeString string) bool {
	return strings.HasPrefix(shareCodeString, ShareCodePrefix)
}

// ExtractPayload 从完整分享码中提取载荷部分
func ExtractPayload(shareCodeString string) (string, error) {
	if !IsValidProtocol(shareCodeString) {
		return "", fmt.Errorf("invalid protocol: expected %s prefix", ShareCodePrefix)
	}
	
	payload := strings.TrimPrefix(shareCodeString, ShareCodePrefix)
	if payload == "" {
		return "", fmt.Errorf("empty payload")
	}
	
	return payload, nil
}