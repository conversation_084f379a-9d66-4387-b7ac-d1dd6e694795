package mixfile

import (
	"encoding/json"
	"fmt"
	"time"

	"magnet-downloader/pkg/logger"
)

// Version MixFile版本
const (
	CurrentVersion = "1.0.0"
)

// ChunkInfo 分片信息
type ChunkInfo struct {
	Index int    `json:"index"` // 分片索引
	URL   string `json:"url"`   // 分片URL
	Hash  string `json:"hash"`  // 分片SHA256哈希
	Size  int64  `json:"size"`  // 分片大小
}

// IndexFile 索引文件结构
type IndexFile struct {
	Version       string      `json:"version"`        // MixFile版本
	FileName      string      `json:"filename"`       // 原文件名
	FileSize      int64       `json:"filesize"`       // 原文件大小
	ChunkSize     int         `json:"chunksize"`      // 分片大小(字节)
	Chunks        []ChunkInfo `json:"chunks"`         // 分片列表
	EncryptionKey string      `json:"encryption_key"` // 加密密钥(十六进制)
	CreatedAt     time.Time   `json:"created_at"`     // 创建时间
	
	// 扩展字段
	OriginalFileHash string            `json:"original_file_hash"` // 原文件哈希
	Metadata         map[string]string `json:"metadata"`           // 元数据
	Algorithm        string            `json:"algorithm"`          // 加密算法
}
// NewIndexFile 创建新的索引文件
func NewIndexFile(fileName string, fileSize int64, chunkSize int) *IndexFile {
	return &IndexFile{
		Version:   CurrentVersion,
		FileName:  fileName,
		FileSize:  fileSize,
		ChunkSize: chunkSize,
		Chunks:    make([]ChunkInfo, 0),
		CreatedAt: time.Now(),
		Metadata:  make(map[string]string),
		Algorithm: "aes-gcm-256",
	}
}

// AddChunk 添加分片信息
func (idx *IndexFile) AddChunk(chunk ChunkInfo) {
	idx.Chunks = append(idx.Chunks, chunk)
	logger.Debugf("Added chunk to index: index=%d, url=%s, hash=%s, size=%d", 
		chunk.Index, chunk.URL, chunk.Hash, chunk.Size)
}

// GetChunk 获取指定索引的分片信息
func (idx *IndexFile) GetChunk(index int) (*ChunkInfo, error) {
	if index < 0 || index >= len(idx.Chunks) {
		return nil, fmt.Errorf("chunk index out of range: %d", index)
	}
	return &idx.Chunks[index], nil
}

// GetChunkCount 获取分片数量
func (idx *IndexFile) GetChunkCount() int {
	return len(idx.Chunks)
}

// GetTotalChunkSize 获取所有分片的总大小
func (idx *IndexFile) GetTotalChunkSize() int64 {
	var total int64
	for _, chunk := range idx.Chunks {
		total += chunk.Size
	}
	return total
}// Validate 验证索引文件的完整性
func (idx *IndexFile) Validate() error {
	if idx.Version == "" {
		return fmt.Errorf("version is required")
	}
	
	if idx.FileName == "" {
		return fmt.Errorf("filename is required")
	}
	
	if idx.FileSize <= 0 {
		return fmt.Errorf("invalid file size: %d", idx.FileSize)
	}
	
	if idx.ChunkSize <= 0 {
		return fmt.Errorf("invalid chunk size: %d", idx.ChunkSize)
	}
	
	if len(idx.Chunks) == 0 {
		return fmt.Errorf("no chunks found")
	}
	
	if idx.EncryptionKey == "" {
		return fmt.Errorf("encryption key is required")
	}
	
	// 验证分片索引连续性
	for i, chunk := range idx.Chunks {
		if chunk.Index != i {
			return fmt.Errorf("chunk index mismatch: expected %d, got %d", i, chunk.Index)
		}
		
		if chunk.URL == "" {
			return fmt.Errorf("chunk %d: URL is required", i)
		}
		
		if chunk.Hash == "" {
			return fmt.Errorf("chunk %d: hash is required", i)
		}
		
		if chunk.Size <= 0 {
			return fmt.Errorf("chunk %d: invalid size %d", i, chunk.Size)
		}
	}
	
	// 验证总大小一致性
	totalChunkSize := idx.GetTotalChunkSize()
	if totalChunkSize != idx.FileSize {
		logger.Warnf("Total chunk size (%d) does not match file size (%d)", 
			totalChunkSize, idx.FileSize)
	}
	
	return nil
}// ToJSON 序列化为JSON
func (idx *IndexFile) ToJSON() ([]byte, error) {
	data, err := json.MarshalIndent(idx, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal index to JSON: %w", err)
	}
	
	logger.Debugf("Serialized index to JSON: %d bytes", len(data))
	return data, nil
}

// FromJSON 从JSON反序列化
func FromJSON(data []byte) (*IndexFile, error) {
	var idx IndexFile
	if err := json.Unmarshal(data, &idx); err != nil {
		return nil, fmt.Errorf("failed to unmarshal index from JSON: %w", err)
	}
	
	// 验证反序列化后的数据
	if err := idx.Validate(); err != nil {
		return nil, fmt.Errorf("invalid index file: %w", err)
	}
	
	logger.Debugf("Deserialized index from JSON: %s, chunks=%d", 
		idx.FileName, len(idx.Chunks))
	return &idx, nil
}

// SetMetadata 设置元数据
func (idx *IndexFile) SetMetadata(key, value string) {
	if idx.Metadata == nil {
		idx.Metadata = make(map[string]string)
	}
	idx.Metadata[key] = value
}

// GetMetadata 获取元数据
func (idx *IndexFile) GetMetadata(key string) (string, bool) {
	if idx.Metadata == nil {
		return "", false
	}
	value, exists := idx.Metadata[key]
	return value, exists
}// IndexSummary 索引文件摘要
type IndexSummary struct {
	Version          string    `json:"version"`
	FileName         string    `json:"filename"`
	FileSize         int64     `json:"filesize"`
	ChunkCount       int       `json:"chunk_count"`
	TotalChunkSize   int64     `json:"total_chunk_size"`
	CreatedAt        time.Time `json:"created_at"`
	OriginalFileHash string    `json:"original_file_hash"`
	Algorithm        string    `json:"algorithm"`
}

// GetSummary 获取索引文件摘要信息
func (idx *IndexFile) GetSummary() *IndexSummary {
	return &IndexSummary{
		Version:          idx.Version,
		FileName:         idx.FileName,
		FileSize:         idx.FileSize,
		ChunkCount:       len(idx.Chunks),
		TotalChunkSize:   idx.GetTotalChunkSize(),
		CreatedAt:        idx.CreatedAt,
		OriginalFileHash: idx.OriginalFileHash,
		Algorithm:        idx.Algorithm,
	}
}

// UpdateChunkURL 更新分片URL
func (idx *IndexFile) UpdateChunkURL(index int, url string) error {
	if index < 0 || index >= len(idx.Chunks) {
		return fmt.Errorf("chunk index out of range: %d", index)
	}
	
	oldURL := idx.Chunks[index].URL
	idx.Chunks[index].URL = url
	
	logger.Debugf("Updated chunk URL: index=%d, old=%s, new=%s", 
		index, oldURL, url)
	return nil
}

// CalculateProgress 计算下载进度
func (idx *IndexFile) CalculateProgress(downloadedChunks []bool) float64 {
	if len(downloadedChunks) != len(idx.Chunks) {
		return 0.0
	}
	
	var downloadedSize int64
	for i, downloaded := range downloadedChunks {
		if downloaded {
			downloadedSize += idx.Chunks[i].Size
		}
	}
	
	if idx.FileSize == 0 {
		return 0.0
	}
	
	return float64(downloadedSize) / float64(idx.FileSize) * 100.0
}// Clone 克隆索引文件
func (idx *IndexFile) Clone() *IndexFile {
	clone := &IndexFile{
		Version:          idx.Version,
		FileName:         idx.FileName,
		FileSize:         idx.FileSize,
		ChunkSize:        idx.ChunkSize,
		EncryptionKey:    idx.EncryptionKey,
		CreatedAt:        idx.CreatedAt,
		OriginalFileHash: idx.OriginalFileHash,
		Algorithm:        idx.Algorithm,
		Chunks:           make([]ChunkInfo, len(idx.Chunks)),
		Metadata:         make(map[string]string),
	}
	
	// 复制分片信息
	copy(clone.Chunks, idx.Chunks)
	
	// 复制元数据
	for k, v := range idx.Metadata {
		clone.Metadata[k] = v
	}
	
	return clone
}