package mixfile

import (
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/steganography"
)

// RangeRequest Range请求信息
type RangeRequest struct {
	Start int64 `json:"start"` // 起始位置
	End   int64 `json:"end"`   // 结束位置
	Size  int64 `json:"size"`  // 请求大小
}

// MixFileRangeHandler MixFile Range请求处理器
type MixFileRangeHandler struct {
	downloader MixFileDownloader
	cache      *RangeCache
	mutex      sync.RWMutex
}

// RangeCache Range请求缓存
type RangeCache struct {
	chunks map[string]*CachedChunk // 缓存的分片数据
	mutex  sync.RWMutex
}

// CachedChunk 缓存的分片
type CachedChunk struct {
	Data      []byte    `json:"data"`       // 分片数据
	ChunkInfo ChunkInfo `json:"chunk_info"` // 分片信息
	LastUsed  int64     `json:"last_used"`  // 最后使用时间
}

// NewMixFileRangeHandler 创建Range请求处理器
func NewMixFileRangeHandler(downloader MixFileDownloader) *MixFileRangeHandler {
	return &MixFileRangeHandler{
		downloader: downloader,
		cache: &RangeCache{
			chunks: make(map[string]*CachedChunk),
		},
	}
}

// HandleRangeRequest 处理Range请求
func (h *MixFileRangeHandler) HandleRangeRequest(w http.ResponseWriter, r *http.Request, shareCode string) error {
	// 解析分享码获取文件信息
	downloadInfo, err := h.downloader.ParseShareCode(shareCode)
	if err != nil {
		return fmt.Errorf("failed to parse share code: %w", err)
	}

	// 解析Range头
	rangeHeader := r.Header.Get("Range")
	if rangeHeader == "" {
		// 没有Range请求，返回整个文件
		return h.handleFullFileRequest(w, r, shareCode, downloadInfo)
	}

	rangeReq, err := h.parseRangeHeader(rangeHeader, downloadInfo.FileSize)
	if err != nil {
		http.Error(w, "Invalid Range header", http.StatusRequestedRangeNotSatisfiable)
		return err
	}

	// 处理Range请求
	return h.handlePartialRequest(w, r, shareCode, downloadInfo, rangeReq)
}

// parseRangeHeader 解析Range头
func (h *MixFileRangeHandler) parseRangeHeader(rangeHeader string, fileSize int64) (*RangeRequest, error) {
	// 解析 "bytes=start-end" 格式
	if !strings.HasPrefix(rangeHeader, "bytes=") {
		return nil, fmt.Errorf("invalid range header format")
	}

	rangeSpec := strings.TrimPrefix(rangeHeader, "bytes=")
	parts := strings.Split(rangeSpec, "-")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid range specification")
	}

	var start, end int64
	var err error

	// 解析起始位置
	if parts[0] != "" {
		start, err = strconv.ParseInt(parts[0], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid start position: %w", err)
		}
	}

	// 解析结束位置
	if parts[1] != "" {
		end, err = strconv.ParseInt(parts[1], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid end position: %w", err)
		}
	} else {
		end = fileSize - 1
	}

	// 验证范围
	if start < 0 || end >= fileSize || start > end {
		return nil, fmt.Errorf("invalid range: %d-%d for file size %d", start, end, fileSize)
	}

	return &RangeRequest{
		Start: start,
		End:   end,
		Size:  end - start + 1,
	}, nil
}

// handleFullFileRequest 处理完整文件请求
func (h *MixFileRangeHandler) handleFullFileRequest(w http.ResponseWriter, r *http.Request, shareCode string, downloadInfo *ShareCode) error {
	// 设置响应头
	w.Header().Set("Content-Type", "application/octet-stream")
	w.Header().Set("Content-Length", strconv.FormatInt(downloadInfo.FileSize, 10))
	w.Header().Set("Accept-Ranges", "bytes")

	// 流式传输整个文件
	return h.streamFile(w, shareCode, 0, downloadInfo.FileSize-1)
}

// handlePartialRequest 处理部分内容请求
func (h *MixFileRangeHandler) handlePartialRequest(w http.ResponseWriter, r *http.Request, shareCode string, downloadInfo *ShareCode, rangeReq *RangeRequest) error {
	// 设置响应头
	w.Header().Set("Content-Type", "application/octet-stream")
	w.Header().Set("Content-Length", strconv.FormatInt(rangeReq.Size, 10))
	w.Header().Set("Content-Range", fmt.Sprintf("bytes %d-%d/%d", 
		rangeReq.Start, rangeReq.End, downloadInfo.FileSize))
	w.Header().Set("Accept-Ranges", "bytes")
	w.WriteHeader(http.StatusPartialContent)

	// 流式传输请求的范围
	return h.streamFile(w, shareCode, rangeReq.Start, rangeReq.End)
}

// streamFile 流式传输文件
func (h *MixFileRangeHandler) streamFile(w http.ResponseWriter, shareCode string, start, end int64) error {
	// 解析分享码获取索引信息
	downloadInfo, err := h.downloader.ParseShareCode(shareCode)
	if err != nil {
		return fmt.Errorf("failed to parse share code: %w", err)
	}

	// 下载索引文件
	index, err := h.downloadIndexFile(downloadInfo.IndexURL, downloadInfo.EncryptionKey)
	if err != nil {
		return fmt.Errorf("failed to download index file: %w", err)
	}

	// 计算需要的分片范围
	chunkSize := index.FileSize / int64(len(index.Chunks)) // 平均分片大小
	startChunk := int(start / chunkSize)
	endChunk := int(end / chunkSize)

	// 确保不超出分片范围
	if startChunk >= len(index.Chunks) {
		startChunk = len(index.Chunks) - 1
	}
	if endChunk >= len(index.Chunks) {
		endChunk = len(index.Chunks) - 1
	}

	logger.Debugf("Streaming range %d-%d, chunks %d-%d", start, end, startChunk, endChunk)

	// 流式传输相关分片
	currentPos := int64(0)
	for i := 0; i <= endChunk; i++ {
		chunk := index.Chunks[i]
		
		// 获取分片数据
		chunkData, err := h.getChunkData(chunk)
		if err != nil {
			return fmt.Errorf("failed to get chunk %d: %w", i, err)
		}

		// 计算在当前分片中的有效范围
		chunkStart := int64(0)
		chunkEnd := int64(len(chunkData) - 1)

		if currentPos < start {
			// 跳过开始部分
			skip := start - currentPos
			if skip < int64(len(chunkData)) {
				chunkStart = skip
			} else {
				currentPos += int64(len(chunkData))
				continue
			}
		}

		if currentPos+int64(len(chunkData))-1 > end {
			// 截断结束部分
			chunkEnd = end - currentPos + chunkStart
		}

		// 写入有效数据
		if chunkStart <= chunkEnd {
			validData := chunkData[chunkStart : chunkEnd+1]
			if _, err := w.Write(validData); err != nil {
				return fmt.Errorf("failed to write chunk data: %w", err)
			}
		}

		currentPos += int64(len(chunkData))
		
		// 如果已经超出请求范围，停止
		if currentPos > end {
			break
		}
	}

	return nil
}

// getChunkData 获取分片数据（带缓存）
func (h *MixFileRangeHandler) getChunkData(chunk ChunkInfo) ([]byte, error) {
	h.cache.mutex.RLock()
	cached, exists := h.cache.chunks[chunk.URL]
	h.cache.mutex.RUnlock()

	if exists {
		// 更新最后使用时间
		h.cache.mutex.Lock()
		cached.LastUsed = time.Now().Unix()
		h.cache.mutex.Unlock()
		return cached.Data, nil
	}

	// 下载分片数据
	resp, err := http.Get(chunk.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to download chunk: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download chunk: status %d", resp.StatusCode)
	}

	// 读取分片数据
	chunkData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read chunk data: %w", err)
	}

	// 从PNG中提取数据
	steganographer := steganography.NewSteganographer(steganography.DefaultConfig())
	extractedData, err := steganographer.ExtractDataFromPNG(chunkData)
	if err != nil {
		return nil, fmt.Errorf("failed to extract data from PNG: %w", err)
	}

	// 缓存分片数据
	h.cache.mutex.Lock()
	h.cache.chunks[chunk.URL] = &CachedChunk{
		Data:      extractedData,
		ChunkInfo: chunk,
		LastUsed:  time.Now().Unix(),
	}
	h.cache.mutex.Unlock()

	return extractedData, nil
}

// downloadIndexFile 下载索引文件（复用下载器的方法）
func (h *MixFileRangeHandler) downloadIndexFile(indexURL, encryptionKey string) (*IndexFile, error) {
	// 这里应该复用下载器的downloadIndexFile方法
	// 为了简化，我们直接调用下载器的内部方法
	if downloader, ok := h.downloader.(*mixFileDownloader); ok {
		return downloader.downloadIndexFile(indexURL, encryptionKey)
	}
	
	return nil, fmt.Errorf("unsupported downloader type")
}

// ClearCache 清理缓存
func (h *MixFileRangeHandler) ClearCache() {
	h.cache.mutex.Lock()
	defer h.cache.mutex.Unlock()
	
	h.cache.chunks = make(map[string]*CachedChunk)
	logger.Info("Range request cache cleared")
}

// GetCacheStats 获取缓存统计
func (h *MixFileRangeHandler) GetCacheStats() map[string]interface{} {
	h.cache.mutex.RLock()
	defer h.cache.mutex.RUnlock()
	
	totalSize := int64(0)
	for _, chunk := range h.cache.chunks {
		totalSize += int64(len(chunk.Data))
	}
	
	return map[string]interface{}{
		"cached_chunks": len(h.cache.chunks),
		"total_size":    totalSize,
	}
}