package mixfile

import (
	"bytes"
	"compress/gzip"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"fmt"
	"io"

	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/logger"
)

// IndexManager 索引管理器接口
type IndexManager interface {
	CreateIndex(chunks []ChunkInfo, metadata map[string]interface{}) (*IndexFile, error)
	SerializeIndex(index *IndexFile) ([]byte, error)
	ParseIndex(data []byte) (*IndexFile, error)
	EncryptIndex(indexData []byte, key []byte) ([]byte, error)
	DecryptIndex(encryptedData []byte, key []byte) ([]byte, error)
	CompressIndex(data []byte) ([]byte, error)
	DecompressIndex(compressedData []byte) ([]byte, error)
}

// indexManager 索引管理器实现
type indexManager struct {
	crypto *fileprocessor.Crypto
}

// NewIndexManager 创建新的索引管理器
func NewIndexManager() IndexManager {
	return &indexManager{
		crypto: fileprocessor.NewCrypto(nil),
	}
}// CreateIndex 创建索引文件
func (m *indexManager) CreateIndex(chunks []ChunkInfo, metadata map[string]interface{}) (*IndexFile, error) {
	if len(chunks) == 0 {
		return nil, fmt.Errorf("no chunks provided")
	}

	// 从第一个分片推断信息
	firstChunk := chunks[0]
	
	// 计算总文件大小
	var totalSize int64
	for _, chunk := range chunks {
		totalSize += chunk.Size
	}

	// 推断原文件名（从URL或其他元数据）
	fileName := "unknown"
	if name, ok := metadata["filename"]; ok {
		if nameStr, ok := name.(string); ok {
			fileName = nameStr
		}
	}

	// 推断分片大小（使用第一个分片的大小）
	chunkSize := int(firstChunk.Size)
	if size, ok := metadata["chunk_size"]; ok {
		if sizeInt, ok := size.(int); ok {
			chunkSize = sizeInt
		}
	}

	// 创建索引文件
	index := NewIndexFile(fileName, totalSize, chunkSize)

	// 设置加密密钥
	if key, ok := metadata["encryption_key"]; ok {
		if keyStr, ok := key.(string); ok {
			index.EncryptionKey = keyStr
		}
	}

	// 设置原文件哈希
	if hash, ok := metadata["original_file_hash"]; ok {
		if hashStr, ok := hash.(string); ok {
			index.OriginalFileHash = hashStr
		}
	}

	// 添加所有分片
	for _, chunk := range chunks {
		index.AddChunk(chunk)
	}

	// 设置其他元数据
	for key, value := range metadata {
		if valueStr, ok := value.(string); ok {
			index.SetMetadata(key, valueStr)
		}
	}

	// 验证索引文件
	if err := index.Validate(); err != nil {
		return nil, fmt.Errorf("invalid index file: %w", err)
	}

	logger.Infof("Created index file: %s, chunks=%d, size=%d", 
		fileName, len(chunks), totalSize)
	return index, nil
}// SerializeIndex 序列化索引文件
func (m *indexManager) SerializeIndex(index *IndexFile) ([]byte, error) {
	if index == nil {
		return nil, fmt.Errorf("index is nil")
	}

	// 验证索引文件
	if err := index.Validate(); err != nil {
		return nil, fmt.Errorf("invalid index file: %w", err)
	}

	// 序列化为JSON
	data, err := index.ToJSON()
	if err != nil {
		return nil, fmt.Errorf("failed to serialize index: %w", err)
	}

	logger.Debugf("Serialized index: %d bytes", len(data))
	return data, nil
}

// ParseIndex 解析索引文件
func (m *indexManager) ParseIndex(data []byte) (*IndexFile, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty data")
	}

	// 从JSON反序列化
	index, err := FromJSON(data)
	if err != nil {
		return nil, fmt.Errorf("failed to parse index: %w", err)
	}

	logger.Debugf("Parsed index: %s, chunks=%d", 
		index.FileName, len(index.Chunks))
	return index, nil
}// EncryptIndex 加密索引数据
func (m *indexManager) EncryptIndex(indexData []byte, key []byte) ([]byte, error) {
	if len(indexData) == 0 {
		return nil, fmt.Errorf("empty index data")
	}

	if len(key) != 32 {
		return nil, fmt.Errorf("invalid key size: expected 32 bytes, got %d", len(key))
	}

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nil, nonce, indexData, nil)

	// 组合nonce和密文
	result := make([]byte, len(nonce)+len(ciphertext))
	copy(result[:len(nonce)], nonce)
	copy(result[len(nonce):], ciphertext)

	logger.Debugf("Encrypted index: %d -> %d bytes", len(indexData), len(result))
	return result, nil
}// DecryptIndex 解密索引数据
func (m *indexManager) DecryptIndex(encryptedData []byte, key []byte) ([]byte, error) {
	if len(encryptedData) == 0 {
		return nil, fmt.Errorf("empty encrypted data")
	}

	if len(key) != 32 {
		return nil, fmt.Errorf("invalid key size: expected 32 bytes, got %d", len(key))
	}

	// 创建AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher: %w", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// 检查数据长度
	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	// 分离nonce和密文
	nonce := encryptedData[:nonceSize]
	ciphertext := encryptedData[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt index: %w", err)
	}

	logger.Debugf("Decrypted index: %d -> %d bytes", len(encryptedData), len(plaintext))
	return plaintext, nil
}// CompressIndex 压缩索引数据
func (m *indexManager) CompressIndex(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty data")
	}

	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)

	if _, err := writer.Write(data); err != nil {
		writer.Close()
		return nil, fmt.Errorf("failed to compress data: %w", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close gzip writer: %w", err)
	}

	compressed := buf.Bytes()
	compressionRatio := float64(len(compressed)) / float64(len(data)) * 100.0

	logger.Debugf("Compressed index: %d -> %d bytes (%.1f%%)", 
		len(data), len(compressed), compressionRatio)
	return compressed, nil
}

// DecompressIndex 解压缩索引数据
func (m *indexManager) DecompressIndex(compressedData []byte) ([]byte, error) {
	if len(compressedData) == 0 {
		return nil, fmt.Errorf("empty compressed data")
	}

	reader, err := gzip.NewReader(bytes.NewReader(compressedData))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer reader.Close()

	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress data: %w", err)
	}

	logger.Debugf("Decompressed index: %d -> %d bytes", 
		len(compressedData), len(data))
	return data, nil
}