package mixfile

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"path/filepath"
	"time"

	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/logger"
)

// ShareCodeProcessor 分享码处理器
type ShareCodeProcessor struct {
	manager ShareCodeManager
}

// NewShareCodeProcessor 创建新的分享码处理器
func NewShareCodeProcessor(enableCompression bool) *ShareCodeProcessor {
	return &ShareCodeProcessor{
		manager: NewShareCodeManager(enableCompression),
	}
}

// IndexToShareCode 将索引文件转换为分享码
func (p *ShareCodeProcessor) IndexToShareCode(index *IndexFile, indexURL string) (string, error) {
	if index == nil {
		return "", fmt.Errorf("index is nil")
	}

	if indexURL == "" {
		return "", fmt.Errorf("index URL is required")
	}

	// 计算索引文件哈希
	indexData, err := index.ToJSON()
	if err != nil {
		return "", fmt.Errorf("failed to serialize index: %w", err)
	}

	// 计算索引数据的SHA256哈希
	hash := sha256.Sum256(indexData)
	indexHash := hex.EncodeToString(hash[:])

	// 准备元数据
	metadata := map[string]interface{}{
		"filename":    index.FileName,
		"filesize":    index.FileSize,
		"chunk_count": len(index.Chunks),
		"algorithm":   index.Algorithm,
		"created_at":  index.CreatedAt.Format(time.RFC3339),
	}

	// 添加索引文件的元数据
	for key, value := range index.Metadata {
		metadata[key] = value
	}

	// 生成分享码
	shareCode, err := p.manager.GenerateShareCode(indexURL, indexHash, index.EncryptionKey, metadata)
	if err != nil {
		return "", fmt.Errorf("failed to generate share code: %w", err)
	}

	logger.Infof("Generated share code from index: %s -> %s",
		index.FileName, shareCode[:20]+"...")
	return shareCode, nil
} // ShareCodeToDownloadInfo 将分享码转换为下载信息
func (p *ShareCodeProcessor) ShareCodeToDownloadInfo(shareCodeString string) (*DownloadInfo, error) {
	// 解析分享码
	shareCode, err := p.manager.ParseShareCode(shareCodeString)
	if err != nil {
		return nil, fmt.Errorf("failed to parse share code: %w", err)
	}

	// 创建下载信息
	downloadInfo := &DownloadInfo{
		ShareCode:     shareCode,
		IndexURL:      shareCode.IndexURL,
		IndexHash:     shareCode.IndexHash,
		EncryptionKey: shareCode.EncryptionKey,
		FileName:      shareCode.FileName,
		FileSize:      shareCode.FileSize,
		ChunkCount:    shareCode.ChunkCount,
		Algorithm:     shareCode.Algorithm,
		CreatedAt:     shareCode.CreatedAt,
	}

	logger.Infof("Converted share code to download info: %s", shareCode.FileName)
	return downloadInfo, nil
}

// ValidateShareCodeFormat 验证分享码格式
func (p *ShareCodeProcessor) ValidateShareCodeFormat(shareCodeString string) error {
	return p.manager.ValidateShareCode(shareCodeString)
}

// GetShareCodeInfo 获取分享码信息（不完全解析）
func (p *ShareCodeProcessor) GetShareCodeInfo(shareCodeString string) (*ShareCodeSummary, error) {
	shareCode, err := p.manager.ParseShareCode(shareCodeString)
	if err != nil {
		return nil, fmt.Errorf("failed to parse share code: %w", err)
	}

	return shareCode.GetSummary(), nil
}

// GenerateShareCodeFromProcessingResult 从文件处理结果生成分享码
func (p *ShareCodeProcessor) GenerateShareCodeFromProcessingResult(result *fileprocessor.ProcessingResult, chunkURLs []string, indexURL string) (string, error) {
	// 首先创建索引文件
	indexProcessor := NewIndexProcessor()
	index, err := indexProcessor.ProcessingResultToIndex(result, chunkURLs)
	if err != nil {
		return "", fmt.Errorf("failed to create index: %w", err)
	}

	// 然后生成分享码
	shareCode, err := p.IndexToShareCode(index, indexURL)
	if err != nil {
		return "", fmt.Errorf("failed to generate share code: %w", err)
	}

	logger.Infof("Generated share code from processing result: %s",
		filepath.Base(result.WorkDir))
	return shareCode, nil
}

// DownloadInfo 下载信息结构
type DownloadInfo struct {
	ShareCode     *ShareCode `json:"share_code"`
	IndexURL      string     `json:"index_url"`
	IndexHash     string     `json:"index_hash"`
	EncryptionKey string     `json:"encryption_key"`
	FileName      string     `json:"filename"`
	FileSize      int64      `json:"filesize"`
	ChunkCount    int        `json:"chunk_count"`
	Algorithm     string     `json:"algorithm"`
	CreatedAt     time.Time  `json:"created_at"`
}
