package mixfile

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"io"

	"magnet-downloader/pkg/logger"
)

// ShareCodeManager 分享码管理器接口
type ShareCodeManager interface {
	GenerateShareCode(indexURL, indexHash, encryptionKey string, metadata map[string]interface{}) (string, error)
	ParseShareCode(shareCode string) (*ShareCode, error)
	ValidateShareCode(shareCode string) error
	EncodeShareCode(data *ShareCode) (string, error)
	DecodeShareCode(encoded string) (*ShareCode, error)
	CompressShareCode(data []byte) ([]byte, error)
	DecompressShareCode(compressedData []byte) ([]byte, error)
}

// shareCodeManager 分享码管理器实现
type shareCodeManager struct {
	enableCompression bool
	maxLength         int
}

// NewShareCodeManager 创建新的分享码管理器
func NewShareCodeManager(enableCompression bool) ShareCodeManager {
	return &shareCodeManager{
		enableCompression: enableCompression,
		maxLength:         2048, // 默认最大长度2KB
	}
}// GenerateShareCode 生成分享码
func (m *shareCodeManager) GenerateShareCode(indexURL, indexHash, encryptionKey string, metadata map[string]interface{}) (string, error) {
	// 创建分享码结构
	shareCode := NewShareCode(indexURL, indexHash, encryptionKey)
	
	// 设置元数据
	if fileName, ok := metadata["filename"]; ok {
		if fileNameStr, ok := fileName.(string); ok {
			shareCode.FileName = fileNameStr
		}
	}
	
	if fileSize, ok := metadata["filesize"]; ok {
		if fileSizeInt, ok := fileSize.(int64); ok {
			shareCode.FileSize = fileSizeInt
		}
	}
	
	if chunkCount, ok := metadata["chunk_count"]; ok {
		if chunkCountInt, ok := chunkCount.(int); ok {
			shareCode.ChunkCount = chunkCountInt
		}
	}
	
	if description, ok := metadata["description"]; ok {
		if descStr, ok := description.(string); ok {
			shareCode.Description = descStr
		}
	}
	
	// 设置其他元数据
	for key, value := range metadata {
		if valueStr, ok := value.(string); ok {
			shareCode.SetMetadata(key, valueStr)
		}
	}
	
	// 验证分享码
	if err := shareCode.Validate(); err != nil {
		return "", fmt.Errorf("invalid share code: %w", err)
	}
	
	// 编码分享码
	encoded, err := m.EncodeShareCode(shareCode)
	if err != nil {
		return "", fmt.Errorf("failed to encode share code: %w", err)
	}
	
	// 生成完整的分享码字符串
	fullShareCode := ShareCodePrefix + encoded
	
	// 检查长度限制
	if len(fullShareCode) > m.maxLength {
		return "", fmt.Errorf("share code too long: %d > %d", len(fullShareCode), m.maxLength)
	}
	
	logger.Infof("Generated share code: %s, length=%d", shareCode.FileName, len(fullShareCode))
	return fullShareCode, nil
}

// ParseShareCode 解析分享码
func (m *shareCodeManager) ParseShareCode(shareCode string) (*ShareCode, error) {
	// 提取载荷
	payload, err := ExtractPayload(shareCode)
	if err != nil {
		return nil, fmt.Errorf("failed to extract payload: %w", err)
	}
	
	// 解码分享码
	decoded, err := m.DecodeShareCode(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to decode share code: %w", err)
	}
	
	logger.Debugf("Parsed share code: %s", decoded.FileName)
	return decoded, nil
}

// ValidateShareCode 验证分享码
func (m *shareCodeManager) ValidateShareCode(shareCode string) error {
	// 检查协议前缀
	if !IsValidProtocol(shareCode) {
		return fmt.Errorf("invalid protocol: expected %s prefix", ShareCodePrefix)
	}
	
	// 检查长度
	if len(shareCode) > m.maxLength {
		return fmt.Errorf("share code too long: %d > %d", len(shareCode), m.maxLength)
	}
	
	// 尝试解析
	_, err := m.ParseShareCode(shareCode)
	if err != nil {
		return fmt.Errorf("parse failed: %w", err)
	}
	
	return nil
}// EncodeShareCode 编码分享码
func (m *shareCodeManager) EncodeShareCode(data *ShareCode) (string, error) {
	if data == nil {
		return "", fmt.Errorf("share code data is nil")
	}
	
	// 序列化为JSON
	jsonData, err := data.ToJSON()
	if err != nil {
		return "", fmt.Errorf("failed to serialize share code: %w", err)
	}
	
	// 压缩（如果启用）
	var processedData []byte
	if m.enableCompression {
		processedData, err = m.CompressShareCode(jsonData)
		if err != nil {
			return "", fmt.Errorf("failed to compress share code: %w", err)
		}
	} else {
		processedData = jsonData
	}
	
	// Base64编码
	encoded := base64.URLEncoding.EncodeToString(processedData)
	
	logger.Debugf("Encoded share code: %d -> %d bytes (compressed=%t)", 
		len(jsonData), len(encoded), m.enableCompression)
	return encoded, nil
}

// DecodeShareCode 解码分享码
func (m *shareCodeManager) DecodeShareCode(encoded string) (*ShareCode, error) {
	if encoded == "" {
		return nil, fmt.Errorf("empty encoded data")
	}
	
	// Base64解码
	decodedData, err := base64.URLEncoding.DecodeString(encoded)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %w", err)
	}
	
	// 解压缩（如果启用）
	var jsonData []byte
	if m.enableCompression {
		jsonData, err = m.DecompressShareCode(decodedData)
		if err != nil {
			return nil, fmt.Errorf("failed to decompress share code: %w", err)
		}
	} else {
		jsonData = decodedData
	}
	
	// 反序列化
	shareCode, err := ShareCodeFromJSON(jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to deserialize share code: %w", err)
	}
	
	logger.Debugf("Decoded share code: %d -> %d bytes (compressed=%t)", 
		len(encoded), len(jsonData), m.enableCompression)
	return shareCode, nil
}// CompressShareCode 压缩分享码数据
func (m *shareCodeManager) CompressShareCode(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty data")
	}
	
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)
	
	if _, err := writer.Write(data); err != nil {
		writer.Close()
		return nil, fmt.Errorf("failed to compress data: %w", err)
	}
	
	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close gzip writer: %w", err)
	}
	
	compressed := buf.Bytes()
	compressionRatio := float64(len(compressed)) / float64(len(data)) * 100.0
	
	logger.Debugf("Compressed share code: %d -> %d bytes (%.1f%%)", 
		len(data), len(compressed), compressionRatio)
	return compressed, nil
}

// DecompressShareCode 解压缩分享码数据
func (m *shareCodeManager) DecompressShareCode(compressedData []byte) ([]byte, error) {
	if len(compressedData) == 0 {
		return nil, fmt.Errorf("empty compressed data")
	}
	
	reader, err := gzip.NewReader(bytes.NewReader(compressedData))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer reader.Close()
	
	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress data: %w", err)
	}
	
	logger.Debugf("Decompressed share code: %d -> %d bytes", 
		len(compressedData), len(data))
	return data, nil
}