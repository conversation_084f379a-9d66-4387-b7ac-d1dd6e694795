package mixfile

import (
	"crypto/sha256"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/steganography"
)

// DownloadProgress 下载进度信息
type DownloadProgress struct {
	Stage           string  `json:"stage"`            // 当前阶段
	Progress        float64 `json:"progress"`         // 当前进度 (0-100)
	OverallProgress float64 `json:"overall_progress"` // 总体进度 (0-100)
	ChunkIndex      int     `json:"chunk_index"`      // 当前分片索引
	ChunkCount      int     `json:"chunk_count"`      // 总分片数
	DownloadedSize  int64   `json:"downloaded_size"`  // 已下载大小
	TotalSize       int64   `json:"total_size"`       // 总大小
	Speed           int64   `json:"speed"`            // 下载速度 (bytes/s)
	ETA             int64   `json:"eta"`              // 预计剩余时间 (秒)
	Message         string  `json:"message"`          // 进度消息
}

// ProgressCallback 进度回调函数
type ProgressCallback func(*DownloadProgress)

// MixFileDownloader MixFile下载器接口
type MixFileDownloader interface {
	// ParseShareCode 解析分享码
	ParseShareCode(shareCode string) (*ShareCode, error)
	
	// DownloadFile 下载文件
	DownloadFile(shareCode string, outputPath string, progressCallback ProgressCallback) error
	
	// VerifyFileIntegrity 验证文件完整性
	VerifyFileIntegrity(filePath string, expectedHash string) error
	
	// GetFileInfo 获取文件信息（不下载）
	GetFileInfo(shareCode string) (*FileInfo, error)
	
	// DownloadChunk 下载单个分片
	DownloadChunk(chunkURL string, outputPath string) error
	
	// ReassembleFile 重组文件
	ReassembleFile(chunkPaths []string, outputPath string) error
}

// FileInfo 文件信息
type FileInfo struct {
	FileName    string    `json:"filename"`
	FileSize    int64     `json:"filesize"`
	ChunkCount  int       `json:"chunk_count"`
	Algorithm   string    `json:"algorithm"`
	CreatedAt   time.Time `json:"created_at"`
	Description string    `json:"description"`
	Encrypted   bool      `json:"encrypted"`
}

// mixFileDownloader MixFile下载器实现
type mixFileDownloader struct {
	shareCodeProcessor *ShareCodeProcessor
	indexManager       IndexManager
	httpClient         *http.Client
	config             *DownloaderConfig
	mutex              sync.RWMutex
}

// DownloaderConfig 下载器配置
type DownloaderConfig struct {
	MaxConcurrentDownloads int           `json:"max_concurrent_downloads"` // 最大并发下载数
	ChunkTimeout          time.Duration `json:"chunk_timeout"`            // 分片下载超时
	RetryAttempts         int           `json:"retry_attempts"`           // 重试次数
	RetryDelay            time.Duration `json:"retry_delay"`              // 重试延迟
	EnableRangeRequests   bool          `json:"enable_range_requests"`    // 启用Range请求
	TempDir               string        `json:"temp_dir"`                 // 临时目录
	KeepTempFiles         bool          `json:"keep_temp_files"`          // 保留临时文件
}

// DefaultDownloaderConfig 默认下载器配置
func DefaultDownloaderConfig() *DownloaderConfig {
	return &DownloaderConfig{
		MaxConcurrentDownloads: 3,
		ChunkTimeout:          30 * time.Second,
		RetryAttempts:         3,
		RetryDelay:            time.Second,
		EnableRangeRequests:   true,
		TempDir:               "/tmp/mixfile_downloads",
		KeepTempFiles:         false,
	}
}

// NewMixFileDownloader 创建MixFile下载器
func NewMixFileDownloader(config *DownloaderConfig) MixFileDownloader {
	if config == nil {
		config = DefaultDownloaderConfig()
	}

	return &mixFileDownloader{
		shareCodeProcessor: NewShareCodeProcessor(true), // 启用压缩
		indexManager:       NewIndexManager(),
		httpClient: &http.Client{
			Timeout: config.ChunkTimeout,
		},
		config: config,
	}
}

// ParseShareCode 解析分享码
func (d *mixFileDownloader) ParseShareCode(shareCode string) (*ShareCode, error) {
	downloadInfo, err := d.shareCodeProcessor.ShareCodeToDownloadInfo(shareCode)
	if err != nil {
		return nil, err
	}
	return downloadInfo.ShareCode, nil
}

// GetFileInfo 获取文件信息
func (d *mixFileDownloader) GetFileInfo(shareCode string) (*FileInfo, error) {
	// 解析分享码
	downloadInfo, err := d.shareCodeProcessor.ShareCodeToDownloadInfo(shareCode)
	if err != nil {
		return nil, fmt.Errorf("failed to parse share code: %w", err)
	}

	return &FileInfo{
		FileName:    downloadInfo.FileName,
		FileSize:    downloadInfo.FileSize,
		ChunkCount:  downloadInfo.ChunkCount,
		Algorithm:   downloadInfo.Algorithm,
		CreatedAt:   downloadInfo.CreatedAt,
		Description: "", // 需要从索引文件中获取
		Encrypted:   downloadInfo.EncryptionKey != "",
	}, nil
}

// DownloadFile 下载文件
func (d *mixFileDownloader) DownloadFile(shareCode string, outputPath string, progressCallback ProgressCallback) error {
	logger.Infof("Starting MixFile download: output=%s", outputPath)

	// 阶段1：解析分享码
	if progressCallback != nil {
		progressCallback(&DownloadProgress{
			Stage:           "parsing_share_code",
			Progress:        0,
			OverallProgress: 0,
			Message:         "正在解析分享码...",
		})
	}

	downloadInfo, err := d.shareCodeProcessor.ShareCodeToDownloadInfo(shareCode)
	if err != nil {
		return fmt.Errorf("failed to parse share code: %w", err)
	}

	logger.Debugf("Parsed share code: filename=%s, chunks=%d", 
		downloadInfo.FileName, downloadInfo.ChunkCount)

	// 阶段2：下载索引文件
	if progressCallback != nil {
		progressCallback(&DownloadProgress{
			Stage:           "downloading_index",
			Progress:        0,
			OverallProgress: 10,
			Message:         "正在下载索引文件...",
		})
	}

	index, err := d.downloadIndexFile(downloadInfo.IndexURL, downloadInfo.EncryptionKey)
	if err != nil {
		return fmt.Errorf("failed to download index file: %w", err)
	}

	logger.Debugf("Downloaded index file: chunks=%d", len(index.Chunks))

	// 阶段3：创建临时目录
	tempDir := filepath.Join(d.config.TempDir, fmt.Sprintf("download_%d", time.Now().Unix()))
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return fmt.Errorf("failed to create temp directory: %w", err)
	}

	// 清理临时目录（如果配置要求）
	if !d.config.KeepTempFiles {
		defer func() {
			if err := os.RemoveAll(tempDir); err != nil {
				logger.Warnf("Failed to cleanup temp directory: %v", err)
			}
		}()
	}

	// 阶段4：下载所有分片
	if progressCallback != nil {
		progressCallback(&DownloadProgress{
			Stage:           "downloading_chunks",
			Progress:        0,
			OverallProgress: 20,
			ChunkCount:      len(index.Chunks),
			TotalSize:       index.FileSize,
			Message:         "正在下载分片文件...",
		})
	}

	chunkPaths, err := d.downloadChunks(index, tempDir, progressCallback)
	if err != nil {
		return fmt.Errorf("failed to download chunks: %w", err)
	}

	// 阶段5：重组文件
	if progressCallback != nil {
		progressCallback(&DownloadProgress{
			Stage:           "reassembling_file",
			Progress:        0,
			OverallProgress: 90,
			Message:         "正在重组文件...",
		})
	}

	if err := d.ReassembleFile(chunkPaths, outputPath); err != nil {
		return fmt.Errorf("failed to reassemble file: %w", err)
	}

	// 阶段6：验证文件完整性
	if progressCallback != nil {
		progressCallback(&DownloadProgress{
			Stage:           "verifying_integrity",
			Progress:        0,
			OverallProgress: 95,
			Message:         "正在验证文件完整性...",
		})
	}

	if err := d.VerifyFileIntegrity(outputPath, index.OriginalFileHash); err != nil {
		return fmt.Errorf("file integrity verification failed: %w", err)
	}

	// 完成
	if progressCallback != nil {
		progressCallback(&DownloadProgress{
			Stage:           "completed",
			Progress:        100,
			OverallProgress: 100,
			Message:         "下载完成",
		})
	}

	logger.Infof("MixFile download completed: %s", outputPath)
	return nil
}// downloadIndexFile 下载索引文件
func (d *mixFileDownloader) downloadIndexFile(indexURL, encryptionKey string) (*IndexFile, error) {
	// 下载索引文件
	resp, err := d.httpClient.Get(indexURL)
	if err != nil {
		return nil, fmt.Errorf("failed to download index file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download index file: status %d", resp.StatusCode)
	}

	// 读取索引数据
	indexData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read index data: %w", err)
	}

	// 从PNG中提取数据（如果是隐写术上传的）
	steganographer := steganography.NewSteganographer(steganography.DefaultConfig())
	extractedData, err := steganographer.ExtractDataFromPNG(indexData)
	if err != nil {
		// 如果提取失败，可能不是隐写术上传的，直接使用原始数据
		logger.Debugf("Failed to extract data from PNG, using raw data: %v", err)
		extractedData = indexData
	}

	// 解密索引数据（如果需要）
	var processedData []byte
	if encryptionKey != "" {
		key := []byte(encryptionKey)
		if len(key) > 32 {
			key = key[:32] // 截取前32字节作为AES-256密钥
		}

		decryptedData, err := d.indexManager.DecryptIndex(extractedData, key)
		if err != nil {
			// 如果解密失败，可能没有加密，直接使用提取的数据
			logger.Debugf("Failed to decrypt index data, using extracted data: %v", err)
			processedData = extractedData
		} else {
			processedData = decryptedData
		}
	} else {
		processedData = extractedData
	}

	// 解压缩索引数据（如果需要）
	decompressedData, err := d.indexManager.DecompressIndex(processedData)
	if err != nil {
		// 如果解压缩失败，可能没有压缩，直接使用处理后的数据
		logger.Debugf("Failed to decompress index data, using processed data: %v", err)
		decompressedData = processedData
	}

	// 解析索引文件
	index, err := d.indexManager.ParseIndex(decompressedData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse index file: %w", err)
	}

	logger.Debugf("Successfully downloaded and parsed index file: %s", index.FileName)
	return index, nil
}

// downloadChunks 下载所有分片
func (d *mixFileDownloader) downloadChunks(index *IndexFile, tempDir string, progressCallback ProgressCallback) ([]string, error) {
	chunkPaths := make([]string, len(index.Chunks))
	
	// 创建并发控制
	semaphore := make(chan struct{}, d.config.MaxConcurrentDownloads)
	var wg sync.WaitGroup
	var downloadErr error
	var downloadedSize int64
	startTime := time.Now()

	// 下载每个分片
	for i, chunk := range index.Chunks {
		wg.Add(1)
		go func(idx int, chunkInfo ChunkInfo) {
			defer wg.Done()
			
			// 获取并发许可
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 如果已经有错误，跳过
			if downloadErr != nil {
				return
			}

			// 下载分片
			chunkPath := filepath.Join(tempDir, fmt.Sprintf("chunk_%d.dat", idx))
			if err := d.downloadChunkWithRetry(chunkInfo.URL, chunkPath, chunkInfo.Hash); err != nil {
				downloadErr = fmt.Errorf("failed to download chunk %d: %w", idx, err)
				return
			}

			chunkPaths[idx] = chunkPath

			// 更新进度
			if progressCallback != nil {
				d.mutex.Lock()
				downloadedSize += chunkInfo.Size
				elapsed := time.Since(startTime)
				speed := int64(0)
				if elapsed.Seconds() > 0 {
					speed = downloadedSize / int64(elapsed.Seconds())
				}
				
				eta := int64(0)
				if speed > 0 {
					remaining := index.FileSize - downloadedSize
					eta = remaining / speed
				}

				progress := float64(idx+1) / float64(len(index.Chunks)) * 100
				overallProgress := 20 + (progress * 0.7) // 20-90%

				progressCallback(&DownloadProgress{
					Stage:           "downloading_chunks",
					Progress:        progress,
					OverallProgress: overallProgress,
					ChunkIndex:      idx + 1,
					ChunkCount:      len(index.Chunks),
					DownloadedSize:  downloadedSize,
					TotalSize:       index.FileSize,
					Speed:           speed,
					ETA:             eta,
					Message:         fmt.Sprintf("正在下载分片 %d/%d", idx+1, len(index.Chunks)),
				})
				d.mutex.Unlock()
			}

			logger.Debugf("Downloaded chunk %d: %s", idx, chunkPath)
		}(i, chunk)
	}

	// 等待所有下载完成
	wg.Wait()

	if downloadErr != nil {
		return nil, downloadErr
	}

	logger.Infof("All chunks downloaded successfully: %d chunks", len(chunkPaths))
	return chunkPaths, nil
}

// downloadChunkWithRetry 带重试的分片下载
func (d *mixFileDownloader) downloadChunkWithRetry(chunkURL, outputPath, expectedHash string) error {
	var lastErr error

	for attempt := 0; attempt < d.config.RetryAttempts; attempt++ {
		if attempt > 0 {
			logger.Debugf("Retrying chunk download (attempt %d/%d): %s", 
				attempt+1, d.config.RetryAttempts, chunkURL)
			time.Sleep(d.config.RetryDelay * time.Duration(attempt))
		}

		if err := d.DownloadChunk(chunkURL, outputPath); err != nil {
			lastErr = err
			continue
		}

		// 验证分片哈希
		if err := d.verifyChunkHash(outputPath, expectedHash); err != nil {
			lastErr = fmt.Errorf("chunk hash verification failed: %w", err)
			continue
		}

		return nil
	}

	return fmt.Errorf("chunk download failed after %d attempts: %w", 
		d.config.RetryAttempts, lastErr)
}

// DownloadChunk 下载单个分片
func (d *mixFileDownloader) DownloadChunk(chunkURL string, outputPath string) error {
	// 下载分片文件
	resp, err := d.httpClient.Get(chunkURL)
	if err != nil {
		return fmt.Errorf("failed to download chunk: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to download chunk: status %d", resp.StatusCode)
	}

	// 读取分片数据
	chunkData, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read chunk data: %w", err)
	}

	// 从PNG中提取加密数据
	steganographer := steganography.NewSteganographer(steganography.DefaultConfig())
	extractedData, err := steganographer.ExtractDataFromPNG(chunkData)
	if err != nil {
		return fmt.Errorf("failed to extract data from PNG: %w", err)
	}

	// 保存提取的数据
	if err := os.WriteFile(outputPath, extractedData, 0644); err != nil {
		return fmt.Errorf("failed to save chunk data: %w", err)
	}

	return nil
}

// verifyChunkHash 验证分片哈希
func (d *mixFileDownloader) verifyChunkHash(chunkPath, expectedHash string) error {
	data, err := os.ReadFile(chunkPath)
	if err != nil {
		return fmt.Errorf("failed to read chunk file: %w", err)
	}

	hash := sha256.Sum256(data)
	actualHash := fmt.Sprintf("%x", hash)

	if actualHash != expectedHash {
		return fmt.Errorf("hash mismatch: expected %s, got %s", expectedHash, actualHash)
	}

	return nil
}

// ReassembleFile 重组文件
func (d *mixFileDownloader) ReassembleFile(chunkPaths []string, outputPath string) error {
	// 创建输出文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer outputFile.Close()

	// 按顺序读取并写入每个分片
	for i, chunkPath := range chunkPaths {
		chunkData, err := os.ReadFile(chunkPath)
		if err != nil {
			return fmt.Errorf("failed to read chunk %d: %w", i, err)
		}

		if _, err := outputFile.Write(chunkData); err != nil {
			return fmt.Errorf("failed to write chunk %d: %w", i, err)
		}
	}

	logger.Infof("File reassembled successfully: %s", outputPath)
	return nil
}

// VerifyFileIntegrity 验证文件完整性
func (d *mixFileDownloader) VerifyFileIntegrity(filePath string, expectedHash string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return fmt.Errorf("failed to calculate file hash: %w", err)
	}

	actualHash := fmt.Sprintf("%x", hash.Sum(nil))
	if actualHash != expectedHash {
		return fmt.Errorf("file integrity check failed: expected %s, got %s", 
			expectedHash, actualHash)
	}

	logger.Infof("File integrity verified: %s", filePath)
	return nil
}