package mixfile

import (
	"fmt"
	"path/filepath"

	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/logger"
)

// IndexProcessor 索引处理器
type IndexProcessor struct {
	manager IndexManager
	crypto  *fileprocessor.Crypto
}

// NewIndexProcessor 创建新的索引处理器
func NewIndexProcessor() *IndexProcessor {
	return &IndexProcessor{
		manager: NewIndexManager(),
		crypto:  fileprocessor.NewCrypto(nil),
	}
}

// ProcessingResultToIndex 将文件处理结果转换为索引文件
func (p *IndexProcessor) ProcessingResultToIndex(result *fileprocessor.ProcessingResult, chunkURLs []string) (*IndexFile, error) {
	if result == nil {
		return nil, fmt.Errorf("processing result is nil")
	}

	if len(result.Chunks) != len(chunkURLs) {
		return nil, fmt.Errorf("chunk count mismatch: result=%d, urls=%d", 
			len(result.Chunks), len(chunkURLs))
	}

	if len(result.ChunkHashes) != len(result.Chunks) {
		return nil, fmt.Errorf("hash count mismatch: chunks=%d, hashes=%d", 
			len(result.Chunks), len(result.ChunkHashes))
	}

	// 创建分片信息列表
	chunks := make([]ChunkInfo, len(result.Chunks))
	for i, chunk := range result.Chunks {
		chunks[i] = ChunkInfo{
			Index: i,
			URL:   chunkURLs[i],
			Hash:  result.ChunkHashes[i],
			Size:  chunk.Size,
		}
	}

	// 准备元数据
	metadata := map[string]interface{}{
		"filename":           filepath.Base(result.WorkDir), // 使用工作目录名作为文件名
		"chunk_size":         result.Chunks[0].Size,
		"encryption_key":     result.EncryptionKey,
		"original_file_hash": result.OriginalFileHash,
		"processing_time":    result.ProcessingTime.String(),
	}

	// 创建索引文件
	index, err := p.manager.CreateIndex(chunks, metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to create index: %w", err)
	}

	logger.Infof("Converted processing result to index: chunks=%d, size=%d", 
		len(chunks), index.FileSize)
	return index, nil
}// PrepareIndexForUpload 准备索引文件用于上传
func (p *IndexProcessor) PrepareIndexForUpload(index *IndexFile, compress bool, encrypt bool, encryptionKey []byte) ([]byte, error) {
	if index == nil {
		return nil, fmt.Errorf("index is nil")
	}

	// 序列化索引文件
	data, err := p.manager.SerializeIndex(index)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize index: %w", err)
	}

	// 压缩（如果需要）
	if compress {
		data, err = p.manager.CompressIndex(data)
		if err != nil {
			return nil, fmt.Errorf("failed to compress index: %w", err)
		}
	}

	// 加密（如果需要）
	if encrypt {
		if len(encryptionKey) == 0 {
			return nil, fmt.Errorf("encryption key is required")
		}
		data, err = p.manager.EncryptIndex(data, encryptionKey)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt index: %w", err)
		}
	}

	logger.Infof("Prepared index for upload: compress=%t, encrypt=%t, size=%d", 
		compress, encrypt, len(data))
	return data, nil
}

// ParseIndexFromDownload 从下载的数据解析索引文件
func (p *IndexProcessor) ParseIndexFromDownload(data []byte, compressed bool, encrypted bool, encryptionKey []byte) (*IndexFile, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty data")
	}

	var err error

	// 解密（如果需要）
	if encrypted {
		if len(encryptionKey) == 0 {
			return nil, fmt.Errorf("encryption key is required")
		}
		data, err = p.manager.DecryptIndex(data, encryptionKey)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt index: %w", err)
		}
	}

	// 解压缩（如果需要）
	if compressed {
		data, err = p.manager.DecompressIndex(data)
		if err != nil {
			return nil, fmt.Errorf("failed to decompress index: %w", err)
		}
	}

	// 解析索引文件
	index, err := p.manager.ParseIndex(data)
	if err != nil {
		return nil, fmt.Errorf("failed to parse index: %w", err)
	}

	logger.Infof("Parsed index from download: %s, chunks=%d", 
		index.FileName, len(index.Chunks))
	return index, nil
}// ValidateIndexIntegrity 验证索引文件完整性
func (p *IndexProcessor) ValidateIndexIntegrity(index *IndexFile) error {
	if index == nil {
		return fmt.Errorf("index is nil")
	}

	// 基本验证
	if err := index.Validate(); err != nil {
		return fmt.Errorf("basic validation failed: %w", err)
	}

	// 验证加密密钥格式
	if index.EncryptionKey != "" {
		_, err := p.crypto.KeyFromHex(index.EncryptionKey)
		if err != nil {
			return fmt.Errorf("invalid encryption key: %w", err)
		}
	}

	// 验证哈希格式
	for i, chunk := range index.Chunks {
		if len(chunk.Hash) != 64 { // SHA256哈希长度
			return fmt.Errorf("chunk %d: invalid hash length %d", i, len(chunk.Hash))
		}
	}

	logger.Debugf("Index integrity validation passed: %s", index.FileName)
	return nil
}

// RepairIndex 修复索引文件
func (p *IndexProcessor) RepairIndex(index *IndexFile) (*IndexFile, error) {
	if index == nil {
		return nil, fmt.Errorf("index is nil")
	}

	// 克隆索引文件
	repaired := index.Clone()

	// 修复版本信息
	if repaired.Version == "" {
		repaired.Version = CurrentVersion
		logger.Debugf("Repaired version: set to %s", CurrentVersion)
	}

	// 修复算法信息
	if repaired.Algorithm == "" {
		repaired.Algorithm = "aes-gcm-256"
		logger.Debugf("Repaired algorithm: set to aes-gcm-256")
	}

	// 修复元数据
	if repaired.Metadata == nil {
		repaired.Metadata = make(map[string]string)
		logger.Debugf("Repaired metadata: initialized empty map")
	}

	// 修复分片索引
	for i := range repaired.Chunks {
		if repaired.Chunks[i].Index != i {
			repaired.Chunks[i].Index = i
			logger.Debugf("Repaired chunk index: %d", i)
		}
	}

	// 验证修复后的索引
	if err := p.ValidateIndexIntegrity(repaired); err != nil {
		return nil, fmt.Errorf("repair failed: %w", err)
	}

	logger.Infof("Index repaired successfully: %s", repaired.FileName)
	return repaired, nil
}