package aria2

import (
	"time"
)

// DownloadStatus aria2下载状态
type DownloadStatus string

const (
	StatusActive   DownloadStatus = "active"   // 活跃下载
	StatusWaiting  DownloadStatus = "waiting"  // 等待中
	StatusPaused   DownloadStatus = "paused"   // 已暂停
	StatusError    DownloadStatus = "error"    // 错误
	StatusComplete DownloadStatus = "complete" // 已完成
	StatusRemoved  DownloadStatus = "removed"  // 已移除
)

// DownloadInfo aria2下载信息
type DownloadInfo struct {
	GID             string         `json:"gid"`             // 全局标识符
	Status          DownloadStatus `json:"status"`          // 状态
	TotalLength     string         `json:"totalLength"`     // 总大小(字节)
	CompletedLength string         `json:"completedLength"` // 已完成大小(字节)
	UploadLength    string         `json:"uploadLength"`    // 已上传大小(字节)
	Bitfield        string         `json:"bitfield"`        // 位字段
	DownloadSpeed   string         `json:"downloadSpeed"`   // 下载速度(字节/秒)
	UploadSpeed     string         `json:"uploadSpeed"`     // 上传速度(字节/秒)
	InfoHash        string         `json:"infoHash"`        // 信息哈希
	NumSeeders      string         `json:"numSeeders"`      // 种子数
	Seeder          string         `json:"seeder"`          // 是否为种子
	PieceLength     string         `json:"pieceLength"`     // 片段长度
	NumPieces       string         `json:"numPieces"`       // 片段数量
	Connections     string         `json:"connections"`     // 连接数
	ErrorCode       string         `json:"errorCode"`       // 错误代码
	ErrorMessage    string         `json:"errorMessage"`    // 错误信息
	FollowedBy      []string       `json:"followedBy"`      // 后续任务
	Following       string         `json:"following"`       // 前置任务
	BelongsTo       string         `json:"belongsTo"`       // 所属任务
	Dir             string         `json:"dir"`             // 下载目录
	Files           []FileInfo     `json:"files"`           // 文件列表
	BTorrent        *BTorrentInfo  `json:"bittorrent"`      // BitTorrent信息
}

// FileInfo 文件信息
type FileInfo struct {
	Index           string `json:"index"`           // 文件索引
	Path            string `json:"path"`            // 文件路径
	Length          string `json:"length"`          // 文件大小
	CompletedLength string `json:"completedLength"` // 已完成大小
	Selected        string `json:"selected"`        // 是否选中
	URIs            []URI  `json:"uris"`            // URI列表
}

// URI URI信息
type URI struct {
	URI    string `json:"uri"`    // URI地址
	Status string `json:"status"` // 状态
}

// BTorrentInfo BitTorrent信息
type BTorrentInfo struct {
	AnnounceList [][]string   `json:"announceList"` // Tracker列表
	Comment      string       `json:"comment"`      // 注释
	CreationDate int64        `json:"creationDate"` // 创建日期
	Mode         string       `json:"mode"`         // 模式
	Info         *TorrentInfo `json:"info"`         // 种子信息
}

// TorrentInfo 种子信息
type TorrentInfo struct {
	Name string `json:"name"` // 名称
}

// GlobalStat 全局统计信息
type GlobalStat struct {
	DownloadSpeed   string `json:"downloadSpeed"`   // 总下载速度
	UploadSpeed     string `json:"uploadSpeed"`     // 总上传速度
	NumActive       string `json:"numActive"`       // 活跃下载数
	NumWaiting      string `json:"numWaiting"`      // 等待下载数
	NumStopped      string `json:"numStopped"`      // 已停止下载数
	NumStoppedTotal string `json:"numStoppedTotal"` // 总停止下载数
}

// Version 版本信息
type Version struct {
	Version         string   `json:"version"`         // 版本号
	EnabledFeatures []string `json:"enabledFeatures"` // 启用的功能
}

// SessionInfo 会话信息
type SessionInfo struct {
	SessionId string `json:"sessionId"` // 会话ID
}

// AddUriOptions 添加URI选项
type AddUriOptions struct {
	Dir                    string   `json:"dir,omitempty"`                       // 下载目录
	Out                    string   `json:"out,omitempty"`                       // 输出文件名
	MaxDownloadLimit       string   `json:"max-download-limit,omitempty"`        // 最大下载速度
	MaxUploadLimit         string   `json:"max-upload-limit,omitempty"`          // 最大上传速度
	MaxConnections         string   `json:"max-connection-per-server,omitempty"` // 最大连接数
	Split                  string   `json:"split,omitempty"`                     // 分段数
	MinSplitSize           string   `json:"min-split-size,omitempty"`            // 最小分段大小
	MaxConcurrentDownloads string   `json:"max-concurrent-downloads,omitempty"`  // 最大并发下载数
	ContinueDownload       string   `json:"continue,omitempty"`                  // 继续下载
	CheckIntegrity         string   `json:"check-integrity,omitempty"`           // 检查完整性
	AllowOverwrite         string   `json:"allow-overwrite,omitempty"`           // 允许覆盖
	AutoFileRenaming       string   `json:"auto-file-renaming,omitempty"`        // 自动文件重命名
	UserAgent              string   `json:"user-agent,omitempty"`                // 用户代理
	Header                 []string `json:"header,omitempty"`                    // HTTP头
}

// ChangeOptions 修改选项
type ChangeOptions struct {
	MaxDownloadLimit string `json:"max-download-limit,omitempty"`        // 最大下载速度
	MaxUploadLimit   string `json:"max-upload-limit,omitempty"`          // 最大上传速度
	MaxConnections   string `json:"max-connection-per-server,omitempty"` // 最大连接数
}

// JSONRPCRequest JSON-RPC请求
type JSONRPCRequest struct {
	JSONRPC string        `json:"jsonrpc"` // JSON-RPC版本
	ID      string        `json:"id"`      // 请求ID
	Method  string        `json:"method"`  // 方法名
	Params  []interface{} `json:"params"`  // 参数列表
}

// JSONRPCResponse JSON-RPC响应
type JSONRPCResponse struct {
	JSONRPC string        `json:"jsonrpc"` // JSON-RPC版本
	ID      string        `json:"id"`      // 请求ID
	Result  interface{}   `json:"result"`  // 结果
	Error   *JSONRPCError `json:"error"`   // 错误信息
}

// JSONRPCError JSON-RPC错误
type JSONRPCError struct {
	Code    int    `json:"code"`    // 错误代码
	Message string `json:"message"` // 错误信息
}

// ClientConfig aria2客户端配置
type ClientConfig struct {
	Host    string        `json:"host"`    // 主机地址
	Port    int           `json:"port"`    // 端口
	Secret  string        `json:"secret"`  // 密钥
	Timeout time.Duration `json:"timeout"` // 超时时间
	Secure  bool          `json:"secure"`  // 是否使用HTTPS
}

// DefaultOptions 默认选项
var DefaultOptions = &AddUriOptions{
	MaxConnections:         "16",
	Split:                  "16",
	MinSplitSize:           "1M",
	MaxConcurrentDownloads: "5",
	ContinueDownload:       "true",
	CheckIntegrity:         "true",
	AllowOverwrite:         "false",
	AutoFileRenaming:       "true",
	UserAgent:              "magnet-downloader/1.0",
}

// ParseDownloadSpeed 解析下载速度(字节/秒)
func (di *DownloadInfo) ParseDownloadSpeed() int64 {
	if di.DownloadSpeed == "" {
		return 0
	}
	// 简单解析，实际应该处理字符串转换
	return 0
}

// ParseTotalLength 解析总大小(字节)
func (di *DownloadInfo) ParseTotalLength() int64 {
	if di.TotalLength == "" {
		return 0
	}
	// 简单解析，实际应该处理字符串转换
	return 0
}

// ParseCompletedLength 解析已完成大小(字节)
func (di *DownloadInfo) ParseCompletedLength() int64 {
	if di.CompletedLength == "" {
		return 0
	}
	// 简单解析，实际应该处理字符串转换
	return 0
}

// GetProgress 获取下载进度(0-100)
func (di *DownloadInfo) GetProgress() float64 {
	total := di.ParseTotalLength()
	if total == 0 {
		return 0
	}
	completed := di.ParseCompletedLength()
	return float64(completed) / float64(total) * 100
}

// IsActive 检查是否为活跃状态
func (di *DownloadInfo) IsActive() bool {
	return di.Status == StatusActive
}

// IsCompleted 检查是否已完成
func (di *DownloadInfo) IsCompleted() bool {
	return di.Status == StatusComplete
}

// IsPaused 检查是否已暂停
func (di *DownloadInfo) IsPaused() bool {
	return di.Status == StatusPaused
}

// HasError 检查是否有错误
func (di *DownloadInfo) HasError() bool {
	return di.Status == StatusError
}

// GetFileName 获取文件名
func (di *DownloadInfo) GetFileName() string {
	if len(di.Files) > 0 {
		return di.Files[0].Path
	}
	if di.BTorrent != nil && di.BTorrent.Info != nil {
		return di.BTorrent.Info.Name
	}
	return ""
}
