package aria2

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"magnet-downloader/pkg/logger"
)

// Client aria2客户端
type Client struct {
	config *ClientConfig
	client *http.Client
	reqID  int64
}

// NewClient 创建aria2客户端
func NewClient(config *ClientConfig) *Client {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	return &Client{
		config: config,
		client: &http.Client{
			Timeout: config.Timeout,
		},
		reqID: 0,
	}
}

// getURL 获取aria2 RPC URL
func (c *Client) getURL() string {
	scheme := "http"
	if c.config.Secure {
		scheme = "https"
	}
	return fmt.Sprintf("%s://%s:%d/jsonrpc", scheme, c.config.Host, c.config.Port)
}

// getNextID 获取下一个请求ID
func (c *Client) getNextID() string {
	c.reqID++
	return strconv.FormatInt(c.reqID, 10)
}

// buildParams 构建参数列表
func (c *Client) buildParams(params ...interface{}) []interface{} {
	var result []interface{}

	// 如果配置了密钥，添加到参数开头
	if c.config.Secret != "" {
		result = append(result, "token:"+c.config.Secret)
	}

	// 添加其他参数
	result = append(result, params...)
	return result
}

// call 调用aria2 JSON-RPC方法
func (c *Client) call(method string, params ...interface{}) (*JSONRPCResponse, error) {
	// 构建请求
	request := &JSONRPCRequest{
		JSONRPC: "2.0",
		ID:      c.getNextID(),
		Method:  method,
		Params:  c.buildParams(params...),
	}

	// 序列化请求
	requestData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	logger.Debugf("aria2 RPC request: %s %s", method, string(requestData))

	// 发送HTTP请求
	resp, err := c.client.Post(c.getURL(), "application/json", bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	logger.Debugf("aria2 RPC response: %s", string(responseData))

	// 解析响应
	var response JSONRPCResponse
	if err := json.Unmarshal(responseData, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 检查错误
	if response.Error != nil {
		return nil, fmt.Errorf("aria2 error %d: %s", response.Error.Code, response.Error.Message)
	}

	return &response, nil
}

// AddURI 添加URI下载任务
func (c *Client) AddURI(uris []string, options *AddUriOptions) (string, error) {
	params := []interface{}{uris}

	if options != nil {
		params = append(params, options)
	}

	resp, err := c.call("aria2.addUri", params...)
	if err != nil {
		return "", err
	}

	gid, ok := resp.Result.(string)
	if !ok {
		return "", fmt.Errorf("invalid response format")
	}

	logger.Infof("Added download task: GID=%s, URIs=%v", gid, uris)
	return gid, nil
}

// AddMagnet 添加磁力链接下载任务
func (c *Client) AddMagnet(magnetURI string, options *AddUriOptions) (string, error) {
	return c.AddURI([]string{magnetURI}, options)
}

// Remove 移除下载任务
func (c *Client) Remove(gid string) (string, error) {
	resp, err := c.call("aria2.remove", gid)
	if err != nil {
		return "", err
	}

	removedGid, ok := resp.Result.(string)
	if !ok {
		return "", fmt.Errorf("invalid response format")
	}

	logger.Infof("Removed download task: GID=%s", gid)
	return removedGid, nil
}

// ForceRemove 强制移除下载任务
func (c *Client) ForceRemove(gid string) (string, error) {
	resp, err := c.call("aria2.forceRemove", gid)
	if err != nil {
		return "", err
	}

	removedGid, ok := resp.Result.(string)
	if !ok {
		return "", fmt.Errorf("invalid response format")
	}

	logger.Infof("Force removed download task: GID=%s", gid)
	return removedGid, nil
}

// Pause 暂停下载任务
func (c *Client) Pause(gid string) (string, error) {
	resp, err := c.call("aria2.pause", gid)
	if err != nil {
		return "", err
	}

	pausedGid, ok := resp.Result.(string)
	if !ok {
		return "", fmt.Errorf("invalid response format")
	}

	logger.Infof("Paused download task: GID=%s", gid)
	return pausedGid, nil
}

// Unpause 恢复下载任务
func (c *Client) Unpause(gid string) (string, error) {
	resp, err := c.call("aria2.unpause", gid)
	if err != nil {
		return "", err
	}

	unpausedGid, ok := resp.Result.(string)
	if !ok {
		return "", fmt.Errorf("invalid response format")
	}

	logger.Infof("Unpaused download task: GID=%s", gid)
	return unpausedGid, nil
}

// TellStatus 获取下载状态
func (c *Client) TellStatus(gid string, keys ...string) (*DownloadInfo, error) {
	params := []interface{}{gid}
	if len(keys) > 0 {
		params = append(params, keys)
	}

	resp, err := c.call("aria2.tellStatus", params...)
	if err != nil {
		return nil, err
	}

	var downloadInfo DownloadInfo
	resultBytes, err := json.Marshal(resp.Result)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal result: %w", err)
	}

	if err := json.Unmarshal(resultBytes, &downloadInfo); err != nil {
		return nil, fmt.Errorf("failed to unmarshal download info: %w", err)
	}

	return &downloadInfo, nil
}

// TellActive 获取活跃下载列表
func (c *Client) TellActive(keys ...string) ([]*DownloadInfo, error) {
	params := []interface{}{}
	if len(keys) > 0 {
		params = append(params, keys)
	}

	resp, err := c.call("aria2.tellActive", params...)
	if err != nil {
		return nil, err
	}

	var downloads []*DownloadInfo
	resultBytes, err := json.Marshal(resp.Result)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal result: %w", err)
	}

	if err := json.Unmarshal(resultBytes, &downloads); err != nil {
		return nil, fmt.Errorf("failed to unmarshal downloads: %w", err)
	}

	return downloads, nil
}

// TellWaiting 获取等待下载列表
func (c *Client) TellWaiting(offset, num int, keys ...string) ([]*DownloadInfo, error) {
	params := []interface{}{offset, num}
	if len(keys) > 0 {
		params = append(params, keys)
	}

	resp, err := c.call("aria2.tellWaiting", params...)
	if err != nil {
		return nil, err
	}

	var downloads []*DownloadInfo
	resultBytes, err := json.Marshal(resp.Result)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal result: %w", err)
	}

	if err := json.Unmarshal(resultBytes, &downloads); err != nil {
		return nil, fmt.Errorf("failed to unmarshal downloads: %w", err)
	}

	return downloads, nil
}

// TellStopped 获取已停止下载列表
func (c *Client) TellStopped(offset, num int, keys ...string) ([]*DownloadInfo, error) {
	params := []interface{}{offset, num}
	if len(keys) > 0 {
		params = append(params, keys)
	}

	resp, err := c.call("aria2.tellStopped", params...)
	if err != nil {
		return nil, err
	}

	var downloads []*DownloadInfo
	resultBytes, err := json.Marshal(resp.Result)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal result: %w", err)
	}

	if err := json.Unmarshal(resultBytes, &downloads); err != nil {
		return nil, fmt.Errorf("failed to unmarshal downloads: %w", err)
	}

	return downloads, nil
}

// ChangeOption 修改下载选项
func (c *Client) ChangeOption(gid string, options *ChangeOptions) (string, error) {
	resp, err := c.call("aria2.changeOption", gid, options)
	if err != nil {
		return "", err
	}

	result, ok := resp.Result.(string)
	if !ok {
		return "", fmt.Errorf("invalid response format")
	}

	logger.Infof("Changed options for task: GID=%s", gid)
	return result, nil
}

// GetGlobalStat 获取全局统计信息
func (c *Client) GetGlobalStat() (*GlobalStat, error) {
	resp, err := c.call("aria2.getGlobalStat")
	if err != nil {
		return nil, err
	}

	var globalStat GlobalStat
	resultBytes, err := json.Marshal(resp.Result)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal result: %w", err)
	}

	if err := json.Unmarshal(resultBytes, &globalStat); err != nil {
		return nil, fmt.Errorf("failed to unmarshal global stat: %w", err)
	}

	return &globalStat, nil
}

// GetVersion 获取版本信息
func (c *Client) GetVersion() (*Version, error) {
	resp, err := c.call("aria2.getVersion")
	if err != nil {
		return nil, err
	}

	var version Version
	resultBytes, err := json.Marshal(resp.Result)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal result: %w", err)
	}

	if err := json.Unmarshal(resultBytes, &version); err != nil {
		return nil, fmt.Errorf("failed to unmarshal version: %w", err)
	}

	return &version, nil
}

// Ping 检查连接状态
func (c *Client) Ping() error {
	_, err := c.GetVersion()
	return err
}
