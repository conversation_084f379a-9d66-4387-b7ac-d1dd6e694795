package doodstream

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
)

// 全局速率限制器
var (
	lastDoodStreamRequest = time.Now()
	doodStreamMutex       sync.Mutex
)

// Config DoodStream配置
type Config struct {
	APIKey     string        `json:"api_key"`     // API密钥
	BaseURL    string        `json:"base_url"`    // API基础URL
	Timeout    time.Duration `json:"timeout"`     // 请求超时时间
	MaxRetries int           `json:"max_retries"` // 最大重试次数
}

// UploadServerResponse 获取上传服务器响应
type UploadServerResponse struct {
	Message    string `json:"msg"`
	ServerTime string `json:"server_time"`
	Status     int    `json:"status"`
	Result     string `json:"result"` // 上传服务器URL
}

// UploadResponse 上传响应
type UploadResponse struct {
	Message    string `json:"msg"`
	ServerTime string `json:"server_time"`
	Status     int    `json:"status"`
	Result     []struct {
		DownloadURL    string `json:"download_url"`
		SingleImg      string `json:"single_img"`
		Status         int    `json:"status"`
		FileCode       string `json:"filecode"`
		SplashImg      string `json:"splash_img"`
		CanPlay        int    `json:"canplay"`
		Size           string `json:"size"`
		Length         string `json:"length"`
		Uploaded       string `json:"uploaded"`
		ProtectedEmbed string `json:"protected_embed"`
		ProtectedDL    string `json:"protected_dl"`
		Title          string `json:"title"`
	} `json:"result"`
}

// UploadResult 上传结果（统一接口）
type UploadResult struct {
	Success  bool   `json:"success"`
	URL      string `json:"url"`       // 下载链接
	PlayURL  string `json:"play_url"`  // 播放链接（protected_embed）
	FileCode string `json:"file_code"` // 文件代码
	Size     int64  `json:"size"`      // 文件大小
	Title    string `json:"title"`     // 文件标题
	CanPlay  bool   `json:"can_play"`  // 是否可播放
	Error    string `json:"error,omitempty"`
}

// Client DoodStream客户端
type Client struct {
	config     *Config
	httpClient *http.Client
	mutex      sync.RWMutex
}

// NewClient 创建新的DoodStream客户端
func NewClient(config *Config) *Client {
	if config == nil {
		config = &Config{
			BaseURL:    "https://doodapi.co",
			Timeout:    300 * time.Second, // 5分钟超时，适合大文件上传
			MaxRetries: 3,
		}
	}

	if config.BaseURL == "" {
		config.BaseURL = "https://doodapi.co"
	}

	if config.Timeout == 0 {
		config.Timeout = 300 * time.Second
	}

	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}

	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// GetUploadServer 获取上传服务器URL
func (c *Client) GetUploadServer() (string, error) {
	if c.config.APIKey == "" {
		return "", fmt.Errorf("API key is required")
	}

	// 应用速率限制
	doodStreamRateLimit()

	url := fmt.Sprintf("%s/api/upload/server?key=%s", c.config.BaseURL, c.config.APIKey)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("User-Agent", "magnet-downloader/1.0")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	var response UploadServerResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	if response.Status != 200 {
		return "", fmt.Errorf("API error: %s", response.Message)
	}

	logger.Debugf("Got upload server URL: %s", response.Result)
	return response.Result, nil
} // UploadFile 上传文件
func (c *Client) UploadFile(filePath string) (*UploadResult, error) {
	// 验证文件路径
	if filePath == "" {
		return nil, NewValidationError("文件路径不能为空", "filePath is empty")
	}

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return nil, NewFileError(ErrorTypeFileAccess, "文件不存在", err)
	}
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "无法访问文件", err)
	}

	// 检查文件大小
	if fileInfo.Size() == 0 {
		return nil, NewFileError(ErrorTypeFileSize, "文件大小为0", fmt.Errorf("file size is 0"))
	}

	// 检查文件大小限制（例如：5GB）
	maxFileSize := int64(5 * 1024 * 1024 * 1024) // 5GB
	if fileInfo.Size() > maxFileSize {
		return nil, NewFileError(ErrorTypeFileSize,
			fmt.Sprintf("文件大小超出限制，最大支持%.1fGB", float64(maxFileSize)/(1024*1024*1024)),
			fmt.Errorf("file size %d exceeds limit %d", fileInfo.Size(), maxFileSize))
	}

	filename := filepath.Base(filePath)
	logger.Infof("Starting upload for file: %s (size: %d bytes)", filename, fileInfo.Size())

	return c.UploadFileStream(filePath, filename)
}

// UploadFileStream 流式上传文件
func (c *Client) UploadFileStream(filePath, filename string) (*UploadResult, error) {
	// 1. 获取上传服务器URL
	var serverURL string
	var lastErr error

	// 重试获取上传服务器URL
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			waitTime := c.calculateBackoff(attempt, isRateLimitError(lastErr))
			logger.Debugf("Retrying get upload server after %v (attempt %d/%d)", waitTime, attempt+1, c.config.MaxRetries)
			time.Sleep(waitTime)
		}

		serverURL, lastErr = c.GetUploadServer()
		if lastErr == nil {
			break
		}

		logger.Warnf("Failed to get upload server (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, lastErr)
	}

	if lastErr != nil {
		return nil, NewAPIError("获取上传服务器失败", lastErr.Error(), 500)
	}

	// 2. 执行文件上传
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			waitTime := c.calculateBackoff(attempt, isRateLimitError(lastErr))
			logger.Debugf("Retrying upload after %v (attempt %d/%d)", waitTime, attempt+1, c.config.MaxRetries)
			time.Sleep(waitTime)
		}

		result, lastErr := c.performStreamUpload(serverURL, filePath, filename)
		if lastErr == nil {
			logger.Infof("Upload successful for file: %s", filename)
			return result, nil
		}

		logger.Warnf("Upload failed (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, lastErr)
	}

	if lastErr != nil {
		return nil, NewAPIError("上传失败", lastErr.Error(), 500)
	}
	return nil, NewAPIError("上传失败", "unknown error", 500)
}

// UploadData 上传数据
func (c *Client) UploadData(data []byte, filename string) (*UploadResult, error) {
	// 1. 获取上传服务器URL
	var serverURL string
	var lastErr error

	// 重试获取上传服务器URL
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			waitTime := c.calculateBackoff(attempt, isRateLimitError(lastErr))
			logger.Debugf("Retrying get upload server after %v (attempt %d/%d)", waitTime, attempt+1, c.config.MaxRetries)
			time.Sleep(waitTime)
		}

		url, err := c.GetUploadServer()
		if err == nil {
			serverURL = url
			break
		}

		lastErr = err
		if !isRetryableError(err) {
			return nil, fmt.Errorf("failed to get upload server: %w", err)
		}

		logger.Warnf("Get upload server attempt %d failed: %v", attempt+1, err)
	}

	if serverURL == "" {
		if lastErr != nil {
			// 如果最后一个错误是DoodStreamError，直接返回
			if dErr, ok := lastErr.(*DoodStreamError); ok {
				return nil, dErr
			}
		}
		return nil, NewServerError(
			fmt.Sprintf("获取上传服务器失败，已重试%d次", c.config.MaxRetries),
			http.StatusServiceUnavailable,
			lastErr)
	}

	// 2. 执行上传请求
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			waitTime := c.calculateBackoff(attempt, isRateLimitError(lastErr))
			logger.Debugf("Retrying upload after %v (attempt %d/%d)", waitTime, attempt+1, c.config.MaxRetries)
			time.Sleep(waitTime)
		}

		result, err := c.doUpload(serverURL, data, filename)
		if err == nil {
			return result, nil
		}

		lastErr = err

		// 如果不是可重试错误，直接返回
		if !isRetryableError(err) {
			return nil, fmt.Errorf("upload failed: %w", err)
		}

		logger.Warnf("Upload attempt %d failed: %v", attempt+1, err)
	}

	return &UploadResult{
		Success: false,
		Error:   fmt.Sprintf("upload failed after %d attempts: %v", c.config.MaxRetries, lastErr),
	}, lastErr
}

// doUpload 执行上传请求
func (c *Client) doUpload(serverURL string, data []byte, filename string) (*UploadResult, error) {
	// 应用速率限制
	doodStreamRateLimit()

	// 创建表单数据
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加API密钥
	if err := writer.WriteField("api_key", c.config.APIKey); err != nil {
		return nil, fmt.Errorf("failed to write api_key field: %w", err)
	}

	// 添加文件数据
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := part.Write(data); err != nil {
		return nil, fmt.Errorf("failed to write file data: %w", err)
	}

	writer.Close()

	// 创建请求
	req, err := http.NewRequest("POST", serverURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("User-Agent", "magnet-downloader/1.0")

	// 执行请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response UploadResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	if response.Status != 200 {
		return nil, fmt.Errorf("upload failed: %s", response.Message)
	}

	if len(response.Result) == 0 {
		return nil, fmt.Errorf("no upload result returned")
	}

	// 转换为统一格式
	result := response.Result[0]
	uploadResult := &UploadResult{
		Success:  true,
		URL:      result.DownloadURL,
		PlayURL:  result.ProtectedEmbed,
		FileCode: result.FileCode,
		Title:    result.Title,
		CanPlay:  result.CanPlay == 1,
	}

	// 解析文件大小
	if result.Size != "" {
		if size, err := parseSize(result.Size); err == nil {
			uploadResult.Size = size
		}
	}

	logger.Infof("Successfully uploaded file: %s (code: %s, size: %d)", filename, result.FileCode, uploadResult.Size)
	return uploadResult, nil
} // TestConnection 测试连接
func (c *Client) TestConnection() error {
	if c.config.APIKey == "" {
		return fmt.Errorf("API key is required")
	}

	// 尝试获取上传服务器URL来测试连接
	_, err := c.GetUploadServer()
	if err != nil {
		return fmt.Errorf("connection test failed: %w", err)
	}

	logger.Info("DoodStream connection test successful")
	return nil
}

// parseSize 解析文件大小字符串
func parseSize(sizeStr string) (int64, error) {
	if sizeStr == "" {
		return 0, fmt.Errorf("empty size string")
	}

	// DoodStream返回的size是字节数的字符串
	var size int64
	if _, err := fmt.Sscanf(sizeStr, "%d", &size); err != nil {
		return 0, fmt.Errorf("failed to parse size: %w", err)
	}

	return size, nil
}

// GetConfig 获取配置（用于调试）
func (c *Client) GetConfig() *Config {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 返回配置副本，隐藏API密钥
	return &Config{
		APIKey:     "***", // 隐藏API密钥
		BaseURL:    c.config.BaseURL,
		Timeout:    c.config.Timeout,
		MaxRetries: c.config.MaxRetries,
	}
}

// SetAPIKey 设置API密钥
func (c *Client) SetAPIKey(apiKey string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.config.APIKey = apiKey
}

// GetAPIKey 获取API密钥（用于内部使用）
func (c *Client) GetAPIKey() string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.config.APIKey
}

// doodStreamRateLimit DoodStream速率限制（可配置）
func doodStreamRateLimit() {
	doodStreamMutex.Lock()
	defer doodStreamMutex.Unlock()

	timeSinceLastRequest := time.Since(lastDoodStreamRequest)

	// 动态调整速率限制：
	// - 高带宽环境下使用更激进的速率限制
	// - 基础间隔50ms（20请求/秒），适合2.5G带宽
	minInterval := 50 * time.Millisecond

	// 根据并发情况动态调整
	// 如果有多个并发请求，稍微放宽限制
	activeRequests := getActiveRequestCount()
	if activeRequests > 5 {
		minInterval = 30 * time.Millisecond // 33请求/秒
	} else if activeRequests > 10 {
		minInterval = 20 * time.Millisecond // 50请求/秒
	}

	if timeSinceLastRequest < minInterval {
		waitTime := minInterval - timeSinceLastRequest
		logger.Debugf("DoodStream rate limiting: waiting %v (active: %d)", waitTime, activeRequests)
		time.Sleep(waitTime)
	}

	lastDoodStreamRequest = time.Now()
}

// getActiveRequestCount 获取当前活跃请求数（简化实现）
func getActiveRequestCount() int {
	// 这里可以实现更复杂的活跃请求计数
	// 暂时返回固定值，实际应用中可以通过全局计数器实现
	return 1
}

// isRateLimitError 检查是否为速率限制错误
func isRateLimitError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())
	return strings.Contains(errStr, "too many requests") ||
		strings.Contains(errStr, "rate limit") ||
		strings.Contains(errStr, "429")
}

// isRetryableError 检查是否为可重试错误
func isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// 优先使用新的错误处理机制
	if IsRetryableError(err) {
		return true
	}

	// 向后兼容：检查传统错误字符串
	errStr := strings.ToLower(err.Error())
	return strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "connection") ||
		strings.Contains(errStr, "network") ||
		strings.Contains(errStr, "500") ||
		strings.Contains(errStr, "502") ||
		strings.Contains(errStr, "503") ||
		strings.Contains(errStr, "504") ||
		isRateLimitError(err)
}

// calculateBackoff 计算智能退避时间
func (c *Client) calculateBackoff(attempt int, isRateLimit bool) time.Duration {
	if isRateLimit {
		// 速率限制错误：使用较长的固定等待时间
		return time.Duration(10+attempt*5) * time.Second // 10s, 15s, 20s...
	} else {
		// 其他错误：使用指数退避
		backoff := time.Duration(attempt*attempt) * time.Second
		// 最大等待时间不超过30秒
		if backoff > 30*time.Second {
			backoff = 30 * time.Second
		}
		return backoff
	}
}

// performStreamUpload 执行流式上传（内存优化版本）
func (c *Client) performStreamUpload(serverURL, filePath, filename string) (*UploadResult, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "打开文件失败", err)
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "获取文件信息失败", err)
	}

	// 应用速率限制
	doodStreamRateLimit()

	// 在URL中添加API密钥
	uploadURL := fmt.Sprintf("%s?%s", serverURL, c.config.APIKey)

	logger.Infof("Uploading file: %s (size: %.2f MB) to %s", filename, float64(fileInfo.Size())/(1024*1024), uploadURL)

	// 使用传统上传方式（内存优化）
	return c.performTraditionalUpload(uploadURL, file, filename, fileInfo.Size())
}

// performStreamUploadWithPipe 使用管道进行真正的流式上传
func (c *Client) performStreamUploadWithPipe(uploadURL string, file *os.File, filename string, fileSize int64) (*UploadResult, error) {
	// 创建管道用于流式传输
	pipeReader, pipeWriter := io.Pipe()

	// 创建multipart writer，写入管道
	writer := multipart.NewWriter(pipeWriter)
	
	// 获取Content-Type（在goroutine启动前）
	contentType := writer.FormDataContentType()

	// 在goroutine中写入数据
	go func() {
		defer func() {
			writer.Close()
			pipeWriter.Close()
		}()

		// 添加API密钥字段（必须在表单中传递）
		if err := writer.WriteField("api_key", c.config.APIKey); err != nil {
			logger.Errorf("Failed to write api_key field: %v", err)
			pipeWriter.CloseWithError(err)
			return
		}

		// 创建文件字段
		part, err := writer.CreateFormFile("file", filename)
		if err != nil {
			logger.Errorf("Failed to create form file: %v", err)
			pipeWriter.CloseWithError(err)
			return
		}

		// 流式复制文件内容（分块读取，避免大内存占用）
		buffer := make([]byte, 32*1024) // 32KB缓冲区
		for {
			n, err := file.Read(buffer)
			if n > 0 {
				if _, writeErr := part.Write(buffer[:n]); writeErr != nil {
					logger.Errorf("Failed to write file chunk: %v", writeErr)
					pipeWriter.CloseWithError(writeErr)
					return
				}
			}
			if err == io.EOF {
				break
			}
			if err != nil {
				logger.Errorf("Failed to read file chunk: %v", err)
				pipeWriter.CloseWithError(err)
				return
			}
		}
	}()

	// 创建HTTP请求（不在URL中包含API密钥，因为在表单中传递）
	req, err := http.NewRequest("POST", uploadURL, pipeReader)
	if err != nil {
		pipeReader.Close()
		return nil, NewAPIError("创建上传请求失败", err.Error(), 500)
	}

	req.Header.Set("Content-Type", contentType)
	req.Header.Set("User-Agent", "magnet-downloader/1.0")
	// 注意：不设置ContentLength，让HTTP客户端使用chunked传输

	// 执行请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		pipeReader.Close()
		return nil, NewAPIError("上传请求失败", err.Error(), 500)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, NewAPIError("读取上传响应失败", err.Error(), 500)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, NewAPIError(
			fmt.Sprintf("上传失败，HTTP状态码: %d", resp.StatusCode),
			fmt.Sprintf("response: %s", string(respBody)),
			resp.StatusCode)
	}

	// 解析响应
	var uploadResp UploadResponse
	if err := json.Unmarshal(respBody, &uploadResp); err != nil {
		return nil, NewAPIError("解析上传响应失败", err.Error(), 500)
	}

	// 检查API响应状态
	if uploadResp.Status != 200 {
		return nil, NewAPIError(
			fmt.Sprintf("上传失败，API状态码: %d, 消息: %s", uploadResp.Status, uploadResp.Message),
			"API error",
			uploadResp.Status)
	}

	if len(uploadResp.Result) == 0 {
		return nil, NewAPIError("上传响应中缺少结果数据", "missing result in response", 500)
	}

	// 取第一个结果
	result := uploadResp.Result[0]

	// 解析文件大小
	var parsedFileSize int64
	if result.Size != "" {
		fmt.Sscanf(result.Size, "%d", &parsedFileSize)
	}

	// 构造UploadResult
	uploadResult := &UploadResult{
		Success:  true,
		URL:      result.DownloadURL,
		PlayURL:  fmt.Sprintf("https://doodstream.com/e/%s", result.FileCode),
		FileCode: result.FileCode,
		Size:     parsedFileSize,
		Title:    result.Title,
		CanPlay:  result.CanPlay > 0,
	}

	logger.Infof("Upload completed successfully: %s -> %s", filename, uploadResult.PlayURL)

	return uploadResult, nil
}
