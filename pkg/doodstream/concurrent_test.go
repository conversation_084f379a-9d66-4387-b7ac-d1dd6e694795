package doodstream

import (
	"sync"
	"testing"
	"time"
)

func TestConcurrentRateLimit(t *testing.T) {
	// 重置全局变量
	lastDoodStreamRequest = time.Now().Add(-time.Second)
	
	const numGoroutines = 10
	const callsPerGoroutine = 5
	
	var wg sync.WaitGroup
	start := time.Now()
	
	// 启动多个goroutine并发调用速率限制
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < callsPerGoroutine; j++ {
				doodStreamRateLimit()
			}
		}(i)
	}
	
	wg.Wait()
	elapsed := time.Since(start)
	
	// 总共50次调用，按10请求/秒的速率，应该需要大约5秒
	// 考虑到并发和测试环境的不确定性，允许一定的误差
	expectedMin := 4 * time.Second
	expectedMax := 7 * time.Second
	
	if elapsed < expectedMin {
		t.Errorf("Concurrent rate limiting too fast: %v (expected at least %v)", elapsed, expectedMin)
	}
	
	if elapsed > expectedMax {
		t.<PERSON>rrorf("Concurrent rate limiting too slow: %v (expected at most %v)", elapsed, expectedMax)
	}
	
	t.Logf("Concurrent rate limiting test completed in %v", elapsed)
}

func TestRateLimitPrecision(t *testing.T) {
	// 重置全局变量
	lastDoodStreamRequest = time.Now().Add(-time.Second)
	
	const numCalls = 20
	times := make([]time.Time, numCalls)
	
	// 记录每次调用的时间
	for i := 0; i < numCalls; i++ {
		doodStreamRateLimit()
		times[i] = time.Now()
	}
	
	// 检查相邻调用之间的间隔
	for i := 1; i < numCalls; i++ {
		interval := times[i].Sub(times[i-1])
		
		// 间隔应该接近100ms（允许±20ms的误差）
		if interval < 80*time.Millisecond || interval > 120*time.Millisecond {
			t.Errorf("Call %d interval: %v (expected ~100ms)", i, interval)
		}
	}
	
	t.Logf("Rate limit precision test: %d calls completed", numCalls)
}

func BenchmarkRateLimit(b *testing.B) {
	// 重置全局变量
	lastDoodStreamRequest = time.Now().Add(-time.Second)
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		doodStreamRateLimit()
	}
}