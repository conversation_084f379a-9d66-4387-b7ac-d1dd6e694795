package doodstream

import (
	"testing"
	"time"
)

func TestDoodStreamRateLimit(t *testing.T) {
	// 重置全局变量
	lastDoodStreamRequest = time.Now().Add(-time.Second)
	
	// 测试速率限制
	start := time.Now()
	
	// 第一次调用应该立即返回
	doodStreamRateLimit()
	elapsed1 := time.Since(start)
	
	// 第二次调用应该等待至少100ms
	start2 := time.Now()
	doodStreamRateLimit()
	elapsed2 := time.Since(start2)
	
	if elapsed1 > 50*time.Millisecond {
		t.<PERSON><PERSON><PERSON>("First call should be immediate, but took %v", elapsed1)
	}
	
	if elapsed2 < 90*time.Millisecond {
		t.<PERSON><PERSON><PERSON>("Second call should wait ~100ms, but only took %v", elapsed2)
	}
	
	if elapsed2 > 150*time.Millisecond {
		t.<PERSON>rrorf("Second call should wait ~100ms, but took too long: %v", elapsed2)
	}
}

func TestIsRateLimitError(t *testing.T) {
	tests := []struct {
		err      string
		expected bool
	}{
		{"Too Many Requests", true},
		{"rate limit exceeded", true},
		{"HTTP 429", true},
		{"connection timeout", false},
		{"network error", false},
		{"", false},
	}
	
	for _, test := range tests {
		var err error
		if test.err != "" {
			err = &testError{test.err}
		}
		
		result := isRateLimitError(err)
		if result != test.expected {
			t.Errorf("isRateLimitError(%q) = %v, expected %v", test.err, result, test.expected)
		}
	}
}

func TestIsRetryableError(t *testing.T) {
	tests := []struct {
		err      string
		expected bool
	}{
		{"Too Many Requests", true},
		{"connection timeout", true},
		{"network error", true},
		{"HTTP 500", true},
		{"HTTP 502", true},
		{"HTTP 503", true},
		{"HTTP 504", true},
		{"invalid API key", false},
		{"file not found", false},
		{"", false},
	}
	
	for _, test := range tests {
		var err error
		if test.err != "" {
			err = &testError{test.err}
		}
		
		result := isRetryableError(err)
		if result != test.expected {
			t.Errorf("isRetryableError(%q) = %v, expected %v", test.err, result, test.expected)
		}
	}
}

func TestCalculateBackoff(t *testing.T) {
	client := NewClient(&Config{MaxRetries: 3})
	
	// 测试速率限制错误的退避时间
	backoff1 := client.calculateBackoff(1, true)
	expected1 := 15 * time.Second // 10 + 1*5
	if backoff1 != expected1 {
		t.Errorf("Rate limit backoff for attempt 1: got %v, expected %v", backoff1, expected1)
	}
	
	// 测试普通错误的指数退避
	backoff2 := client.calculateBackoff(2, false)
	expected2 := 4 * time.Second // 2*2
	if backoff2 != expected2 {
		t.Errorf("Normal backoff for attempt 2: got %v, expected %v", backoff2, expected2)
	}
	
	// 测试最大退避时间限制
	backoff3 := client.calculateBackoff(10, false)
	expected3 := 30 * time.Second // 最大30秒
	if backoff3 != expected3 {
		t.Errorf("Max backoff: got %v, expected %v", backoff3, expected3)
	}
}

// testError 用于测试的错误类型
type testError struct {
	msg string
}

func (e *testError) Error() string {
	return e.msg
}