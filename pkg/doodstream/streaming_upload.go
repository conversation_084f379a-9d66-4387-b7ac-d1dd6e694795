package doodstream

import (
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"

	"magnet-downloader/pkg/logger"
)

// StreamingUploadWriter 实现真正的流式上传
type StreamingUploadWriter struct {
	file     *os.File
	filename string
	apiKey   string
	boundary string
	
	// 预计算的头部和尾部
	header []byte
	footer []byte
	
	// 当前读取状态
	headerSent bool
	fileSent   bool
	footerSent bool
	
	// 文件读取缓冲区
	buffer []byte
}

// NewStreamingUploadWriter 创建流式上传写入器
func NewStreamingUploadWriter(file *os.File, filename, apiKey string) *StreamingUploadWriter {
	writer := &StreamingUploadWriter{
		file:     file,
		filename: filename,
		apiKey:   apiKey,
		boundary: "----formdata-go-" + fmt.Sprintf("%d", file.Fd()),
		buffer:   make([]byte, 64*1024), // 64KB缓冲区
	}
	
	// 预计算multipart头部
	writer.header = []byte(fmt.Sprintf(
		"--%s\r\nContent-Disposition: form-data; name=\"api_key\"\r\n\r\n%s\r\n--%s\r\nContent-Disposition: form-data; name=\"file\"; filename=\"%s\"\r\nContent-Type: application/octet-stream\r\n\r\n",
		writer.boundary, apiKey, writer.boundary, filename))
	
	// 预计算multipart尾部
	writer.footer = []byte(fmt.Sprintf("\r\n--%s--\r\n", writer.boundary))
	
	return writer
}

// Read 实现 io.Reader 接口，提供流式数据
func (w *StreamingUploadWriter) Read(p []byte) (n int, err error) {
	// 1. 先发送头部
	if !w.headerSent {
		n = copy(p, w.header)
		if n < len(w.header) {
			// 缓冲区太小，只能发送部分头部
			w.header = w.header[n:]
			return n, nil
		}
		w.headerSent = true
		w.header = nil // 释放内存
		
		// 如果还有空间，继续读取文件
		if n < len(p) {
			fileN, fileErr := w.readFile(p[n:])
			return n + fileN, fileErr
		}
		return n, nil
	}
	
	// 2. 发送文件内容
	if !w.fileSent {
		return w.readFile(p)
	}
	
	// 3. 发送尾部
	if !w.footerSent {
		n = copy(p, w.footer)
		if n < len(w.footer) {
			w.footer = w.footer[n:]
			return n, nil
		}
		w.footerSent = true
		return n, io.EOF
	}
	
	return 0, io.EOF
}

// readFile 从文件读取数据
func (w *StreamingUploadWriter) readFile(p []byte) (n int, err error) {
	n, err = w.file.Read(p)
	if err == io.EOF {
		w.fileSent = true
	}
	return n, err
}

// ContentType 返回Content-Type
func (w *StreamingUploadWriter) ContentType() string {
	return "multipart/form-data; boundary=" + w.boundary
}

// performTrueStreamUpload 执行真正的流式上传
func (c *Client) performTrueStreamUpload(uploadURL string, file *os.File, filename string, fileSize int64) (*UploadResult, error) {
	// 创建流式上传写入器
	streamWriter := NewStreamingUploadWriter(file, filename, c.config.APIKey)
	
	logger.Infof("Starting true streaming upload for: %s (%.2f MB)", filename, float64(fileSize)/(1024*1024))
	
	// 创建HTTP请求
	req, err := http.NewRequest("POST", uploadURL, streamWriter)
	if err != nil {
		return nil, NewAPIError("创建上传请求失败", err.Error(), 500)
	}
	
	req.Header.Set("Content-Type", streamWriter.ContentType())
	req.Header.Set("User-Agent", "magnet-downloader/1.0")
	// 不设置Content-Length，让HTTP客户端使用chunked传输
	
	// 执行请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, NewAPIError("上传请求失败", err.Error(), 500)
	}
	defer resp.Body.Close()
	
	// 处理响应（与之前相同）
	return c.handleUploadResponse(resp, filename)
}

// handleUploadResponse 处理上传响应
func (c *Client) handleUploadResponse(resp *http.Response, filename string) (*UploadResult, error) {
	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, NewAPIError("读取上传响应失败", err.Error(), 500)
	}
	
	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, NewAPIError(
			fmt.Sprintf("上传失败，HTTP状态码: %d", resp.StatusCode), 
			fmt.Sprintf("response: %s", string(respBody)),
			resp.StatusCode)
	}
	
	// 解析响应（使用现有的解析逻辑）
	var uploadResp UploadResponse
	if err := json.Unmarshal(respBody, &uploadResp); err != nil {
		return nil, NewAPIError("解析上传响应失败", err.Error(), 500)
	}
	
	// 检查API响应状态
	if uploadResp.Status != 200 {
		return nil, NewAPIError(
			fmt.Sprintf("上传失败，API状态码: %d, 消息: %s", uploadResp.Status, uploadResp.Message), 
			"API error",
			uploadResp.Status)
	}
	
	if len(uploadResp.Result) == 0 {
		return nil, NewAPIError("上传响应中缺少结果数据", "missing result in response", 500)
	}
	
	// 取第一个结果
	result := uploadResp.Result[0]
	
	// 构造UploadResult
	uploadResult := &UploadResult{
		Success:  true,
		URL:      result.DownloadURL,
		PlayURL:  fmt.Sprintf("https://doodstream.com/e/%s", result.FileCode),
		FileCode: result.FileCode,
		Size:     result.Size,
		Title:    result.Title,
		CanPlay:  result.CanPlay > 0,
	}
	
	logger.Infof("True streaming upload completed: %s -> %s", filename, uploadResult.PlayURL)
	
	return uploadResult, nil
}