package doodstream

import (
	"testing"
	"time"
)

func TestNewClient(t *testing.T) {
	// 测试默认配置
	client := NewClient(nil)
	if client == nil {
		t.Fatal("NewClient returned nil")
	}

	config := client.GetConfig()
	if config.BaseURL != "https://doodapi.co" {
		t.<PERSON><PERSON>("Expected BaseURL to be https://doodapi.co, got %s", config.BaseURL)
	}

	if config.Timeout != 300*time.Second {
		t.Errorf("Expected Timeout to be 300s, got %v", config.Timeout)
	}

	if config.MaxRetries != 3 {
		t.<PERSON><PERSON><PERSON>("Expected MaxRetries to be 3, got %d", config.MaxRetries)
	}
}

func TestNewClientWithConfig(t *testing.T) {
	config := &Config{
		APIKey:     "test-key",
		BaseURL:    "https://test.example.com",
		Timeout:    60 * time.Second,
		MaxRetries: 5,
	}

	client := NewClient(config)
	if client == nil {
		t.Fatal("NewClient returned nil")
	}

	clientConfig := client.GetConfig()
	if clientConfig.BaseURL != config.BaseURL {
		t.Errorf("Expected BaseURL to be %s, got %s", config.BaseURL, clientConfig.BaseURL)
	}

	if clientConfig.Timeout != config.Timeout {
		t.Errorf("Expected Timeout to be %v, got %v", config.Timeout, clientConfig.Timeout)
	}

	if clientConfig.MaxRetries != config.MaxRetries {
		t.Errorf("Expected MaxRetries to be %d, got %d", config.MaxRetries, clientConfig.MaxRetries)
	}
}

func TestSetAPIKey(t *testing.T) {
	client := NewClient(nil)
	testKey := "test-api-key"
	
	client.SetAPIKey(testKey)
	
	if client.GetAPIKey() != testKey {
		t.Errorf("Expected API key to be %s, got %s", testKey, client.GetAPIKey())
	}
}

func TestParseSize(t *testing.T) {
	tests := []struct {
		input    string
		expected int64
		hasError bool
	}{
		{"123456", 123456, false},
		{"0", 0, false},
		{"", 0, true},
		{"abc", 0, true},
	}

	for _, test := range tests {
		result, err := parseSize(test.input)
		if test.hasError {
			if err == nil {
				t.Errorf("Expected error for input %s, but got none", test.input)
			}
		} else {
			if err != nil {
				t.Errorf("Unexpected error for input %s: %v", test.input, err)
			}
			if result != test.expected {
				t.Errorf("Expected %d for input %s, got %d", test.expected, test.input, result)
			}
		}
	}
}