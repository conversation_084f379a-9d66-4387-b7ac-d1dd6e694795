package doodstream

import (
	"fmt"
	"net/http"
)

// ErrorType 错误类型枚举
type ErrorType string

const (
	// 网络相关错误
	ErrorTypeNetwork     ErrorType = "network_error"
	ErrorTypeTimeout     ErrorType = "timeout_error"
	ErrorTypeConnection  ErrorType = "connection_error"
	
	// API相关错误
	ErrorTypeAPI         ErrorType = "api_error"
	ErrorTypeAuth        ErrorType = "auth_error"
	ErrorTypeRateLimit   ErrorType = "rate_limit_error"
	ErrorTypeQuota       ErrorType = "quota_error"
	
	// 文件相关错误
	ErrorTypeFile        ErrorType = "file_error"
	ErrorTypeFileSize    ErrorType = "file_size_error"
	ErrorTypeFileFormat  ErrorType = "file_format_error"
	ErrorTypeFileAccess  ErrorType = "file_access_error"
	
	// 上传相关错误
	ErrorTypeUpload      ErrorType = "upload_error"
	ErrorTypeUploadServer ErrorType = "upload_server_error"
	
	// 配置相关错误
	ErrorTypeConfig      ErrorType = "config_error"
	ErrorTypeValidation  ErrorType = "validation_error"
	
	// 服务器相关错误
	ErrorTypeServer      ErrorType = "server_error"
	ErrorTypeInternal    ErrorType = "internal_error"
	ErrorTypeUnknown     ErrorType = "unknown_error"
)

// DoodStreamError DoodStream专用错误类型
type DoodStreamError struct {
	Type         ErrorType `json:"type"`
	Message      string    `json:"message"`
	Details      string    `json:"details,omitempty"`
	UserMessage  string    `json:"user_message,omitempty"`
	HTTPStatus   int       `json:"http_status,omitempty"`
	RetryAfter   int       `json:"retry_after,omitempty"`
	Retryable    bool      `json:"retryable"`
	OriginalErr  error     `json:"-"`
}

// Error 实现error接口
func (e *DoodStreamError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Type, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s] %s", e.Type, e.Message)
}

// Unwrap 支持错误链
func (e *DoodStreamError) Unwrap() error {
	return e.OriginalErr
}

// IsRetryable 检查错误是否可重试
func (e *DoodStreamError) IsRetryable() bool {
	return e.Retryable
}

// GetUserMessage 获取用户友好的错误信息
func (e *DoodStreamError) GetUserMessage() string {
	if e.UserMessage != "" {
		return e.UserMessage
	}
	
	// 根据错误类型返回默认的用户友好信息
	switch e.Type {
	case ErrorTypeNetwork:
		return "网络连接异常，请检查网络设置"
	case ErrorTypeTimeout:
		return "上传超时，请稍后重试"
	case ErrorTypeConnection:
		return "无法连接到DoodStream服务，请检查网络"
	case ErrorTypeAuth:
		return "API密钥无效，请检查配置"
	case ErrorTypeRateLimit:
		return "请求过于频繁，请稍后重试"
	case ErrorTypeQuota:
		return "存储配额不足，请联系管理员"
	case ErrorTypeFileSize:
		return "文件大小超出限制"
	case ErrorTypeFileFormat:
		return "不支持的文件格式"
	case ErrorTypeFileAccess:
		return "无法访问文件，请检查文件路径和权限"
	case ErrorTypeConfig:
		return "配置错误，请检查设置"
	case ErrorTypeValidation:
		return "参数验证失败，请检查输入"
	case ErrorTypeServer:
		return "服务器错误，请稍后重试"
	default:
		return "上传失败，请稍后重试"
	}
}

// 错误构造函数

// NewNetworkError 创建网络错误
func NewNetworkError(message string, err error) *DoodStreamError {
	return &DoodStreamError{
		Type:        ErrorTypeNetwork,
		Message:     message,
		Details:     err.Error(),
		Retryable:   true,
		OriginalErr: err,
	}
}

// NewTimeoutError 创建超时错误
func NewTimeoutError(message string, err error) *DoodStreamError {
	return &DoodStreamError{
		Type:        ErrorTypeTimeout,
		Message:     message,
		Details:     err.Error(),
		Retryable:   true,
		OriginalErr: err,
	}
}

// NewAuthError 创建认证错误
func NewAuthError(message string) *DoodStreamError {
	return &DoodStreamError{
		Type:        ErrorTypeAuth,
		Message:     message,
		UserMessage: "API密钥无效，请检查DoodStream配置",
		HTTPStatus:  http.StatusUnauthorized,
		Retryable:   false,
	}
}

// NewRateLimitError 创建速率限制错误
func NewRateLimitError(message string, retryAfter int) *DoodStreamError {
	return &DoodStreamError{
		Type:        ErrorTypeRateLimit,
		Message:     message,
		UserMessage: fmt.Sprintf("请求过于频繁，请等待%d秒后重试", retryAfter),
		HTTPStatus:  http.StatusTooManyRequests,
		RetryAfter:  retryAfter,
		Retryable:   true,
	}
}

// NewFileError 创建文件错误
func NewFileError(errorType ErrorType, message string, err error) *DoodStreamError {
	return &DoodStreamError{
		Type:        errorType,
		Message:     message,
		Details:     err.Error(),
		Retryable:   false,
		OriginalErr: err,
	}
}

// NewValidationError 创建验证错误
func NewValidationError(message, details string) *DoodStreamError {
	return &DoodStreamError{
		Type:        ErrorTypeValidation,
		Message:     message,
		Details:     details,
		UserMessage: "参数验证失败，请检查输入",
		HTTPStatus:  http.StatusBadRequest,
		Retryable:   false,
	}
}

// NewServerError 创建服务器错误
func NewServerError(message string, httpStatus int, err error) *DoodStreamError {
	return &DoodStreamError{
		Type:        ErrorTypeServer,
		Message:     message,
		Details:     err.Error(),
		HTTPStatus:  httpStatus,
		Retryable:   httpStatus >= 500, // 5xx错误可重试
		OriginalErr: err,
	}
}

// NewAPIError 创建API错误
func NewAPIError(message, details string, httpStatus int) *DoodStreamError {
	return &DoodStreamError{
		Type:       ErrorTypeAPI,
		Message:    message,
		Details:    details,
		HTTPStatus: httpStatus,
		Retryable:  httpStatus >= 500,
	}
}

// 错误检查辅助函数

// IsNetworkError 检查是否为网络错误
func IsNetworkError(err error) bool {
	if dErr, ok := err.(*DoodStreamError); ok {
		return dErr.Type == ErrorTypeNetwork || dErr.Type == ErrorTypeConnection
	}
	return false
}

// IsTimeoutError 检查是否为超时错误
func IsTimeoutError(err error) bool {
	if dErr, ok := err.(*DoodStreamError); ok {
		return dErr.Type == ErrorTypeTimeout
	}
	return false
}

// IsAuthError 检查是否为认证错误
func IsAuthError(err error) bool {
	if dErr, ok := err.(*DoodStreamError); ok {
		return dErr.Type == ErrorTypeAuth
	}
	return false
}

// IsRateLimitError 检查是否为速率限制错误
func IsRateLimitError(err error) bool {
	if dErr, ok := err.(*DoodStreamError); ok {
		return dErr.Type == ErrorTypeRateLimit
	}
	return false
}

// IsFileError 检查是否为文件相关错误
func IsFileError(err error) bool {
	if dErr, ok := err.(*DoodStreamError); ok {
		return dErr.Type == ErrorTypeFile || 
			   dErr.Type == ErrorTypeFileSize || 
			   dErr.Type == ErrorTypeFileFormat || 
			   dErr.Type == ErrorTypeFileAccess
	}
	return false
}

// IsRetryableError 检查错误是否可重试
func IsRetryableError(err error) bool {
	if dErr, ok := err.(*DoodStreamError); ok {
		return dErr.Retryable
	}
	// 对于非DoodStreamError，采用保守策略
	return false
}

// GetRetryAfter 获取重试等待时间
func GetRetryAfter(err error) int {
	if dErr, ok := err.(*DoodStreamError); ok {
		return dErr.RetryAfter
	}
	return 0
}

// FormatErrorForUser 格式化错误信息供用户显示
func FormatErrorForUser(err error) string {
	if dErr, ok := err.(*DoodStreamError); ok {
		return dErr.GetUserMessage()
	}
	return "操作失败，请稍后重试"
}

// FormatErrorForDeveloper 格式化错误信息供开发者调试
func FormatErrorForDeveloper(err error) string {
	if dErr, ok := err.(*DoodStreamError); ok {
		return dErr.Error()
	}
	return err.Error()
}