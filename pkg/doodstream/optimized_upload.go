package doodstream

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"

	"magnet-downloader/pkg/logger"
)

// performOptimizedTraditionalUpload 执行优化的传统上传（针对大文件）
func (c *Client) performOptimizedTraditionalUpload(uploadURL string, file *os.File, filename string, fileSize int64) (*UploadResult, error) {
	// 创建表单数据
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加API密钥字段
	if err := writer.WriteField("api_key", c.config.APIKey); err != nil {
		return nil, NewAPIError("添加API密钥字段失败", err.Error(), 500)
	}

	// 创建文件字段
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return nil, NewAPIError("创建表单字段失败", err.Error(), 500)
	}

	// 使用更大的缓冲区进行分块复制（1MB缓冲区，减少系统调用）
	buffer := make([]byte, 1024*1024) // 1MB缓冲区
	totalRead := int64(0)
	
	logger.Infof("Starting optimized upload for large file: %s (%.2f GB)", filename, float64(fileSize)/(1024*1024*1024))
	
	for {
		n, err := file.Read(buffer)
		if n > 0 {
			if _, writeErr := part.Write(buffer[:n]); writeErr != nil {
				return nil, NewFileError(ErrorTypeFileAccess, "写入文件数据失败", writeErr)
			}
			totalRead += int64(n)
			
			// 每100MB记录一次进度
			if totalRead%(100*1024*1024) == 0 {
				progress := float64(totalRead) / float64(fileSize) * 100
				logger.Debugf("Upload progress for %s: %.1f%% (%.2f MB / %.2f MB)", 
					filename, progress, float64(totalRead)/(1024*1024), float64(fileSize)/(1024*1024))
			}
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, NewFileError(ErrorTypeFileAccess, "读取文件失败", err)
		}
	}

	logger.Infof("File read completed for %s, total: %.2f MB", filename, float64(totalRead)/(1024*1024))

	// 关闭writer
	if err := writer.Close(); err != nil {
		return nil, NewAPIError("关闭multipart writer失败", err.Error(), 500)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", uploadURL, &buf)
	if err != nil {
		return nil, NewAPIError("创建上传请求失败", err.Error(), 500)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("User-Agent", "magnet-downloader/1.0")

	logger.Infof("Starting HTTP upload for %s (buffer size: %.2f MB)", filename, float64(buf.Len())/(1024*1024))

	// 执行请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, NewAPIError("上传请求失败", err.Error(), 500)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, NewAPIError("读取上传响应失败", err.Error(), 500)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, NewAPIError(
			fmt.Sprintf("上传失败，HTTP状态码: %d", resp.StatusCode), 
			fmt.Sprintf("response: %s", string(respBody)),
			resp.StatusCode)
	}

	// 解析响应
	var uploadResp UploadResponse
	if err := json.Unmarshal(respBody, &uploadResp); err != nil {
		return nil, NewAPIError("解析上传响应失败", err.Error(), 500)
	}

	// 检查API响应状态
	if uploadResp.Status != 200 {
		return nil, NewAPIError(
			fmt.Sprintf("上传失败，API状态码: %d, 消息: %s", uploadResp.Status, uploadResp.Message), 
			"API error",
			uploadResp.Status)
	}

	if len(uploadResp.Result) == 0 {
		return nil, NewAPIError("上传响应中缺少结果数据", "missing result in response", 500)
	}

	// 取第一个结果
	result := uploadResp.Result[0]
	
	// 解析文件大小
	var parsedFileSize int64
	if result.Size != "" {
		fmt.Sscanf(result.Size, "%d", &parsedFileSize)
	}

	// 构造UploadResult
	uploadResult := &UploadResult{
		Success:  true,
		URL:      result.DownloadURL,
		PlayURL:  fmt.Sprintf("https://doodstream.com/e/%s", result.FileCode),
		FileCode: result.FileCode,
		Size:     parsedFileSize,
		Title:    result.Title,
		CanPlay:  result.CanPlay > 0,
	}

	logger.Infof("Optimized upload completed successfully: %s -> %s", filename, uploadResult.PlayURL)

	return uploadResult, nil
}