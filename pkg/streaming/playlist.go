package streaming

import (
	"fmt"
	"strings"

	"magnet-downloader/pkg/logger"
)

// PlaylistConfig 播放列表配置
type PlaylistConfig struct {
	Version           int    `json:"version"`            // HLS版本
	TargetDuration    int    `json:"target_duration"`    // 目标时长(秒)
	MediaSequence     int    `json:"media_sequence"`     // 媒体序列号
	AllowCache        bool   `json:"allow_cache"`        // 是否允许缓存
	PlaylistType      string `json:"playlist_type"`      // 播放列表类型
	EncryptionEnabled bool   `json:"encryption_enabled"` // 是否启用加密
	EncryptionMethod  string `json:"encryption_method"`  // 加密方法
	EncryptionKeyURI  string `json:"encryption_key_uri"` // 加密密钥URI
	EncryptionIV      string `json:"encryption_iv"`      // 加密初始向量
}

// Segment 播放片段
type Segment struct {
	Duration float64 `json:"duration"` // 时长(秒)
	URI      string  `json:"uri"`      // 片段URI
	Title    string  `json:"title"`    // 片段标题
}

// Playlist HLS播放列表
type Playlist struct {
	config   *PlaylistConfig
	segments []Segment
}

// NewPlaylist 创建新的播放列表
func NewPlaylist(config *PlaylistConfig) *Playlist {
	if config == nil {
		config = &PlaylistConfig{
			Version:        3,
			TargetDuration: 10,
			MediaSequence:  0,
			AllowCache:     true,
			PlaylistType:   "VOD",
		}
	}

	return &Playlist{
		config:   config,
		segments: make([]Segment, 0),
	}
}

// AddSegment 添加片段
func (p *Playlist) AddSegment(segment Segment) {
	p.segments = append(p.segments, segment)
}

// AddSegments 批量添加片段
func (p *Playlist) AddSegments(segments []Segment) {
	p.segments = append(p.segments, segments...)
}

// GenerateFromURLs 从URL列表生成播放列表
func (p *Playlist) GenerateFromURLs(urls []string, segmentDuration float64) {
	p.segments = make([]Segment, 0, len(urls))

	for i, url := range urls {
		segment := Segment{
			Duration: segmentDuration,
			URI:      url,
			Title:    fmt.Sprintf("Segment %d", i+1),
		}
		p.segments = append(p.segments, segment)
	}

	logger.Infof("Generated playlist from URLs: segments=%d, duration=%.2f", len(urls), segmentDuration)
}

// Generate 生成M3U8播放列表内容
func (p *Playlist) Generate() string {
	var builder strings.Builder

	// 写入头部信息
	builder.WriteString("#EXTM3U\n")
	builder.WriteString(fmt.Sprintf("#EXT-X-VERSION:%d\n", p.config.Version))
	builder.WriteString(fmt.Sprintf("#EXT-X-TARGETDURATION:%d\n", p.config.TargetDuration))
	builder.WriteString(fmt.Sprintf("#EXT-X-MEDIA-SEQUENCE:%d\n", p.config.MediaSequence))

	// 播放列表类型
	if p.config.PlaylistType != "" {
		builder.WriteString(fmt.Sprintf("#EXT-X-PLAYLIST-TYPE:%s\n", p.config.PlaylistType))
	}

	// 缓存设置
	if p.config.AllowCache {
		builder.WriteString("#EXT-X-ALLOW-CACHE:YES\n")
	} else {
		builder.WriteString("#EXT-X-ALLOW-CACHE:NO\n")
	}

	// 加密信息
	if p.config.EncryptionEnabled {
		builder.WriteString("#EXT-X-KEY:")
		builder.WriteString(fmt.Sprintf("METHOD=%s", p.config.EncryptionMethod))

		if p.config.EncryptionKeyURI != "" {
			builder.WriteString(fmt.Sprintf(",URI=\"%s\"", p.config.EncryptionKeyURI))
		}

		if p.config.EncryptionIV != "" {
			builder.WriteString(fmt.Sprintf(",IV=%s", p.config.EncryptionIV))
		}

		builder.WriteString("\n")
	}

	// 写入片段信息
	for _, segment := range p.segments {
		// 片段时长
		builder.WriteString(fmt.Sprintf("#EXTINF:%.6f,", segment.Duration))

		// 片段标题
		if segment.Title != "" {
			builder.WriteString(segment.Title)
		}
		builder.WriteString("\n")

		// 片段URI
		builder.WriteString(segment.URI)
		builder.WriteString("\n")
	}

	// 结束标记
	builder.WriteString("#EXT-X-ENDLIST\n")

	return builder.String()
}

// Validate 验证播放列表
func (p *Playlist) Validate() error {
	if len(p.segments) == 0 {
		return fmt.Errorf("playlist has no segments")
	}

	if p.config.Version < 1 || p.config.Version > 7 {
		return fmt.Errorf("invalid HLS version: %d", p.config.Version)
	}

	if p.config.TargetDuration <= 0 {
		return fmt.Errorf("invalid target duration: %d", p.config.TargetDuration)
	}

	// 验证片段
	for i, segment := range p.segments {
		if segment.Duration <= 0 {
			return fmt.Errorf("invalid segment duration at index %d: %.2f", i, segment.Duration)
		}

		if segment.URI == "" {
			return fmt.Errorf("empty segment URI at index %d", i)
		}
	}

	// 验证加密配置
	if p.config.EncryptionEnabled {
		if p.config.EncryptionMethod == "" {
			return fmt.Errorf("encryption enabled but method not specified")
		}

		validMethods := []string{"AES-128", "AES-256", "SAMPLE-AES"}
		methodValid := false
		for _, method := range validMethods {
			if p.config.EncryptionMethod == method {
				methodValid = true
				break
			}
		}

		if !methodValid {
			return fmt.Errorf("invalid encryption method: %s", p.config.EncryptionMethod)
		}
	}

	return nil
}

// GetDuration 获取播放列表总时长
func (p *Playlist) GetDuration() float64 {
	var totalDuration float64
	for _, segment := range p.segments {
		totalDuration += segment.Duration
	}
	return totalDuration
}

// GetSegmentCount 获取片段数量
func (p *Playlist) GetSegmentCount() int {
	return len(p.segments)
}

// GetConfig 获取配置
func (p *Playlist) GetConfig() *PlaylistConfig {
	// 返回配置副本
	config := *p.config
	return &config
}

// UpdateConfig 更新配置
func (p *Playlist) UpdateConfig(config *PlaylistConfig) {
	p.config = config
}

// Clear 清空播放列表
func (p *Playlist) Clear() {
	p.segments = p.segments[:0]
}

// PlaylistGenerator 播放列表生成器
type PlaylistGenerator struct {
	defaultConfig *PlaylistConfig
}

// NewPlaylistGenerator 创建播放列表生成器
func NewPlaylistGenerator(config *PlaylistConfig) *PlaylistGenerator {
	if config == nil {
		config = &PlaylistConfig{
			Version:        3,
			TargetDuration: 10,
			MediaSequence:  0,
			AllowCache:     true,
			PlaylistType:   "VOD",
		}
	}

	return &PlaylistGenerator{
		defaultConfig: config,
	}
}

// GenerateFromChunks 从分片URL生成播放列表
func (pg *PlaylistGenerator) GenerateFromChunks(urls []string, chunkDuration float64, encryptionKey string) (string, error) {
	if len(urls) == 0 {
		return "", fmt.Errorf("no URLs provided")
	}

	config := *pg.defaultConfig // 复制默认配置

	// 如果提供了加密密钥，启用加密
	if encryptionKey != "" {
		config.EncryptionEnabled = true
		config.EncryptionMethod = "AES-256"
		config.EncryptionKeyURI = fmt.Sprintf("data:text/plain;base64,%s", encryptionKey)
	}

	playlist := NewPlaylist(&config)

	// 添加片段
	for i, url := range urls {
		segment := Segment{
			Duration: chunkDuration,
			URI:      url,
			Title:    fmt.Sprintf("Chunk %d", i+1),
		}
		playlist.AddSegment(segment)
	}

	// 验证播放列表
	if err := playlist.Validate(); err != nil {
		return "", fmt.Errorf("playlist validation failed: %w", err)
	}

	content := playlist.Generate()

	logger.Infof("Generated M3U8 playlist: segments=%d, duration=%.2f, encrypted=%t",
		len(urls), playlist.GetDuration(), config.EncryptionEnabled)

	return content, nil
}

// GenerateSimple 生成简单播放列表
func (pg *PlaylistGenerator) GenerateSimple(urls []string) (string, error) {
	return pg.GenerateFromChunks(urls, 10.0, "")
}

// SaveToFile 保存播放列表到文件
func (pg *PlaylistGenerator) SaveToFile(content, filepath string) error {
	// 这里可以使用文件系统操作保存文件
	// 为了保持包的独立性，这里只返回内容
	logger.Infof("Playlist content generated, length=%d", len(content))
	return nil
}
