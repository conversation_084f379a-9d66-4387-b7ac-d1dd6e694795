package steganography

import (
	"bytes"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"

	"magnet-downloader/pkg/logger"
)

// PNGGenerator PNG图片生成器
type PNGGenerator struct {
	config *Config
}

// NewPNGGenerator 创建PNG生成器
func NewPNGGenerator(config *Config) *PNGGenerator {
	if config == nil {
		config = DefaultConfig()
	}
	return &PNGGenerator{config: config}
}

// GenerateBlankPNG 生成空白PNG图片
func (g *PNGGenerator) GenerateBlankPNG(width, height int) ([]byte, error) {
	if width <= 0 || height <= 0 {
		return nil, fmt.Errorf("invalid dimensions: %dx%d", width, height)
	}

	// 创建空白图片
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 填充背景色（浅灰色，看起来更自然）
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			// 创建微妙的渐变效果
			r := uint8(200 + (x*y)%56)
			g := uint8(200 + (x+y)%56)
			b := uint8(200 + (x*2+y)%56)
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	// 编码为PNG
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		return nil, fmt.Errorf("failed to encode PNG: %w", err)
	}

	logger.Debugf("Generated blank PNG: %dx%d, size: %d bytes", width, height, buf.Len())
	return buf.Bytes(), nil
}

// GenerateOptimalSizePNG 根据数据大小生成最优尺寸的PNG
func (g *PNGGenerator) GenerateOptimalSizePNG(dataSize int) ([]byte, error) {
	width, height := g.calculateOptimalSize(dataSize)
	return g.GenerateBlankPNG(width, height)
}

// calculateOptimalSize 计算最优图片尺寸
func (g *PNGGenerator) calculateOptimalSize(dataSize int) (width, height int) {
	// 数据大小包括：4字节长度前缀 + 实际数据
	totalDataSize := 4 + dataSize

	// 每个像素可以隐藏的位数（RGB三个通道，每个通道1位）
	bitsPerPixel := 3
	if g.config.UseAlpha {
		bitsPerPixel = 4
	}

	// 需要的总位数
	totalBits := totalDataSize * 8

	// 需要的像素数
	requiredPixels := int(math.Ceil(float64(totalBits) / float64(bitsPerPixel)))

	// 计算接近正方形的尺寸
	sideLength := int(math.Ceil(math.Sqrt(float64(requiredPixels))))

	// 确保不小于最小尺寸
	if sideLength < g.config.MinImageSize {
		sideLength = g.config.MinImageSize
	}

	// 使用8的倍数以提高兼容性
	sideLength = ((sideLength + 7) / 8) * 8

	return sideLength, sideLength
}

// CreatePNGFromTemplate 从模板创建PNG（复用现有的PNG模板）
func (g *PNGGenerator) CreatePNGFromTemplate(templateType string) ([]byte, error) {
	switch templateType {
	case "small":
		// 1x1像素的PNG模板（来自client.go）
		return []byte{
			0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
			0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
			0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
			0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x57, 0x63, 0xF8, 0x0F, 0x00, 0x00,
			0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8E, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x49,
			0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82,
		}, nil
	case "medium":
		// 16x16像素的PNG模板（来自test_imgbb_upload.go）
		return []byte{
			0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
			0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x10,
			0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x91, 0x68, 0x36, 0x00, 0x00, 0x00,
			0x3A, 0x49, 0x44, 0x41, 0x54, 0x28, 0x15, 0x63, 0xF8, 0xCF, 0xC0, 0xC0,
			0xC0, 0x00, 0x04, 0x20, 0x80, 0x18, 0x30, 0x8A, 0x81, 0x81, 0x01, 0x02,
			0x08, 0x20, 0x06, 0x8C, 0x62, 0x60, 0x60, 0x80, 0x00, 0x02, 0x88, 0x01,
			0xA3, 0x18, 0x18, 0x18, 0x20, 0x80, 0x00, 0x62, 0xC0, 0x28, 0x06, 0x06,
			0x06, 0x08, 0x20, 0x80, 0x18, 0x30, 0x8A, 0x81, 0x81, 0x01, 0x02, 0x08,
			0x00, 0x00, 0x84, 0x3E, 0x0C, 0x8F, 0x0A, 0x72, 0xF8, 0x8F, 0x00, 0x00,
			0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82,
		}, nil
	default:
		return nil, fmt.Errorf("unknown template type: %s", templateType)
	}
}

// ValidatePNG 验证PNG格式是否正确
func (g *PNGGenerator) ValidatePNG(pngData []byte) error {
	if len(pngData) < 8 {
		return fmt.Errorf("PNG data too short")
	}

	// 检查PNG签名
	pngSignature := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A}
	if !bytes.Equal(pngData[:8], pngSignature) {
		return fmt.Errorf("invalid PNG signature")
	}

	// 尝试解码PNG
	_, err := png.Decode(bytes.NewReader(pngData))
	if err != nil {
		return fmt.Errorf("PNG decode failed: %w", err)
	}

	return nil
}

// GetPNGInfo 获取PNG图片信息
func (g *PNGGenerator) GetPNGInfo(pngData []byte) (*PNGInfo, error) {
	if err := g.ValidatePNG(pngData); err != nil {
		return nil, err
	}

	// 解码图片获取基本信息
	img, err := png.Decode(bytes.NewReader(pngData))
	if err != nil {
		return nil, fmt.Errorf("failed to decode PNG: %w", err)
	}

	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算可隐藏的数据容量
	bitsPerPixel := 3 // RGB
	if g.config.UseAlpha {
		bitsPerPixel = 4 // RGBA
	}

	totalPixels := width * height
	maxDataBits := totalPixels * bitsPerPixel
	maxDataBytes := maxDataBits / 8

	// 减去长度前缀的4字节
	if maxDataBytes > 4 {
		maxDataBytes -= 4
	} else {
		maxDataBytes = 0
	}

	return &PNGInfo{
		Width:        width,
		Height:       height,
		Size:         len(pngData),
		MaxDataBytes: maxDataBytes,
		ColorModel:   fmt.Sprintf("%T", img.ColorModel()),
	}, nil
}

// PNGInfo PNG图片信息
type PNGInfo struct {
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	Size         int    `json:"size"`
	MaxDataBytes int    `json:"max_data_bytes"`
	ColorModel   string `json:"color_model"`
}

// CreateCustomPNG 创建自定义PNG图片
func (g *PNGGenerator) CreateCustomPNG(width, height int, pattern string) ([]byte, error) {
	if width <= 0 || height <= 0 {
		return nil, fmt.Errorf("invalid dimensions: %dx%d", width, height)
	}

	img := image.NewRGBA(image.Rect(0, 0, width, height))

	switch pattern {
	case "gradient":
		g.fillGradientPattern(img)
	case "noise":
		g.fillNoisePattern(img)
	case "checkerboard":
		g.fillCheckerboardPattern(img)
	default:
		g.fillSolidPattern(img, color.RGBA{128, 128, 128, 255})
	}

	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		return nil, fmt.Errorf("failed to encode PNG: %w", err)
	}

	logger.Debugf("Created custom PNG: %dx%d, pattern: %s, size: %d bytes",
		width, height, pattern, buf.Len())
	return buf.Bytes(), nil
}

// fillGradientPattern 填充渐变模式
func (g *PNGGenerator) fillGradientPattern(img *image.RGBA) {
	bounds := img.Bounds()
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			r := uint8(float64(x) / float64(bounds.Dx()) * 255)
			g := uint8(float64(y) / float64(bounds.Dy()) * 255)
			b := uint8((float64(x) + float64(y)) / float64(bounds.Dx()+bounds.Dy()) * 255)
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}
}

// fillNoisePattern 填充噪声模式
func (g *PNGGenerator) fillNoisePattern(img *image.RGBA) {
	bounds := img.Bounds()
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			// 简单的伪随机噪声
			seed := x*1000 + y
			r := uint8((seed*1103515245 + 12345) % 256)
			g := uint8((seed*1664525 + 1013904223) % 256)
			b := uint8((seed*22695477 + 1) % 256)
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}
}

// fillCheckerboardPattern 填充棋盘模式
func (g *PNGGenerator) fillCheckerboardPattern(img *image.RGBA) {
	bounds := img.Bounds()
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			if (x/8+y/8)%2 == 0 {
				img.Set(x, y, color.RGBA{200, 200, 200, 255})
			} else {
				img.Set(x, y, color.RGBA{100, 100, 100, 255})
			}
		}
	}
}

// fillSolidPattern 填充纯色模式
func (g *PNGGenerator) fillSolidPattern(img *image.RGBA, c color.RGBA) {
	bounds := img.Bounds()
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			img.Set(x, y, c)
		}
	}
}

// OptimizePNGSize 优化PNG大小
func (g *PNGGenerator) OptimizePNGSize(pngData []byte) ([]byte, error) {
	// 解码PNG
	img, err := png.Decode(bytes.NewReader(pngData))
	if err != nil {
		return nil, fmt.Errorf("failed to decode PNG: %w", err)
	}

	// 使用最佳压缩设置重新编码
	var buf bytes.Buffer
	encoder := &png.Encoder{
		CompressionLevel: png.BestCompression,
	}

	if err := encoder.Encode(&buf, img); err != nil {
		return nil, fmt.Errorf("failed to re-encode PNG: %w", err)
	}

	originalSize := len(pngData)
	optimizedSize := buf.Len()

	logger.Debugf("PNG optimization: %d -> %d bytes (%.1f%% reduction)",
		originalSize, optimizedSize,
		float64(originalSize-optimizedSize)/float64(originalSize)*100)

	return buf.Bytes(), nil
}
