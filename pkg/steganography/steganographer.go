package steganography

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"

	"magnet-downloader/pkg/logger"
)

// Steganographer 隐写术接口
type Steganographer interface {
	HideDataInPNG(data []byte) ([]byte, error)
	ExtractDataFromPNG(pngData []byte) ([]byte, error)
	CalculateRequiredImageSize(dataSize int) (width, height int)
}

// Config 隐写术配置
type Config struct {
	BitsPerChannel int  // 每个颜色通道使用的位数（默认1，即LSB）
	UseAlpha       bool // 是否使用Alpha通道
	MinImageSize   int  // 最小图片尺寸
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		BitsPerChannel: 1,
		UseAlpha:       false,
		MinImageSize:   64,
	}
}

// steganographer 隐写术实现
type steganographer struct {
	config *Config
}

// NewSteganographer 创建新的隐写术实例
func NewSteganographer(config *Config) Steganographer {
	if config == nil {
		config = DefaultConfig()
	}
	return &steganographer{config: config}
}

// CalculateRequiredImageSize 计算所需的图片尺寸
func (s *steganographer) CalculateRequiredImageSize(dataSize int) (width, height int) {
	// 数据大小包括：4字节长度前缀 + 实际数据
	totalDataSize := 4 + dataSize
	
	// 每个像素可以隐藏的位数
	bitsPerPixel := 3 * s.config.BitsPerChannel // RGB三个通道
	if s.config.UseAlpha {
		bitsPerPixel += s.config.BitsPerChannel // 加上Alpha通道
	}
	
	// 需要的总位数
	totalBits := totalDataSize * 8
	
	// 需要的像素数
	requiredPixels := int(math.Ceil(float64(totalBits) / float64(bitsPerPixel)))
	
	// 计算正方形图片的边长
	sideLength := int(math.Ceil(math.Sqrt(float64(requiredPixels))))
	
	// 确保不小于最小尺寸
	if sideLength < s.config.MinImageSize {
		sideLength = s.config.MinImageSize
	}
	
	// 为了兼容性，使用8的倍数
	sideLength = ((sideLength + 7) / 8) * 8
	
	logger.Debugf("Calculated image size for %d bytes: %dx%d (pixels: %d, required: %d)", 
		dataSize, sideLength, sideLength, sideLength*sideLength, requiredPixels)
	
	return sideLength, sideLength
}

// HideDataInPNG 将数据隐藏到PNG图片中
func (s *steganographer) HideDataInPNG(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("data cannot be empty")
	}
	
	// 计算所需图片尺寸
	width, height := s.CalculateRequiredImageSize(len(data))
	
	// 创建空白图片
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	
	// 填充随机背景色（使图片看起来更自然）
	s.fillRandomBackground(img)
	
	// 准备要隐藏的数据：长度前缀 + 实际数据
	dataWithLength := make([]byte, 4+len(data))
	binary.BigEndian.PutUint32(dataWithLength[:4], uint32(len(data)))
	copy(dataWithLength[4:], data)
	
	// 将数据转换为位序列
	bits := s.bytesToBits(dataWithLength)
	
	// 将位序列隐藏到图片中
	if err := s.hideBitsInImage(img, bits); err != nil {
		return nil, fmt.Errorf("failed to hide bits in image: %w", err)
	}
	
	// 将图片编码为PNG
	var buf bytes.Buffer
	if err := png.Encode(&buf, img); err != nil {
		return nil, fmt.Errorf("failed to encode PNG: %w", err)
	}
	
	logger.Infof("Successfully hid %d bytes in %dx%d PNG image", len(data), width, height)
	return buf.Bytes(), nil
}

// ExtractDataFromPNG 从PNG图片中提取隐藏的数据
func (s *steganographer) ExtractDataFromPNG(pngData []byte) ([]byte, error) {
	// 解码PNG图片
	img, err := png.Decode(bytes.NewReader(pngData))
	if err != nil {
		return nil, fmt.Errorf("failed to decode PNG: %w", err)
	}
	
	// 转换为RGBA格式
	bounds := img.Bounds()
	rgba := image.NewRGBA(bounds)
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			rgba.Set(x, y, img.At(x, y))
		}
	}
	
	// 首先提取长度信息（前32位）
	lengthBits := s.extractBitsFromImage(rgba, 32)
	if len(lengthBits) < 32 {
		return nil, fmt.Errorf("insufficient data in image for length prefix")
	}
	
	// 解析数据长度
	lengthBytes := s.bitsToBytes(lengthBits[:32])
	dataLength := binary.BigEndian.Uint32(lengthBytes)
	
	if dataLength == 0 {
		return nil, fmt.Errorf("invalid data length: 0")
	}
	
	if dataLength > 100*1024*1024 { // 100MB限制
		return nil, fmt.Errorf("data length too large: %d bytes", dataLength)
	}
	
	// 提取实际数据
	totalBitsNeeded := 32 + int(dataLength)*8
	allBits := s.extractBitsFromImage(rgba, totalBitsNeeded)
	
	if len(allBits) < totalBitsNeeded {
		return nil, fmt.Errorf("insufficient data in image: need %d bits, got %d", 
			totalBitsNeeded, len(allBits))
	}
	
	// 提取数据部分（跳过长度前缀）
	dataBits := allBits[32:totalBitsNeeded]
	data := s.bitsToBytes(dataBits)
	
	logger.Infof("Successfully extracted %d bytes from PNG image", len(data))
	return data, nil
}

// fillRandomBackground 填充随机背景色
func (s *steganographer) fillRandomBackground(img *image.RGBA) {
	bounds := img.Bounds()
	
	// 使用简单的伪随机模式创建背景
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			// 创建看起来自然的背景色
			r := uint8(128 + (x*y)%128)
			g := uint8(128 + (x+y)%128)
			b := uint8(128 + (x*2+y)%128)
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}
}

// bytesToBits 将字节转换为位序列
func (s *steganographer) bytesToBits(data []byte) []bool {
	bits := make([]bool, len(data)*8)
	for i, b := range data {
		for j := 0; j < 8; j++ {
			bits[i*8+j] = (b>>(7-j))&1 == 1
		}
	}
	return bits
}

// bitsToBytes 将位序列转换为字节
func (s *steganographer) bitsToBytes(bits []bool) []byte {
	// 确保位数是8的倍数
	for len(bits)%8 != 0 {
		bits = append(bits, false)
	}
	
	bytes := make([]byte, len(bits)/8)
	for i := 0; i < len(bytes); i++ {
		var b byte
		for j := 0; j < 8; j++ {
			if bits[i*8+j] {
				b |= 1 << (7 - j)
			}
		}
		bytes[i] = b
	}
	return bytes
}

// hideBitsInImage 将位序列隐藏到图片中
func (s *steganographer) hideBitsInImage(img *image.RGBA, bits []bool) error {
	bounds := img.Bounds()
	bitIndex := 0
	
	for y := bounds.Min.Y; y < bounds.Max.Y && bitIndex < len(bits); y++ {
		for x := bounds.Min.X; x < bounds.Max.X && bitIndex < len(bits); x++ {
			c := img.RGBAAt(x, y)
			
			// 修改RGB通道的LSB
			if bitIndex < len(bits) {
				c.R = s.setBit(c.R, bits[bitIndex])
				bitIndex++
			}
			if bitIndex < len(bits) {
				c.G = s.setBit(c.G, bits[bitIndex])
				bitIndex++
			}
			if bitIndex < len(bits) {
				c.B = s.setBit(c.B, bits[bitIndex])
				bitIndex++
			}
			
			// 如果启用Alpha通道
			if s.config.UseAlpha && bitIndex < len(bits) {
				c.A = s.setBit(c.A, bits[bitIndex])
				bitIndex++
			}
			
			img.SetRGBA(x, y, c)
		}
	}
	
	if bitIndex < len(bits) {
		return fmt.Errorf("image too small to hide all data: hid %d/%d bits", bitIndex, len(bits))
	}
	
	return nil
}

// extractBitsFromImage 从图片中提取位序列
func (s *steganographer) extractBitsFromImage(img *image.RGBA, maxBits int) []bool {
	bounds := img.Bounds()
	bits := make([]bool, 0, maxBits)
	
	for y := bounds.Min.Y; y < bounds.Max.Y && len(bits) < maxBits; y++ {
		for x := bounds.Min.X; x < bounds.Max.X && len(bits) < maxBits; x++ {
			c := img.RGBAAt(x, y)
			
			// 提取RGB通道的LSB
			if len(bits) < maxBits {
				bits = append(bits, s.getBit(c.R))
			}
			if len(bits) < maxBits {
				bits = append(bits, s.getBit(c.G))
			}
			if len(bits) < maxBits {
				bits = append(bits, s.getBit(c.B))
			}
			
			// 如果启用Alpha通道
			if s.config.UseAlpha && len(bits) < maxBits {
				bits = append(bits, s.getBit(c.A))
			}
		}
	}
	
	return bits
}

// setBit 设置字节的最低位
func (s *steganographer) setBit(b uint8, bit bool) uint8 {
	if bit {
		return b | 1
	}
	return b &^ 1
}

// getBit 获取字节的最低位
func (s *steganographer) getBit(b uint8) bool {
	return b&1 == 1
}
