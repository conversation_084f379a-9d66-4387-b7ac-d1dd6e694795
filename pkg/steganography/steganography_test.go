package steganography

import (
	"bytes"
	"image"
	"image/png"
	"testing"

	"magnet-downloader/pkg/logger"
)

func init() {
	// 初始化日志用于测试
	logger.Init("debug", "text")
}

// TestSteganographerBasic 测试基本的隐写功能
func TestSteganographerBasic(t *testing.T) {
	config := DefaultConfig()
	stego := NewSteganographer(config)

	// 测试数据
	testData := []byte("Hello, MixFile! This is a test message for steganography.")

	// 隐藏数据
	pngData, err := stego.HideDataInPNG(testData)
	if err != nil {
		t.Fatalf("Failed to hide data: %v", err)
	}

	// 验证生成的是有效的PNG
	_, err = png.Decode(bytes.NewReader(pngData))
	if err != nil {
		t.Fatalf("Generated PNG is invalid: %v", err)
	}

	// 提取数据
	extractedData, err := stego.ExtractDataFromPNG(pngData)
	if err != nil {
		t.Fatalf("Failed to extract data: %v", err)
	}

	// 验证数据一致性
	if !bytes.Equal(testData, extractedData) {
		t.Fatalf("Data mismatch: expected %q, got %q", testData, extractedData)
	}

	t.Logf("Successfully hid and extracted %d bytes", len(testData))
}

// TestSteganographerLargeData 测试大数据隐写
func TestSteganographerLargeData(t *testing.T) {
	config := DefaultConfig()
	stego := NewSteganographer(config)

	// 创建1KB的测试数据
	testData := make([]byte, 1024)
	for i := range testData {
		testData[i] = byte(i % 256)
	}

	// 隐藏数据
	pngData, err := stego.HideDataInPNG(testData)
	if err != nil {
		t.Fatalf("Failed to hide large data: %v", err)
	}

	// 验证PNG有效性
	_, err = png.Decode(bytes.NewReader(pngData))
	if err != nil {
		t.Fatalf("Generated PNG is invalid: %v", err)
	}

	// 提取数据
	extractedData, err := stego.ExtractDataFromPNG(pngData)
	if err != nil {
		t.Fatalf("Failed to extract large data: %v", err)
	}

	// 验证数据一致性
	if !bytes.Equal(testData, extractedData) {
		t.Fatalf("Large data mismatch")
	}

	t.Logf("Successfully processed %d bytes of data", len(testData))
}

// TestCalculateRequiredImageSize 测试图片尺寸计算
func TestCalculateRequiredImageSize(t *testing.T) {
	config := DefaultConfig()
	stego := NewSteganographer(config)

	testCases := []struct {
		dataSize  int
		minWidth  int
		minHeight int
	}{
		{100, 64, 64},   // 小数据
		{1000, 64, 64},  // 中等数据
		{10000, 64, 64}, // 大数据
	}

	for _, tc := range testCases {
		width, height := stego.CalculateRequiredImageSize(tc.dataSize)

		if width < tc.minWidth || height < tc.minHeight {
			t.Errorf("Image size too small for %d bytes: %dx%d", tc.dataSize, width, height)
		}

		// 验证计算的尺寸确实能容纳数据
		totalPixels := width * height
		bitsPerPixel := 3 // RGB
		maxDataBits := totalPixels * bitsPerPixel
		maxDataBytes := maxDataBits/8 - 4 // 减去长度前缀

		if maxDataBytes < tc.dataSize {
			t.Errorf("Calculated size insufficient: need %d bytes, can store %d bytes",
				tc.dataSize, maxDataBytes)
		}

		t.Logf("Data size %d bytes -> Image size %dx%d (capacity: %d bytes)",
			tc.dataSize, width, height, maxDataBytes)
	}
}

// TestPNGGenerator 测试PNG生成器
func TestPNGGenerator(t *testing.T) {
	config := DefaultConfig()
	generator := NewPNGGenerator(config)

	// 测试空白PNG生成
	pngData, err := generator.GenerateBlankPNG(100, 100)
	if err != nil {
		t.Fatalf("Failed to generate blank PNG: %v", err)
	}

	// 验证PNG有效性
	_, err = png.Decode(bytes.NewReader(pngData))
	if err != nil {
		t.Fatalf("Generated blank PNG is invalid: %v", err)
	}

	// 测试模板PNG
	templateData, err := generator.CreatePNGFromTemplate("small")
	if err != nil {
		t.Fatalf("Failed to create PNG from template: %v", err)
	}

	// 验证模板PNG
	_, err = png.Decode(bytes.NewReader(templateData))
	if err != nil {
		t.Fatalf("Template PNG is invalid: %v", err)
	}

	// 测试PNG信息获取
	info, err := generator.GetPNGInfo(pngData)
	if err != nil {
		t.Fatalf("Failed to get PNG info: %v", err)
	}

	if info.Width != 100 || info.Height != 100 {
		t.Errorf("PNG info incorrect: expected 100x100, got %dx%d", info.Width, info.Height)
	}

	t.Logf("PNG info: %dx%d, size: %d bytes, capacity: %d bytes",
		info.Width, info.Height, info.Size, info.MaxDataBytes)
}

// TestLSBEncoder 测试LSB编码器
func TestLSBEncoder(t *testing.T) {
	encoder := NewLSBEncoder(1, false) // 1位，不使用Alpha

	// 创建测试图片
	config := DefaultConfig()
	generator := NewPNGGenerator(config)
	pngData, err := generator.GenerateBlankPNG(64, 64)
	if err != nil {
		t.Fatalf("Failed to generate test image: %v", err)
	}

	// 解码为RGBA
	img, err := png.Decode(bytes.NewReader(pngData))
	if err != nil {
		t.Fatalf("Failed to decode test image: %v", err)
	}

	// 转换为RGBA
	bounds := img.Bounds()
	rgba := NewRGBAFromImage(img, bounds)

	// 测试容量计算
	capacity := encoder.CalculateCapacity(64, 64)
	expectedCapacity := 64 * 64 * 3 / 8 // 64x64像素，3个通道，1位每通道
	if capacity != expectedCapacity {
		t.Errorf("Capacity calculation incorrect: expected %d, got %d", expectedCapacity, capacity)
	}

	// 测试数据编码
	testData := encoder.CreateTestPattern(100)
	err = encoder.EncodeData(rgba, testData)
	if err != nil {
		t.Fatalf("Failed to encode data: %v", err)
	}

	// 测试数据解码
	decodedData, err := encoder.DecodeData(rgba, len(testData))
	if err != nil {
		t.Fatalf("Failed to decode data: %v", err)
	}

	// 验证数据一致性
	if !bytes.Equal(testData, decodedData) {
		t.Fatalf("LSB encoding/decoding failed: data mismatch")
	}

	// 测试编码验证
	err = encoder.VerifyEncoding(rgba, testData)
	if err != nil {
		t.Fatalf("Encoding verification failed: %v", err)
	}

	t.Logf("LSB encoder test successful: %d bytes", len(testData))
}

// TestSteganographyWithAlpha 测试使用Alpha通道的隐写
func TestSteganographyWithAlpha(t *testing.T) {
	config := &Config{
		BitsPerChannel: 1,
		UseAlpha:       true,
		MinImageSize:   64,
	}
	stego := NewSteganographer(config)

	testData := []byte("Testing with Alpha channel support!")

	// 隐藏数据
	pngData, err := stego.HideDataInPNG(testData)
	if err != nil {
		t.Fatalf("Failed to hide data with alpha: %v", err)
	}

	// 提取数据
	extractedData, err := stego.ExtractDataFromPNG(pngData)
	if err != nil {
		t.Fatalf("Failed to extract data with alpha: %v", err)
	}

	// 验证数据一致性
	if !bytes.Equal(testData, extractedData) {
		t.Fatalf("Alpha channel data mismatch")
	}

	t.Logf("Alpha channel test successful: %d bytes", len(testData))
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	config := DefaultConfig()
	stego := NewSteganographer(config)

	// 测试空数据
	_, err := stego.HideDataInPNG([]byte{})
	if err == nil {
		t.Error("Expected error for empty data")
	}

	// 测试无效PNG数据
	_, err = stego.ExtractDataFromPNG([]byte("invalid png data"))
	if err == nil {
		t.Error("Expected error for invalid PNG data")
	}

	// 测试PNG生成器错误处理
	generator := NewPNGGenerator(config)
	_, err = generator.GenerateBlankPNG(-1, -1)
	if err == nil {
		t.Error("Expected error for invalid dimensions")
	}

	t.Log("Error handling tests passed")
}

// NewRGBAFromImage 辅助函数：从image.Image创建RGBA图片
func NewRGBAFromImage(img image.Image, bounds image.Rectangle) *image.RGBA {
	rgba := image.NewRGBA(bounds)
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			rgba.Set(x, y, img.At(x, y))
		}
	}
	return rgba
}

// BenchmarkSteganography 性能基准测试
func BenchmarkSteganography(b *testing.B) {
	config := DefaultConfig()
	stego := NewSteganographer(config)
	testData := make([]byte, 1024) // 1KB测试数据

	for i := range testData {
		testData[i] = byte(i % 256)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		pngData, err := stego.HideDataInPNG(testData)
		if err != nil {
			b.Fatalf("Hide failed: %v", err)
		}

		_, err = stego.ExtractDataFromPNG(pngData)
		if err != nil {
			b.Fatalf("Extract failed: %v", err)
		}
	}
}

// TestIntegrationWithImgBB 集成测试（模拟imgbb上传）
func TestIntegrationWithImgBB(t *testing.T) {
	config := DefaultConfig()
	stego := NewSteganographer(config)

	// 模拟加密的分片数据
	encryptedChunk := make([]byte, 1024*1024) // 1MB
	for i := range encryptedChunk {
		encryptedChunk[i] = byte(i % 256)
	}

	// 隐藏到PNG中
	pngData, err := stego.HideDataInPNG(encryptedChunk)
	if err != nil {
		t.Fatalf("Failed to hide encrypted chunk: %v", err)
	}

	// 验证PNG格式
	generator := NewPNGGenerator(config)
	err = generator.ValidatePNG(pngData)
	if err != nil {
		t.Fatalf("Generated PNG is invalid: %v", err)
	}

	// 获取PNG信息
	info, err := generator.GetPNGInfo(pngData)
	if err != nil {
		t.Fatalf("Failed to get PNG info: %v", err)
	}

	t.Logf("Integration test successful:")
	t.Logf("  Original data: %d bytes", len(encryptedChunk))
	t.Logf("  PNG size: %d bytes", len(pngData))
	t.Logf("  PNG dimensions: %dx%d", info.Width, info.Height)
	t.Logf("  Compression ratio: %.2f", float64(len(pngData))/float64(len(encryptedChunk)))

	// 验证可以提取原始数据
	extractedData, err := stego.ExtractDataFromPNG(pngData)
	if err != nil {
		t.Fatalf("Failed to extract data: %v", err)
	}

	if !bytes.Equal(encryptedChunk, extractedData) {
		t.Fatalf("Data integrity check failed")
	}

	t.Log("Data integrity verified")
}
