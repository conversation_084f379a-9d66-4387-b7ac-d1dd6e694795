package steganography

import (
	"fmt"
	"image"
	"math"

	"magnet-downloader/pkg/logger"
)

// LSBEncoder LSB（最低有效位）编码器
type LSBEncoder struct {
	bitsPerChannel int
	useAlpha       bool
}

// NewLSBEncoder 创建LSB编码器
func NewLSBEncoder(bitsPerChannel int, useAlpha bool) *LSBEncoder {
	if bitsPerChannel < 1 || bitsPerChannel > 8 {
		bitsPerChannel = 1 // 默认使用1位
	}
	return &LSBEncoder{
		bitsPerChannel: bitsPerChannel,
		useAlpha:       useAlpha,
	}
}

// CalculateCapacity 计算图片的数据容量
func (e *LSBEncoder) CalculateCapacity(width, height int) int {
	totalPixels := width * height
	channelsPerPixel := 3 // RGB
	if e.useAlpha {
		channelsPerPixel = 4 // RGBA
	}

	totalBits := totalPixels * channelsPerPixel * e.bitsPerChannel
	return totalBits / 8 // 转换为字节
}

// EncodeData 将数据编码到图片中
func (e *LSBEncoder) EncodeData(img *image.RGBA, data []byte) error {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 检查容量
	capacity := e.CalculateCapacity(width, height)
	if len(data) > capacity {
		return fmt.Errorf("data too large: %d bytes, capacity: %d bytes", len(data), capacity)
	}

	// 将数据转换为位序列
	bits := e.bytesToBits(data)

	// 编码到图片中
	bitIndex := 0
	for y := 0; y < height && bitIndex < len(bits); y++ {
		for x := 0; x < width && bitIndex < len(bits); x++ {
			pixel := img.RGBAAt(x, y)

			// 编码到R通道
			if bitIndex < len(bits) {
				pixel.R = e.encodeBitsInChannel(pixel.R, bits[bitIndex:bitIndex+min(e.bitsPerChannel, len(bits)-bitIndex)])
				bitIndex += min(e.bitsPerChannel, len(bits)-bitIndex)
			}

			// 编码到G通道
			if bitIndex < len(bits) {
				pixel.G = e.encodeBitsInChannel(pixel.G, bits[bitIndex:bitIndex+min(e.bitsPerChannel, len(bits)-bitIndex)])
				bitIndex += min(e.bitsPerChannel, len(bits)-bitIndex)
			}

			// 编码到B通道
			if bitIndex < len(bits) {
				pixel.B = e.encodeBitsInChannel(pixel.B, bits[bitIndex:bitIndex+min(e.bitsPerChannel, len(bits)-bitIndex)])
				bitIndex += min(e.bitsPerChannel, len(bits)-bitIndex)
			}

			// 编码到A通道（如果启用）
			if e.useAlpha && bitIndex < len(bits) {
				pixel.A = e.encodeBitsInChannel(pixel.A, bits[bitIndex:bitIndex+min(e.bitsPerChannel, len(bits)-bitIndex)])
				bitIndex += min(e.bitsPerChannel, len(bits)-bitIndex)
			}

			img.SetRGBA(x, y, pixel)
		}
	}

	logger.Debugf("Encoded %d bits (%d bytes) using LSB method", bitIndex, len(data))
	return nil
}

// DecodeData 从图片中解码数据
func (e *LSBEncoder) DecodeData(img *image.RGBA, dataLength int) ([]byte, error) {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 检查容量
	capacity := e.CalculateCapacity(width, height)
	if dataLength > capacity {
		return nil, fmt.Errorf("requested data length too large: %d bytes, capacity: %d bytes",
			dataLength, capacity)
	}

	// 计算需要的位数
	bitsNeeded := dataLength * 8
	bits := make([]bool, 0, bitsNeeded)

	// 从图片中提取位
	for y := 0; y < height && len(bits) < bitsNeeded; y++ {
		for x := 0; x < width && len(bits) < bitsNeeded; x++ {
			pixel := img.RGBAAt(x, y)

			// 从R通道解码
			if len(bits) < bitsNeeded {
				channelBits := e.decodeBitsFromChannel(pixel.R)
				for i := 0; i < min(e.bitsPerChannel, bitsNeeded-len(bits)); i++ {
					bits = append(bits, channelBits[i])
				}
			}

			// 从G通道解码
			if len(bits) < bitsNeeded {
				channelBits := e.decodeBitsFromChannel(pixel.G)
				for i := 0; i < min(e.bitsPerChannel, bitsNeeded-len(bits)); i++ {
					bits = append(bits, channelBits[i])
				}
			}

			// 从B通道解码
			if len(bits) < bitsNeeded {
				channelBits := e.decodeBitsFromChannel(pixel.B)
				for i := 0; i < min(e.bitsPerChannel, bitsNeeded-len(bits)); i++ {
					bits = append(bits, channelBits[i])
				}
			}

			// 从A通道解码（如果启用）
			if e.useAlpha && len(bits) < bitsNeeded {
				channelBits := e.decodeBitsFromChannel(pixel.A)
				for i := 0; i < min(e.bitsPerChannel, bitsNeeded-len(bits)); i++ {
					bits = append(bits, channelBits[i])
				}
			}
		}
	}

	if len(bits) < bitsNeeded {
		return nil, fmt.Errorf("insufficient data in image: got %d bits, need %d bits",
			len(bits), bitsNeeded)
	}

	// 将位转换为字节
	data := e.bitsToBytes(bits[:bitsNeeded])

	logger.Debugf("Decoded %d bits (%d bytes) using LSB method", len(bits), len(data))
	return data, nil
}

// encodeBitsInChannel 将位编码到颜色通道中
func (e *LSBEncoder) encodeBitsInChannel(channel uint8, bits []bool) uint8 {
	// 清除低位
	mask := uint8(0xFF << e.bitsPerChannel)
	channel &= mask

	// 设置新的位
	for i, bit := range bits {
		if i >= e.bitsPerChannel {
			break
		}
		if bit {
			channel |= 1 << i
		}
	}

	return channel
}

// decodeBitsFromChannel 从颜色通道中解码位
func (e *LSBEncoder) decodeBitsFromChannel(channel uint8) []bool {
	bits := make([]bool, e.bitsPerChannel)
	for i := 0; i < e.bitsPerChannel; i++ {
		bits[i] = (channel>>i)&1 == 1
	}
	return bits
}

// bytesToBits 将字节转换为位序列
func (e *LSBEncoder) bytesToBits(data []byte) []bool {
	bits := make([]bool, len(data)*8)
	for i, b := range data {
		for j := 0; j < 8; j++ {
			bits[i*8+j] = (b>>(7-j))&1 == 1
		}
	}
	return bits
}

// bitsToBytes 将位序列转换为字节
func (e *LSBEncoder) bitsToBytes(bits []bool) []byte {
	// 确保位数是8的倍数
	for len(bits)%8 != 0 {
		bits = append(bits, false)
	}

	bytes := make([]byte, len(bits)/8)
	for i := 0; i < len(bytes); i++ {
		var b byte
		for j := 0; j < 8; j++ {
			if bits[i*8+j] {
				b |= 1 << (7 - j)
			}
		}
		bytes[i] = b
	}
	return bytes
}

// AnalyzeImage 分析图片的隐写特征
func (e *LSBEncoder) AnalyzeImage(img *image.RGBA) *LSBAnalysis {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	analysis := &LSBAnalysis{
		Width:    width,
		Height:   height,
		Capacity: e.CalculateCapacity(width, height),
	}

	// 分析LSB位的随机性
	lsbBits := make([]bool, 0)
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			pixel := img.RGBAAt(x, y)
			lsbBits = append(lsbBits, pixel.R&1 == 1)
			lsbBits = append(lsbBits, pixel.G&1 == 1)
			lsbBits = append(lsbBits, pixel.B&1 == 1)
			if e.useAlpha {
				lsbBits = append(lsbBits, pixel.A&1 == 1)
			}
		}
	}

	// 计算LSB位的统计特征
	onesCount := 0
	for _, bit := range lsbBits {
		if bit {
			onesCount++
		}
	}

	analysis.LSBRandomness = float64(onesCount) / float64(len(lsbBits))
	analysis.TotalLSBBits = len(lsbBits)

	// 计算相邻位的相关性（简单的随机性检测）
	correlationSum := 0
	for i := 1; i < len(lsbBits); i++ {
		if lsbBits[i] == lsbBits[i-1] {
			correlationSum++
		}
	}
	analysis.LSBCorrelation = float64(correlationSum) / float64(len(lsbBits)-1)

	return analysis
}

// LSBAnalysis LSB分析结果
type LSBAnalysis struct {
	Width          int     `json:"width"`
	Height         int     `json:"height"`
	Capacity       int     `json:"capacity"`
	LSBRandomness  float64 `json:"lsb_randomness"`  // LSB位中1的比例，理想值接近0.5
	LSBCorrelation float64 `json:"lsb_correlation"` // 相邻LSB位的相关性，理想值接近0.5
	TotalLSBBits   int     `json:"total_lsb_bits"`
}

// EstimateDataPresence 估计图片中是否存在隐藏数据
func (e *LSBEncoder) EstimateDataPresence(analysis *LSBAnalysis) bool {
	// 如果LSB位的随机性偏离0.5太多，可能包含数据
	randomnessDeviation := math.Abs(analysis.LSBRandomness - 0.5)

	// 如果相邻位的相关性偏离0.5太多，可能包含数据
	correlationDeviation := math.Abs(analysis.LSBCorrelation - 0.5)

	// 简单的启发式检测
	threshold := 0.1
	return randomnessDeviation > threshold || correlationDeviation > threshold
}

// min 辅助函数：返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// CreateTestPattern 创建测试模式（用于验证编码解码）
func (e *LSBEncoder) CreateTestPattern(size int) []byte {
	pattern := make([]byte, size)
	for i := range pattern {
		// 创建可预测的测试模式
		pattern[i] = byte((i * 37) % 256)
	}
	return pattern
}

// VerifyEncoding 验证编码解码的正确性
func (e *LSBEncoder) VerifyEncoding(img *image.RGBA, originalData []byte) error {
	// 解码数据
	decodedData, err := e.DecodeData(img, len(originalData))
	if err != nil {
		return fmt.Errorf("decode failed: %w", err)
	}

	// 比较数据
	if len(decodedData) != len(originalData) {
		return fmt.Errorf("length mismatch: expected %d, got %d",
			len(originalData), len(decodedData))
	}

	for i := range originalData {
		if decodedData[i] != originalData[i] {
			return fmt.Errorf("data mismatch at byte %d: expected %02x, got %02x",
				i, originalData[i], decodedData[i])
		}
	}

	logger.Debugf("Encoding verification successful: %d bytes", len(originalData))
	return nil
}
