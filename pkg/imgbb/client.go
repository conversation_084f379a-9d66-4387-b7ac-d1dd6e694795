package imgbb

import (
	"bytes"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/textproto"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/steganography"
)

// Config 本地图床配置
type Config struct {
	APIKey     string        `json:"api_key"`     // API密钥（保持兼容性，本地图床不需要）
	BaseURL    string        `json:"base_url"`    // API基础URL
	Timeout    time.Duration `json:"timeout"`     // 请求超时时间
	MaxRetries int           `json:"max_retries"` // 最大重试次数
}

// UploadResponse Telegraph-Image Express上传响应
type UploadResponse struct {
	Src string `json:"src"` // Telegraph-Image Express返回格式: [{"src": "/file/..."}]
}

// TelegraphResponse Telegraph-Image Express响应数组
type TelegraphResponse []UploadResponse

// BatchUploadResponse 批量上传响应
type BatchUploadResponse struct {
	Success int `json:"success"`
	Failed  int `json:"failed"`
	Total   int `json:"total"`
	Results []struct {
		Src      string `json:"src"`
		Filename string `json:"filename"`
		Size     int    `json:"size"`
	} `json:"results"`
	Errors []struct {
		Filename string `json:"filename"`
		Error    string `json:"error"`
	} `json:"errors"`
}

// Image 图片信息
type Image struct {
	Filename  string `json:"filename"`
	Name      string `json:"name"`
	Mime      string `json:"mime"`
	Extension string `json:"extension"`
	URL       string `json:"url"`
	Size      int    `json:"size"`
}

// Error 错误信息
type Error struct {
	Message string `json:"message"`
	Code    int    `json:"code"`
	Context string `json:"context"`
}

// UploadResult 上传结果
type UploadResult struct {
	Success   bool   `json:"success"`
	URL       string `json:"url"`
	DeleteURL string `json:"delete_url"`
	Size      int    `json:"size"`
	Error     string `json:"error,omitempty"`
}

// Client ImgBB客户端
type Client struct {
	config     *Config
	httpClient *http.Client
	mutex      sync.RWMutex
}

// NewClient 创建新的本地图床客户端
func NewClient(config *Config) *Client {
	if config == nil {
		config = &Config{
			BaseURL:    "http://localhost:3000",
			Timeout:    30 * time.Second,
			MaxRetries: 3,
		}
	}

	if config.BaseURL == "" {
		config.BaseURL = "http://localhost:3000"
	}

	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}

	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// UploadFile 上传文件
func (c *Client) UploadFile(filePath string) (*UploadResult, error) {
	// 读取文件
	fileData, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	filename := filepath.Base(filePath)
	return c.UploadData(fileData, filename)
}

// UploadData 上传数据到本地图床
func (c *Client) UploadData(data []byte, filename string) (*UploadResult, error) {
	// 本地图床不需要API密钥，直接处理数据

	// 检查是否为图片文件，如果不是则伪装成PNG
	processedData, processedFilename := c.processDataForUpload(data, filename)

	// 执行上传请求
	var lastErr error
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避
			backoff := time.Duration(attempt*attempt) * time.Second
			logger.Debugf("Retrying upload after %v (attempt %d/%d)", backoff, attempt+1, c.config.MaxRetries)
			time.Sleep(backoff)
		}

		// 每次重试都重新创建表单数据
		var buf bytes.Buffer
		writer := multipart.NewWriter(&buf)

		// 创建文件字段，设置正确的MIME类型
		h := make(textproto.MIMEHeader)
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="file"; filename="%s"`, processedFilename))
		
		// 根据文件扩展名设置MIME类型
		mimeType := c.getMimeType(processedFilename)
		h.Set("Content-Type", mimeType)
		
		fileWriter, err := writer.CreatePart(h)
		if err != nil {
			lastErr = fmt.Errorf("failed to create form file: %w", err)
			continue
		}

		// 写入文件数据
		if _, err := fileWriter.Write(processedData); err != nil {
			lastErr = fmt.Errorf("failed to write file data: %w", err)
			continue
		}

		writer.Close()

		result, err := c.doUpload(&buf, writer.FormDataContentType(), filename)
		if err == nil {
			return result, nil
		}

		lastErr = err
		logger.Warnf("Upload attempt %d failed: %v", attempt+1, err)
	}

	return &UploadResult{
		Success: false,
		Error:   fmt.Sprintf("upload failed after %d attempts: %v", c.config.MaxRetries, lastErr),
	}, lastErr
}

// doUpload 执行上传请求到本地图床
func (c *Client) doUpload(body *bytes.Buffer, contentType, filename string) (*UploadResult, error) {
	url := fmt.Sprintf("%s/upload", c.config.BaseURL)

	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", contentType)
	req.Header.Set("User-Agent", "magnet-downloader/1.0")

	logger.Debugf("Uploading to local image host: filename=%s, size=%d", filename, body.Len())

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logger.Errorf("Upload failed - Status: %d, Response: %s", resp.StatusCode, string(respBody))
		return &UploadResult{
			Success: false,
			Error:   fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(respBody)),
		}, fmt.Errorf("upload failed with status %d", resp.StatusCode)
	}

	// 解析Telegraph-Image Express响应格式: [{"src": "/file/..."}]
	var telegraphResp TelegraphResponse
	if err := json.Unmarshal(respBody, &telegraphResp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// 检查响应是否有效
	if len(telegraphResp) == 0 || telegraphResp[0].Src == "" {
		return &UploadResult{
			Success: false,
			Error:   "invalid response from image host",
		}, fmt.Errorf("invalid response: %s", string(respBody))
	}

	// 构建完整URL
	fullURL := fmt.Sprintf("%s%s", c.config.BaseURL, telegraphResp[0].Src)

	logger.Infof("Upload successful: filename=%s, url=%s", filename, fullURL)

	return &UploadResult{
		Success:   true,
		URL:       fullURL,
		DeleteURL: "",            // 本地图床暂不支持删除URL
		Size:      len(respBody), // 使用响应大小作为估算
	}, nil
}

// TestConnection 测试本地图床连接
func (c *Client) TestConnection() error {
	// 本地图床不需要API密钥，直接测试连接

	// 创建一个小的测试图片（1x1像素的PNG）
	testData := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
		0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
		0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
		0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x57, 0x63, 0xF8, 0x0F, 0x00, 0x00,
		0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8E, 0x8E, 0x00, 0x00, 0x00, 0x00, 0x49,
		0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82,
	}

	result, err := c.UploadData(testData, "test.png")
	if err != nil {
		return fmt.Errorf("connection test failed: %w", err)
	}

	if !result.Success {
		return fmt.Errorf("connection test failed: %s", result.Error)
	}

	logger.Info("Local image host connection test successful")
	return nil
}

// BatchUploadData 批量上传数据到本地图床
func (c *Client) BatchUploadData(files []FileData) (*BatchUploadResponse, error) {
	// 创建表单数据
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	for i, file := range files {
		// 处理数据
		processedData, processedFilename := c.processDataForUpload(file.Data, file.Filename)
		
		// 创建文件字段，设置正确的MIME类型
		h := make(textproto.MIMEHeader)
		h.Set("Content-Disposition", fmt.Sprintf(`form-data; name="files"; filename="%s"`, processedFilename))
		
		// 根据文件扩展名设置MIME类型
		mimeType := c.getMimeType(processedFilename)
		h.Set("Content-Type", mimeType)
		
		fileWriter, err := writer.CreatePart(h)
		if err != nil {
			return nil, fmt.Errorf("failed to create form file %d: %w", i, err)
		}

		// 写入文件数据
		if _, err := fileWriter.Write(processedData); err != nil {
			return nil, fmt.Errorf("failed to write file data %d: %w", i, err)
		}
	}

	writer.Close()

	// 执行批量上传请求
	var lastErr error
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避
			backoff := time.Duration(attempt*attempt) * time.Second
			logger.Debugf("Retrying batch upload after %v (attempt %d/%d)", backoff, attempt+1, c.config.MaxRetries)
			time.Sleep(backoff)
		}

		result, err := c.doBatchUpload(&buf, writer.FormDataContentType(), len(files))
		if err == nil {
			return result, nil
		}

		lastErr = err
		logger.Warnf("Batch upload attempt %d failed: %v", attempt+1, err)
	}

	return nil, lastErr
}

// doBatchUpload 执行批量上传请求到本地图床
func (c *Client) doBatchUpload(body *bytes.Buffer, contentType string, fileCount int) (*BatchUploadResponse, error) {
	url := fmt.Sprintf("%s/upload/batch", c.config.BaseURL)

	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", contentType)
	req.Header.Set("User-Agent", "magnet-downloader/1.0")

	logger.Debugf("Batch uploading to local image host: files=%d, size=%d", fileCount, body.Len())

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logger.Errorf("Batch upload failed - Status: %d, Response: %s", resp.StatusCode, string(respBody))
		return nil, fmt.Errorf("batch upload failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// 解析批量上传响应
	var batchResp BatchUploadResponse
	if err := json.Unmarshal(respBody, &batchResp); err != nil {
		return nil, fmt.Errorf("failed to parse batch response: %w", err)
	}

	logger.Infof("Batch upload completed: success=%d, failed=%d, total=%d", 
		batchResp.Success, batchResp.Failed, batchResp.Total)

	return &batchResp, nil
}

// FileData 文件数据结构
type FileData struct {
	Data     []byte
	Filename string
}

// GetBaseURL 获取基础URL
func (c *Client) GetBaseURL() string {
	return c.config.BaseURL
}

// GetConfig 获取配置
func (c *Client) GetConfig() *Config {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 返回配置副本
	config := *c.config
	return &config
}

// UpdateConfig 更新配置
func (c *Client) UpdateConfig(config *Config) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.config = config
	c.httpClient.Timeout = config.Timeout
}

// BatchUploadResult 批量上传结果
type BatchUploadResult struct {
	Total    int            `json:"total"`    // 总数
	Success  int            `json:"success"`  // 成功数
	Failed   int            `json:"failed"`   // 失败数
	Results  []UploadResult `json:"results"`  // 详细结果
	URLs     []string       `json:"urls"`     // 成功上传的URL列表
	Errors   []string       `json:"errors"`   // 错误列表
	Duration time.Duration  `json:"duration"` // 总耗时
}

// UploadJob 上传任务
type UploadJob struct {
	Index    int    `json:"index"`    // 索引
	FilePath string `json:"filepath"` // 文件路径
	Data     []byte `json:"data"`     // 文件数据
	Filename string `json:"filename"` // 文件名
}

// BatchUpload 批量上传文件
func (c *Client) BatchUpload(jobs []UploadJob, maxConcurrent int, progressCallback func(completed, total int)) (*BatchUploadResult, error) {
	startTime := time.Now()

	result := &BatchUploadResult{
		Total:   len(jobs),
		Results: make([]UploadResult, len(jobs)),
		URLs:    make([]string, 0, len(jobs)),
		Errors:  make([]string, 0),
	}

	if len(jobs) == 0 {
		return result, nil
	}

	if maxConcurrent <= 0 {
		maxConcurrent = 3 // 默认并发数
	}

	logger.Infof("Starting batch upload: jobs=%d, concurrent=%d", len(jobs), maxConcurrent)

	// 创建工作通道
	jobChan := make(chan UploadJob, len(jobs))
	resultChan := make(chan struct {
		index  int
		result UploadResult
	}, len(jobs))

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < maxConcurrent; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for job := range jobChan {
				var uploadResult UploadResult

				if job.Data != nil {
					// 上传数据
					res, err := c.UploadData(job.Data, job.Filename)
					if err != nil {
						uploadResult = UploadResult{
							Success: false,
							Error:   err.Error(),
						}
					} else {
						uploadResult = *res
					}
				} else {
					// 上传文件
					res, err := c.UploadFile(job.FilePath)
					if err != nil {
						uploadResult = UploadResult{
							Success: false,
							Error:   err.Error(),
						}
					} else {
						uploadResult = *res
					}
				}

				resultChan <- struct {
					index  int
					result UploadResult
				}{job.Index, uploadResult}
			}
		}()
	}

	// 发送任务
	go func() {
		for _, job := range jobs {
			jobChan <- job
		}
		close(jobChan)
	}()

	// 等待工作协程完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	completed := 0
	for res := range resultChan {
		result.Results[res.index] = res.result

		if res.result.Success {
			result.Success++
			result.URLs = append(result.URLs, res.result.URL)
		} else {
			result.Failed++
			result.Errors = append(result.Errors, res.result.Error)
		}

		completed++
		if progressCallback != nil {
			progressCallback(completed, len(jobs))
		}

		logger.Debugf("Upload completed: %d/%d, success=%t", completed, len(jobs), res.result.Success)
	}

	result.Duration = time.Since(startTime)

	logger.Infof("Batch upload completed: total=%d, success=%d, failed=%d, duration=%v",
		result.Total, result.Success, result.Failed, result.Duration)

	return result, nil
}

// processDataForUpload 处理上传数据，将非图片文件隐藏到PNG中
func (c *Client) processDataForUpload(data []byte, filename string) ([]byte, string) {
	// 检查是否为图片文件
	if c.isImageFile(filename) {
		logger.Debugf("File is already an image, uploading directly: %s", filename)
		return data, filename
	}

	logger.Debugf("Non-image file detected, applying steganography: %s (%d bytes)", filename, len(data))

	// 使用隐写术将数据隐藏到PNG中
	config := steganography.DefaultConfig()
	steganographer := steganography.NewSteganographer(config)

	pngData, err := steganographer.HideDataInPNG(data)
	if err != nil {
		logger.Errorf("Failed to hide data in PNG: %v", err)
		// 如果隐写失败，返回原始数据
		return data, filename
	}

	// 生成PNG文件名
	pngFilename := c.generatePNGFilename(filename)

	logger.Infof("Successfully applied steganography: %s -> %s (%d -> %d bytes)",
		filename, pngFilename, len(data), len(pngData))

	return pngData, pngFilename
}

// isImageFile 检查是否为图片文件
func (c *Client) isImageFile(filename string) bool {
	if filename == "" {
		return false
	}

	ext := strings.ToLower(filepath.Ext(filename))
	imageExtensions := []string{".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp", ".tiff", ".svg"}

	for _, imgExt := range imageExtensions {
		if ext == imgExt {
			return true
		}
	}

	return false
}

// getMimeType 根据文件扩展名获取MIME类型
func (c *Client) getMimeType(filename string) string {
	if filename == "" {
		return "application/octet-stream"
	}

	ext := strings.ToLower(filepath.Ext(filename))
	
	mimeTypes := map[string]string{
		".png":  "image/png",
		".jpg":  "image/jpeg",
		".jpeg": "image/jpeg",
		".gif":  "image/gif",
		".bmp":  "image/bmp",
		".webp": "image/webp",
		".tiff": "image/tiff",
		".svg":  "image/svg+xml",
		".mp4":  "video/mp4",
		".avi":  "video/avi",
		".mov":  "video/quicktime",
		".wmv":  "video/x-ms-wmv",
		".mp3":  "audio/mpeg",
		".wav":  "audio/wav",
		".ogg":  "audio/ogg",
		".m4a":  "audio/mp4",
		".pdf":  "application/pdf",
		".txt":  "text/plain",
		".zip":  "application/zip",
	}

	if mimeType, exists := mimeTypes[ext]; exists {
		return mimeType
	}

	return "application/octet-stream"
}

// generatePNGFilename 生成PNG文件名
func (c *Client) generatePNGFilename(originalFilename string) string {
	// 获取原始文件名（不含扩展名）
	baseName := strings.TrimSuffix(filepath.Base(originalFilename), filepath.Ext(originalFilename))

	// 如果文件名为空，使用默认名称
	if baseName == "" {
		baseName = "file"
	}

	// 生成随机后缀以避免冲突
	randomSuffix := c.generateRandomString(8)

	// 组合文件名
	pngFilename := fmt.Sprintf("%s_%s.png", baseName, randomSuffix)

	logger.Debugf("Generated PNG filename: %s -> %s", originalFilename, pngFilename)
	return pngFilename
}

// generateRandomString 生成随机字符串
func (c *Client) generateRandomString(length int) string {
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳
		return fmt.Sprintf("%x", time.Now().UnixNano())[:length]
	}
	return hex.EncodeToString(bytes)[:length]
} // ExtractDataFromURL 从imgbb URL下载并提取隐藏的数据
func (c *Client) ExtractDataFromURL(imageURL string) ([]byte, error) {
	// 下载图片
	resp, err := c.httpClient.Get(imageURL)
	if err != nil {
		return nil, fmt.Errorf("failed to download image: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download image: status %d", resp.StatusCode)
	}

	// 读取图片数据
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read image data: %w", err)
	}

	// 提取隐藏的数据
	config := steganography.DefaultConfig()
	steganographer := steganography.NewSteganographer(config)

	extractedData, err := steganographer.ExtractDataFromPNG(imageData)
	if err != nil {
		return nil, fmt.Errorf("failed to extract data from PNG: %w", err)
	}

	logger.Debugf("Successfully extracted data from URL: %s (%d bytes)", imageURL, len(extractedData))
	return extractedData, nil
}

// UploadWithSteganography 强制使用隐写术上传数据
func (c *Client) UploadWithSteganography(data []byte, filename string) (*UploadResult, error) {
	logger.Debugf("Force applying steganography: %s (%d bytes)", filename, len(data))

	// 强制使用隐写术
	config := steganography.DefaultConfig()
	steganographer := steganography.NewSteganographer(config)

	pngData, err := steganographer.HideDataInPNG(data)
	if err != nil {
		return nil, fmt.Errorf("failed to hide data in PNG: %w", err)
	}

	// 生成PNG文件名
	pngFilename := c.generatePNGFilename(filename)

	// 上传PNG数据
	return c.UploadData(pngData, pngFilename)
}

// ValidateSteganographyCapability 验证隐写术能力
func (c *Client) ValidateSteganographyCapability(dataSize int) error {
	config := steganography.DefaultConfig()
	steganographer := steganography.NewSteganographer(config)

	// 计算所需图片尺寸
	width, height := steganographer.CalculateRequiredImageSize(dataSize)

	// 检查是否超出合理范围
	maxDimension := 4096 // 4K分辨率
	if width > maxDimension || height > maxDimension {
		return fmt.Errorf("data too large for steganography: requires %dx%d image (max %dx%d)",
			width, height, maxDimension, maxDimension)
	}

	logger.Debugf("Steganography capability validated: %d bytes -> %dx%d image",
		dataSize, width, height)
	return nil
}

// GetSteganographyInfo 获取隐写术信息
func (c *Client) GetSteganographyInfo(dataSize int) *SteganographyInfo {
	config := steganography.DefaultConfig()
	steganographer := steganography.NewSteganographer(config)

	width, height := steganographer.CalculateRequiredImageSize(dataSize)

	return &SteganographyInfo{
		DataSize:         dataSize,
		RequiredWidth:    width,
		RequiredHeight:   height,
		EstimatedPNGSize: c.estimatePNGSize(width, height),
		MaxDataSize:      c.calculateMaxDataSize(width, height),
	}
}

// SteganographyInfo 隐写术信息
type SteganographyInfo struct {
	DataSize         int `json:"data_size"`          // 数据大小
	RequiredWidth    int `json:"required_width"`     // 所需图片宽度
	RequiredHeight   int `json:"required_height"`    // 所需图片高度
	EstimatedPNGSize int `json:"estimated_png_size"` // 预估PNG大小
	MaxDataSize      int `json:"max_data_size"`      // 最大数据大小
}

// estimatePNGSize 估算PNG大小
func (c *Client) estimatePNGSize(width, height int) int {
	// 简单估算：每个像素4字节(RGBA) + PNG头部和压缩开销
	pixelData := width * height * 4
	overhead := 1024 // PNG头部、块信息等开销

	// 考虑压缩率（通常PNG压缩率在30-70%之间）
	compressionRatio := 0.5
	estimatedSize := int(float64(pixelData)*compressionRatio) + overhead

	return estimatedSize
}

// calculateMaxDataSize 计算最大数据大小
func (c *Client) calculateMaxDataSize(width, height int) int {
	// 每个像素可以隐藏3位（RGB各1位）
	totalPixels := width * height
	maxBits := totalPixels * 3
	maxBytes := maxBits / 8

	// 减去长度前缀的4字节
	if maxBytes > 4 {
		maxBytes -= 4
	} else {
		maxBytes = 0
	}

	return maxBytes
}
