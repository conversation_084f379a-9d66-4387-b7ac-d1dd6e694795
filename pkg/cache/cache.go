package cache

import (
	"encoding/json"
	"fmt"
	"time"

	"magnet-downloader/pkg/redis"
)

// Cache 缓存接口
type Cache interface {
	// 基础操作
	Set(key string, value interface{}, expiration time.Duration) error
	Get(key string, dest interface{}) error
	Del(keys ...string) error
	Exists(keys ...string) (int64, error)

	// 过期时间
	Expire(key string, expiration time.Duration) error
	TTL(key string) (time.Duration, error)

	// 哈希操作
	HSet(key string, field string, value interface{}) error
	HGet(key string, field string, dest interface{}) error
	HGetAll(key string) (map[string]string, error)
	HDel(key string, fields ...string) error

	// 计数器
	Incr(key string) (int64, error)
	Decr(key string) (int64, error)
	IncrBy(key string, value int64) (int64, error)

	// 健康检查
	Ping() error
}

// redisCache Redis缓存实现
type redisCache struct{}

// NewRedisCache 创建Redis缓存实例
func NewRedisCache() Cache {
	return &redisCache{}
}

// Set 设置缓存
func (c *redisCache) Set(key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}
	return redis.Set(key, data, expiration)
}

// Get 获取缓存
func (c *redisCache) Get(key string, dest interface{}) error {
	data, err := redis.Get(key)
	if err != nil {
		return err
	}

	return json.Unmarshal([]byte(data), dest)
}

// Del 删除缓存
func (c *redisCache) Del(keys ...string) error {
	return redis.Del(keys...)
}

// Exists 检查键是否存在
func (c *redisCache) Exists(keys ...string) (int64, error) {
	return redis.Exists(keys...)
}

// Expire 设置过期时间
func (c *redisCache) Expire(key string, expiration time.Duration) error {
	return redis.Expire(key, expiration)
}

// TTL 获取剩余生存时间
func (c *redisCache) TTL(key string) (time.Duration, error) {
	return redis.TTL(key)
}

// HSet 设置哈希字段
func (c *redisCache) HSet(key string, field string, value interface{}) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}
	return redis.HSet(key, field, data)
}

// HGet 获取哈希字段值
func (c *redisCache) HGet(key string, field string, dest interface{}) error {
	data, err := redis.HGet(key, field)
	if err != nil {
		return err
	}

	return json.Unmarshal([]byte(data), dest)
}

// HGetAll 获取所有哈希字段
func (c *redisCache) HGetAll(key string) (map[string]string, error) {
	return redis.HGetAll(key)
}

// HDel 删除哈希字段
func (c *redisCache) HDel(key string, fields ...string) error {
	return redis.HDel(key, fields...)
}

// Incr 递增
func (c *redisCache) Incr(key string) (int64, error) {
	return redis.Incr(key)
}

// Decr 递减
func (c *redisCache) Decr(key string) (int64, error) {
	return redis.Decr(key)
}

// IncrBy 按指定值递增
func (c *redisCache) IncrBy(key string, value int64) (int64, error) {
	client := redis.GetClient()
	if client == nil {
		return 0, fmt.Errorf("Redis client not initialized")
	}
	return client.IncrBy(redis.GetContext(), key, value).Result()
}

// Ping 健康检查
func (c *redisCache) Ping() error {
	client := redis.GetClient()
	if client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return client.Ping(redis.GetContext()).Err()
}

// GetContext 获取上下文（辅助函数）
func GetContext() interface{} {
	// 这里返回一个空接口，实际使用时需要类型断言
	return nil
}

// CacheKey 缓存键生成器
type CacheKey struct {
	prefix string
}

// NewCacheKey 创建缓存键生成器
func NewCacheKey(prefix string) *CacheKey {
	return &CacheKey{prefix: prefix}
}

// Key 生成缓存键
func (ck *CacheKey) Key(parts ...string) string {
	key := ck.prefix
	for _, part := range parts {
		key += ":" + part
	}
	return key
}

// 预定义的缓存键前缀
const (
	PrefixUser         = "user"
	PrefixTask         = "task"
	PrefixConfig       = "config"
	PrefixSession      = "session"
	PrefixQueue        = "queue"
	PrefixLock         = "lock"
	PrefixStats        = "stats"
	PrefixProgress     = "progress"
	PrefixNotification = "notification"
)

// 预定义的缓存键生成器
var (
	UserKey         = NewCacheKey(PrefixUser)
	TaskKey         = NewCacheKey(PrefixTask)
	ConfigKey       = NewCacheKey(PrefixConfig)
	SessionKey      = NewCacheKey(PrefixSession)
	QueueKey        = NewCacheKey(PrefixQueue)
	LockKey         = NewCacheKey(PrefixLock)
	StatsKey        = NewCacheKey(PrefixStats)
	ProgressKey     = NewCacheKey(PrefixProgress)
	NotificationKey = NewCacheKey(PrefixNotification)
)

// 常用的缓存过期时间
const (
	ExpirationMinute = time.Minute
	ExpirationHour   = time.Hour
	ExpirationDay    = 24 * time.Hour
	ExpirationWeek   = 7 * 24 * time.Hour
	ExpirationMonth  = 30 * 24 * time.Hour
	ExpirationNever  = 0
)

// TaskProgressCache 任务进度缓存
type TaskProgressCache struct {
	cache Cache
}

// NewTaskProgressCache 创建任务进度缓存
func NewTaskProgressCache(cache Cache) *TaskProgressCache {
	return &TaskProgressCache{cache: cache}
}

// SetProgress 设置任务进度
func (tpc *TaskProgressCache) SetProgress(taskID uint, progress float64) error {
	key := ProgressKey.Key("task", fmt.Sprintf("%d", taskID))
	return tpc.cache.Set(key, progress, ExpirationHour)
}

// GetProgress 获取任务进度
func (tpc *TaskProgressCache) GetProgress(taskID uint) (float64, error) {
	key := ProgressKey.Key("task", fmt.Sprintf("%d", taskID))
	var progress float64
	err := tpc.cache.Get(key, &progress)
	return progress, err
}

// DeleteProgress 删除任务进度
func (tpc *TaskProgressCache) DeleteProgress(taskID uint) error {
	key := ProgressKey.Key("task", fmt.Sprintf("%d", taskID))
	return tpc.cache.Del(key)
}

// UserSessionCache 用户会话缓存
type UserSessionCache struct {
	cache Cache
}

// NewUserSessionCache 创建用户会话缓存
func NewUserSessionCache(cache Cache) *UserSessionCache {
	return &UserSessionCache{cache: cache}
}

// SetSession 设置用户会话
func (usc *UserSessionCache) SetSession(userID uint, sessionData interface{}) error {
	key := SessionKey.Key("user", fmt.Sprintf("%d", userID))
	return usc.cache.Set(key, sessionData, ExpirationDay)
}

// GetSession 获取用户会话
func (usc *UserSessionCache) GetSession(userID uint, dest interface{}) error {
	key := SessionKey.Key("user", fmt.Sprintf("%d", userID))
	return usc.cache.Get(key, dest)
}

// DeleteSession 删除用户会话
func (usc *UserSessionCache) DeleteSession(userID uint) error {
	key := SessionKey.Key("user", fmt.Sprintf("%d", userID))
	return usc.cache.Del(key)
}
