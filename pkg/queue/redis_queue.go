package queue

import (
	"fmt"
	"strconv"
	"time"

	"magnet-downloader/pkg/redis"

	redisClient "github.com/go-redis/redis/v8"
)

// redisQueue Redis队列实现
type redisQueue struct {
	client *redisClient.Client
	prefix string
}

// NewRedisQueue 创建Redis队列
func NewRedisQueue(prefix string) Queue {
	return &redisQueue{
		client: redis.GetClient(),
		prefix: prefix,
	}
}

// getQueueKey 获取队列键
func (rq *redisQueue) getQueueKey(queueName string) string {
	return fmt.Sprintf("%s:queue:%s", rq.prefix, queueName)
}

// getTaskKey 获取任务键
func (rq *redisQueue) getTaskKey(taskID string) string {
	return fmt.Sprintf("%s:task:%s", rq.prefix, taskID)
}

// getDelayedKey 获取延迟队列键
func (rq *redisQueue) getDelayedKey() string {
	return fmt.Sprintf("%s:delayed", rq.prefix)
}

// getStatsKey 获取统计键
func (rq *redisQueue) getStatsKey(queueName string) string {
	return fmt.Sprintf("%s:stats:%s", rq.prefix, queueName)
}

// Enqueue 入队
func (rq *redisQueue) Enqueue(task *Task) error {
	if rq.client == nil {
		return fmt.Errorf("Redis client not initialized")
	}

	// 序列化任务
	taskData, err := task.ToJSON()
	if err != nil {
		return fmt.Errorf("failed to serialize task: %w", err)
	}

	ctx := redis.GetContext()
	pipe := rq.client.TxPipeline()

	// 保存任务数据
	taskKey := rq.getTaskKey(task.ID)
	pipe.Set(ctx, taskKey, taskData, time.Hour*24) // 任务数据保存24小时

	// 根据优先级添加到不同的队列
	queueKey := rq.getQueueKey(fmt.Sprintf("priority_%d", task.Priority))
	pipe.LPush(ctx, queueKey, task.ID)

	// 更新统计信息
	statsKey := rq.getStatsKey("global")
	pipe.HIncrBy(ctx, statsKey, "total_count", 1)
	pipe.HIncrBy(ctx, statsKey, "pending_count", 1)

	_, err = pipe.Exec(ctx)
	return err
}

// Dequeue 出队
func (rq *redisQueue) Dequeue(queueName string) (*Task, error) {
	if rq.client == nil {
		return nil, fmt.Errorf("Redis client not initialized")
	}

	ctx := redis.GetContext()

	// 按优先级顺序检查队列
	priorities := []Priority{PriorityUrgent, PriorityHigh, PriorityNormal, PriorityLow}

	for _, priority := range priorities {
		queueKey := rq.getQueueKey(fmt.Sprintf("priority_%d", priority))

		// 从队列右侧弹出任务ID
		taskID, err := rq.client.RPop(ctx, queueKey).Result()
		if err == redisClient.Nil {
			continue // 队列为空，检查下一个优先级
		}
		if err != nil {
			return nil, err
		}

		// 获取任务数据
		task, err := rq.GetTask(taskID)
		if err != nil {
			continue // 任务不存在，继续下一个
		}

		// 标记任务为处理中
		task.MarkAsProcessing()
		if err := rq.UpdateTask(task); err != nil {
			return nil, err
		}

		// 更新统计信息
		statsKey := rq.getStatsKey("global")
		pipe := rq.client.TxPipeline()
		pipe.HIncrBy(ctx, statsKey, "pending_count", -1)
		pipe.HIncrBy(ctx, statsKey, "processing_count", 1)
		pipe.Exec(ctx)

		return task, nil
	}

	return nil, fmt.Errorf("no tasks available")
}

// DequeueBlocking 阻塞出队
func (rq *redisQueue) DequeueBlocking(queueName string, timeout time.Duration) (*Task, error) {
	if rq.client == nil {
		return nil, fmt.Errorf("Redis client not initialized")
	}

	ctx := redis.GetContext()

	// 构建所有优先级队列的键
	var queueKeys []string
	priorities := []Priority{PriorityUrgent, PriorityHigh, PriorityNormal, PriorityLow}
	for _, priority := range priorities {
		queueKey := rq.getQueueKey(fmt.Sprintf("priority_%d", priority))
		queueKeys = append(queueKeys, queueKey)
	}

	// 阻塞弹出
	result, err := rq.client.BRPop(ctx, timeout, queueKeys...).Result()
	if err != nil {
		return nil, err
	}

	if len(result) < 2 {
		return nil, fmt.Errorf("invalid result from BRPop")
	}

	taskID := result[1]

	// 获取任务数据
	task, err := rq.GetTask(taskID)
	if err != nil {
		return nil, err
	}

	// 标记任务为处理中
	task.MarkAsProcessing()
	if err := rq.UpdateTask(task); err != nil {
		return nil, err
	}

	return task, nil
}

// GetTask 获取任务
func (rq *redisQueue) GetTask(taskID string) (*Task, error) {
	if rq.client == nil {
		return nil, fmt.Errorf("Redis client not initialized")
	}

	ctx := redis.GetContext()
	taskKey := rq.getTaskKey(taskID)

	taskData, err := rq.client.Get(ctx, taskKey).Result()
	if err != nil {
		return nil, err
	}

	var task Task
	if err := task.FromJSON(taskData); err != nil {
		return nil, fmt.Errorf("failed to deserialize task: %w", err)
	}

	return &task, nil
}

// UpdateTask 更新任务
func (rq *redisQueue) UpdateTask(task *Task) error {
	if rq.client == nil {
		return fmt.Errorf("Redis client not initialized")
	}

	taskData, err := task.ToJSON()
	if err != nil {
		return fmt.Errorf("failed to serialize task: %w", err)
	}

	ctx := redis.GetContext()
	taskKey := rq.getTaskKey(task.ID)

	return rq.client.Set(ctx, taskKey, taskData, time.Hour*24).Err()
}

// DeleteTask 删除任务
func (rq *redisQueue) DeleteTask(taskID string) error {
	if rq.client == nil {
		return fmt.Errorf("Redis client not initialized")
	}

	ctx := redis.GetContext()
	taskKey := rq.getTaskKey(taskID)

	return rq.client.Del(ctx, taskKey).Err()
}

// GetQueueLength 获取队列长度
func (rq *redisQueue) GetQueueLength(queueName string) (int64, error) {
	if rq.client == nil {
		return 0, fmt.Errorf("Redis client not initialized")
	}

	ctx := redis.GetContext()
	queueKey := rq.getQueueKey(queueName)

	return rq.client.LLen(ctx, queueKey).Result()
}

// GetQueueStats 获取队列统计
func (rq *redisQueue) GetQueueStats(queueName string) (*QueueStats, error) {
	if rq.client == nil {
		return nil, fmt.Errorf("Redis client not initialized")
	}

	ctx := redis.GetContext()
	statsKey := rq.getStatsKey(queueName)

	stats, err := rq.client.HGetAll(ctx, statsKey).Result()
	if err != nil {
		return nil, err
	}

	queueStats := &QueueStats{
		QueueName:   queueName,
		LastUpdated: time.Now(),
	}

	// 解析统计数据
	if val, ok := stats["pending_count"]; ok {
		if count, err := strconv.ParseInt(val, 10, 64); err == nil {
			queueStats.PendingCount = count
		}
	}
	if val, ok := stats["processing_count"]; ok {
		if count, err := strconv.ParseInt(val, 10, 64); err == nil {
			queueStats.ProcessingCount = count
		}
	}
	if val, ok := stats["completed_count"]; ok {
		if count, err := strconv.ParseInt(val, 10, 64); err == nil {
			queueStats.CompletedCount = count
		}
	}
	if val, ok := stats["failed_count"]; ok {
		if count, err := strconv.ParseInt(val, 10, 64); err == nil {
			queueStats.FailedCount = count
		}
	}
	if val, ok := stats["total_count"]; ok {
		if count, err := strconv.ParseInt(val, 10, 64); err == nil {
			queueStats.TotalCount = count
		}
	}

	return queueStats, nil
}

// PurgeQueue 清空队列
func (rq *redisQueue) PurgeQueue(queueName string) error {
	if rq.client == nil {
		return fmt.Errorf("Redis client not initialized")
	}

	ctx := redis.GetContext()
	queueKey := rq.getQueueKey(queueName)

	return rq.client.Del(ctx, queueKey).Err()
}

// EnqueueDelayed 延迟入队
func (rq *redisQueue) EnqueueDelayed(task *Task, delay time.Duration) error {
	if rq.client == nil {
		return fmt.Errorf("Redis client not initialized")
	}

	// 设置计划执行时间
	scheduledAt := time.Now().Add(delay)
	task.ScheduledAt = &scheduledAt

	// 序列化任务
	taskData, err := task.ToJSON()
	if err != nil {
		return fmt.Errorf("failed to serialize task: %w", err)
	}

	ctx := redis.GetContext()
	pipe := rq.client.TxPipeline()

	// 保存任务数据
	taskKey := rq.getTaskKey(task.ID)
	pipe.Set(ctx, taskKey, taskData, time.Hour*24)

	// 添加到延迟队列（有序集合，按时间排序）
	delayedKey := rq.getDelayedKey()
	score := float64(scheduledAt.Unix())
	pipe.ZAdd(ctx, delayedKey, &redisClient.Z{
		Score:  score,
		Member: task.ID,
	})

	_, err = pipe.Exec(ctx)
	return err
}

// ProcessDelayedTasks 处理延迟任务
func (rq *redisQueue) ProcessDelayedTasks() error {
	if rq.client == nil {
		return fmt.Errorf("Redis client not initialized")
	}

	ctx := redis.GetContext()
	delayedKey := rq.getDelayedKey()
	now := float64(time.Now().Unix())

	// 获取到期的任务
	taskIDs, err := rq.client.ZRangeByScore(ctx, delayedKey, &redisClient.ZRangeBy{
		Min: "0",
		Max: fmt.Sprintf("%.0f", now),
	}).Result()

	if err != nil {
		return err
	}

	// 处理每个到期任务
	for _, taskID := range taskIDs {
		task, err := rq.GetTask(taskID)
		if err != nil {
			continue
		}

		// 将任务移到正常队列
		if err := rq.Enqueue(task); err != nil {
			continue
		}

		// 从延迟队列中移除
		rq.client.ZRem(ctx, delayedKey, taskID)
	}

	return nil
}

// RetryTask 重试任务
func (rq *redisQueue) RetryTask(taskID string) error {
	task, err := rq.GetTask(taskID)
	if err != nil {
		return err
	}

	if !task.CanRetry() {
		return fmt.Errorf("task cannot be retried")
	}

	task.MarkAsRetrying()
	if err := rq.UpdateTask(task); err != nil {
		return err
	}

	return rq.Enqueue(task)
}

// GetFailedTasks 获取失败任务
func (rq *redisQueue) GetFailedTasks(limit int) ([]*Task, error) {
	// 这里需要实现一个失败任务的索引机制
	// 为简化，暂时返回空列表
	return []*Task{}, nil
}

// Ping 健康检查
func (rq *redisQueue) Ping() error {
	if rq.client == nil {
		return fmt.Errorf("Redis client not initialized")
	}
	return rq.client.Ping(redis.GetContext()).Err()
}

// Close 关闭连接
func (rq *redisQueue) Close() error {
	// Redis连接由全局客户端管理，这里不需要关闭
	return nil
}
