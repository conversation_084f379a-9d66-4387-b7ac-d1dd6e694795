package queue

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"time"
)

// Priority 任务优先级
type Priority int

const (
	PriorityLow    Priority = 1 // 低优先级
	PriorityNormal Priority = 2 // 普通优先级
	PriorityHigh   Priority = 3 // 高优先级
	PriorityUrgent Priority = 4 // 紧急优先级
)

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusPending    TaskStatus = "pending"    // 等待中
	TaskStatusProcessing TaskStatus = "processing" // 处理中
	TaskStatusCompleted  TaskStatus = "completed"  // 已完成
	TaskStatusFailed     TaskStatus = "failed"     // 失败
	TaskStatusRetrying   TaskStatus = "retrying"   // 重试中
)

// Task 队列任务
type Task struct {
	ID          string                 `json:"id"`           // 任务ID
	Type        string                 `json:"type"`         // 任务类型
	Priority    Priority               `json:"priority"`     // 优先级
	Status      TaskStatus             `json:"status"`       // 状态
	Payload     map[string]interface{} `json:"payload"`      // 任务数据
	RetryCount  int                    `json:"retry_count"`  // 重试次数
	MaxRetries  int                    `json:"max_retries"`  // 最大重试次数
	CreatedAt   time.Time              `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time              `json:"updated_at"`   // 更新时间
	ScheduledAt *time.Time             `json:"scheduled_at"` // 计划执行时间
	StartedAt   *time.Time             `json:"started_at"`   // 开始时间
	CompletedAt *time.Time             `json:"completed_at"` // 完成时间
	ErrorMsg    string                 `json:"error_msg"`    // 错误信息
}

// ToJSON 转换为JSON字符串
func (t *Task) ToJSON() (string, error) {
	data, err := json.Marshal(t)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析
func (t *Task) FromJSON(data string) error {
	return json.Unmarshal([]byte(data), t)
}

// CanRetry 检查是否可以重试
func (t *Task) CanRetry() bool {
	return t.Status == TaskStatusFailed && t.RetryCount < t.MaxRetries
}

// MarkAsProcessing 标记为处理中
func (t *Task) MarkAsProcessing() {
	t.Status = TaskStatusProcessing
	now := time.Now()
	t.StartedAt = &now
	t.UpdatedAt = now
}

// MarkAsCompleted 标记为已完成
func (t *Task) MarkAsCompleted() {
	t.Status = TaskStatusCompleted
	now := time.Now()
	t.CompletedAt = &now
	t.UpdatedAt = now
}

// MarkAsFailed 标记为失败
func (t *Task) MarkAsFailed(errorMsg string) {
	t.Status = TaskStatusFailed
	t.ErrorMsg = errorMsg
	t.UpdatedAt = time.Now()
}

// MarkAsRetrying 标记为重试中
func (t *Task) MarkAsRetrying() {
	t.Status = TaskStatusRetrying
	t.RetryCount++
	t.UpdatedAt = time.Now()
}

// Queue 队列接口
type Queue interface {
	// 基础操作
	Enqueue(task *Task) error                                               // 入队
	Dequeue(queueName string) (*Task, error)                                // 出队
	DequeueBlocking(queueName string, timeout time.Duration) (*Task, error) // 阻塞出队

	// 任务管理
	GetTask(taskID string) (*Task, error) // 获取任务
	UpdateTask(task *Task) error          // 更新任务
	DeleteTask(taskID string) error       // 删除任务

	// 队列管理
	GetQueueLength(queueName string) (int64, error)      // 获取队列长度
	GetQueueStats(queueName string) (*QueueStats, error) // 获取队列统计
	PurgeQueue(queueName string) error                   // 清空队列

	// 延迟任务
	EnqueueDelayed(task *Task, delay time.Duration) error // 延迟入队
	ProcessDelayedTasks() error                           // 处理延迟任务

	// 重试机制
	RetryTask(taskID string) error             // 重试任务
	GetFailedTasks(limit int) ([]*Task, error) // 获取失败任务

	// 健康检查
	Ping() error  // 健康检查
	Close() error // 关闭连接
}

// QueueStats 队列统计信息
type QueueStats struct {
	QueueName       string    `json:"queue_name"`       // 队列名称
	PendingCount    int64     `json:"pending_count"`    // 等待任务数
	ProcessingCount int64     `json:"processing_count"` // 处理中任务数
	CompletedCount  int64     `json:"completed_count"`  // 已完成任务数
	FailedCount     int64     `json:"failed_count"`     // 失败任务数
	TotalCount      int64     `json:"total_count"`      // 总任务数
	LastUpdated     time.Time `json:"last_updated"`     // 最后更新时间
}

// TaskHandler 任务处理器接口
type TaskHandler interface {
	Handle(task *Task) error // 处理任务
	GetTaskType() string     // 获取任务类型
}

// Worker 工作者接口
type Worker interface {
	Start() error                              // 启动工作者
	Stop() error                               // 停止工作者
	RegisterHandler(handler TaskHandler) error // 注册任务处理器
	GetStats() *WorkerStats                    // 获取工作者统计
}

// WorkerStats 工作者统计信息
type WorkerStats struct {
	WorkerID       string     `json:"worker_id"`       // 工作者ID
	Status         string     `json:"status"`          // 状态
	ProcessedCount int64      `json:"processed_count"` // 已处理任务数
	FailedCount    int64      `json:"failed_count"`    // 失败任务数
	StartTime      time.Time  `json:"start_time"`      // 启动时间
	LastTaskTime   *time.Time `json:"last_task_time"`  // 最后处理任务时间
}

// 预定义的队列名称
const (
	QueueDownload       = "download"        // 下载队列
	QueueNotification   = "notification"    // 通知队列
	QueueCleanup        = "cleanup"         // 清理队列
	QueueRetry          = "retry"           // 重试队列
	QueueDelayed        = "delayed"         // 延迟队列
	QueueFileProcessing = "file_processing" // 文件处理队列
)

// 预定义的任务类型
const (
	TaskTypeDownload       = "download"        // 下载任务
	TaskTypeNotification   = "notification"    // 通知任务
	TaskTypeCleanup        = "cleanup"         // 清理任务
	TaskTypeRetry          = "retry"           // 重试任务
	TaskTypeFileProcessing = "file_processing" // 文件处理任务
	TaskTypeChunkUpload    = "chunk_upload"    // 分片上传任务
	TaskTypePlaylistGen    = "playlist_gen"    // 播放列表生成任务
)

// NewTask 创建新任务
func NewTask(taskType string, priority Priority, payload map[string]interface{}) *Task {
	now := time.Now()
	return &Task{
		ID:         generateTaskID(),
		Type:       taskType,
		Priority:   priority,
		Status:     TaskStatusPending,
		Payload:    payload,
		RetryCount: 0,
		MaxRetries: 3,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
}

// generateTaskID 生成任务ID
func generateTaskID() string {
	// 使用时间戳和随机数生成唯一ID
	return fmt.Sprintf("%d_%d", time.Now().UnixNano(), rand.Intn(10000))
}

// TaskBuilder 任务构建器
type TaskBuilder struct {
	task *Task
}

// NewTaskBuilder 创建任务构建器
func NewTaskBuilder(taskType string) *TaskBuilder {
	return &TaskBuilder{
		task: &Task{
			ID:         generateTaskID(),
			Type:       taskType,
			Priority:   PriorityNormal,
			Status:     TaskStatusPending,
			Payload:    make(map[string]interface{}),
			RetryCount: 0,
			MaxRetries: 3,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		},
	}
}

// WithPriority 设置优先级
func (tb *TaskBuilder) WithPriority(priority Priority) *TaskBuilder {
	tb.task.Priority = priority
	return tb
}

// WithPayload 设置载荷
func (tb *TaskBuilder) WithPayload(payload map[string]interface{}) *TaskBuilder {
	tb.task.Payload = payload
	return tb
}

// WithMaxRetries 设置最大重试次数
func (tb *TaskBuilder) WithMaxRetries(maxRetries int) *TaskBuilder {
	tb.task.MaxRetries = maxRetries
	return tb
}

// WithScheduledAt 设置计划执行时间
func (tb *TaskBuilder) WithScheduledAt(scheduledAt time.Time) *TaskBuilder {
	tb.task.ScheduledAt = &scheduledAt
	return tb
}

// Build 构建任务
func (tb *TaskBuilder) Build() *Task {
	return tb.task
}
