# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
build/
dist/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/

# Configuration files with sensitive data
config.yaml
config.yml
config.json
.env
.env.local
.env.production

# Downloaded files
downloads/
temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Air live reload
tmp/

# Coverage files
coverage.out
coverage.html

# Docker files
docker-compose.override.yml

# Certificates
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup
