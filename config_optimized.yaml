# DoodStream优化配置文件
# 基于性能测试结果的最优配置

server:
  host: "0.0.0.0"
  port: 8080
  mode: "production"
  read_timeout: 30
  write_timeout: 30

database:
  host: "localhost"
  port: 5432
  username: "postgres"
  password: "password"
  database: "magnet_downloader"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 10
  conn_max_lifetime: 300

jwt:
  secret: "your-secret-key"
  access_token_duration: 3600
  refresh_token_duration: 86400

# 文件处理优化配置
file_processing:
  # 上传服务提供商 (imgbb/doodstream)
  upload_provider: "doodstream"
  
  # 自动处理配置
  auto_start_processing: true
  max_concurrent_uploads: 6  # 基于性能测试的最优值
  
  # 工作目录配置
  work_dir: "/tmp/magnet_processing"
  keep_original: false
  
  # 分片配置 (仅用于imgbb模式)
  chunk_size_mb: 10
  encryption_enabled: true
  
  # DoodStream配置 (优化后)
  doodstream:
    api_key: "${DOODSTREAM_API_KEY}"
    base_url: "https://doodapi.co"
    timeout: 300  # 5分钟超时，适合大文件
    max_retries: 3
    
    # 性能优化参数
    rate_limit_interval: 50  # 50ms间隔 (20请求/秒)
    connection_pool_size: 10
    keep_alive_timeout: 30
    
    # 文件大小分级配置
    size_based_concurrency:
      enabled: true
      small_file_threshold: 52428800    # 50MB
      medium_file_threshold: 209715200  # 200MB
      small_file_concurrency: 8         # 小文件高并发
      medium_file_concurrency: 6        # 中等文件中等并发
      large_file_concurrency: 4         # 大文件低并发
  
  # ImgBB配置 (备用)
  imgbb:
    api_key: ""
    base_url: "http://localhost:3000"
    timeout: 30
    max_retries: 3
    batch_size: 10
    rate_limit_delay: 100  # 100ms延迟
  
  # 播放列表配置
  playlist:
    playlist_type: "hls"
    segment_duration: 10
    output_format: "m3u8"
  
  # MixFile配置 (仅用于imgbb模式)
  mixfile:
    enabled: false  # DoodStream模式下禁用
    share_code_prefix: "MF"
    max_share_code_length: 12
    index_upload_retries: 3
    enable_index_compression: true

# Aria2配置
aria2:
  rpc_url: "http://localhost:6800/jsonrpc"
  rpc_secret: ""
  download_dir: "/downloads"
  max_concurrent_downloads: 5
  max_connection_per_server: 16
  split: 16
  min_split_size: "10M"
  max_download_limit: "0"
  max_upload_limit: "0"
  
  # 性能优化
  disk_cache: "64M"
  file_allocation: "falloc"
  continue_partial: true
  auto_save_interval: 60

# WebSocket配置
websocket:
  read_buffer_size: 1024
  write_buffer_size: 1024
  handshake_timeout: 10
  ping_period: 54
  pong_wait: 60
  max_message_size: 512

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file_path: "logs/app.log"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true

# 性能监控配置
monitoring:
  enabled: true
  metrics_interval: 30  # 30秒收集一次指标
  
  # 告警阈值
  alerts:
    error_rate_threshold: 5.0      # 错误率超过5%告警
    response_time_threshold: 10000 # 响应时间超过10秒告警 (ms)
    bandwidth_utilization: 90.0    # 带宽利用率超过90%告警
    memory_usage_threshold: 80.0   # 内存使用率超过80%告警
    cpu_usage_threshold: 85.0      # CPU使用率超过85%告警
  
  # 性能指标收集
  metrics:
    collect_bandwidth: true
    collect_response_times: true
    collect_error_rates: true
    collect_system_stats: true

# 缓存配置
cache:
  redis:
    enabled: false
    host: "localhost"
    port: 6379
    password: ""
    database: 0
    pool_size: 10
  
  # 内存缓存
  memory:
    enabled: true
    max_size: 1000
    ttl: 3600  # 1小时

# 安全配置
security:
  cors:
    enabled: true
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]
    allow_credentials: true
  
  rate_limiting:
    enabled: true
    requests_per_minute: 100
    burst_size: 20

# 优化建议注释
# 
# 1. 并发配置优化:
#    - max_concurrent_uploads: 6 (基于性能测试结果)
#    - 文件大小分级并发处理
#    - 动态速率限制调整
#
# 2. 超时配置优化:
#    - DoodStream timeout: 300s (适合大文件上传)
#    - 连接保持时间: 30s
#    - 重试机制: 3次
#
# 3. 性能监控:
#    - 启用实时监控
#    - 设置合理的告警阈值
#    - 收集关键性能指标
#
# 4. 带宽利用优化:
#    - 2.5G带宽环境下的最优配置
#    - 预期带宽利用率: 60-80%
#    - 预期上传速度提升: 30-50%