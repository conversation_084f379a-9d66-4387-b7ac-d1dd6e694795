# 更新日志

本文档记录了磁力下载器项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 初始项目架构设计
- 完整的技术栈选型和实现

## [1.0.0] - 2024-01-01

### 新增

#### 🚀 核心功能
- **磁力链接下载**: 支持磁力链接和种子文件下载
- **任务管理**: 完整的任务CRUD操作，支持暂停、恢复、取消
- **实时监控**: WebSocket实时推送下载进度和状态更新
- **用户系统**: 多用户支持，基于JWT的认证和权限控制
- **Web界面**: 现代化的React前端管理界面

#### 🔧 技术特性
- **高性能后端**: 基于Go 1.21 + Gin框架
- **下载引擎**: 集成aria2c，支持高并发下载
- **数据存储**: PostgreSQL主数据库 + Redis缓存
- **实时通信**: WebSocket双向通信
- **任务调度**: 基于robfig/cron的定时任务系统

#### 🛡️ 安全特性
- **JWT认证**: 安全的用户认证机制
- **权限控制**: 基于角色的访问控制(RBAC)
- **数据加密**: 敏感数据加密存储
- **API限流**: 请求频率限制和防护

#### 📊 监控告警
- **指标收集**: Prometheus指标收集
- **可视化**: Grafana监控仪表板
- **告警管理**: AlertManager告警通知
- **健康检查**: 完整的服务健康检查机制

#### 🐳 容器化部署
- **Docker支持**: 多阶段Dockerfile优化
- **服务编排**: Docker Compose完整服务栈
- **Kubernetes**: K8s部署配置支持
- **环境分离**: 开发、测试、生产环境配置

#### 🛠️ 开发工具
- **热重载**: Air热重载开发环境
- **代码质量**: golangci-lint代码检查
- **自动化测试**: 单元测试和集成测试
- **API文档**: 完整的RESTful API文档

### API端点

#### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh` - 刷新Token

#### 任务管理
- `GET /api/v1/tasks` - 获取任务列表
- `POST /api/v1/tasks` - 创建下载任务
- `GET /api/v1/tasks/:id` - 获取任务详情
- `PUT /api/v1/tasks/:id` - 更新任务信息
- `DELETE /api/v1/tasks/:id` - 删除任务
- `POST /api/v1/tasks/:id/start` - 开始任务
- `POST /api/v1/tasks/:id/pause` - 暂停任务
- `POST /api/v1/tasks/:id/resume` - 恢复任务
- `POST /api/v1/tasks/:id/cancel` - 取消任务
- `POST /api/v1/tasks/:id/retry` - 重试任务
- `GET /api/v1/tasks/stats` - 获取任务统计

#### 用户管理
- `GET /api/v1/users/profile` - 获取用户资料
- `PUT /api/v1/users/profile` - 更新用户资料
- `PUT /api/v1/users/password` - 修改密码

#### 管理员功能
- `GET /api/v1/admin/users` - 用户列表管理
- `POST /api/v1/admin/users` - 创建用户
- `PUT /api/v1/admin/users/:id` - 更新用户
- `DELETE /api/v1/admin/users/:id` - 删除用户
- `GET /api/v1/admin/config` - 系统配置管理
- `PUT /api/v1/admin/config/:key` - 更新配置
- `GET /api/v1/admin/scheduler/status` - 调度器状态
- `GET /api/v1/admin/scheduler/jobs` - 调度任务列表

#### WebSocket
- `GET /api/v1/ws` - WebSocket连接端点

### 数据库模型

#### 用户表 (users)
- 用户基本信息、角色权限、登录状态

#### 下载任务表 (download_tasks)
- 任务详情、状态跟踪、进度监控

#### 系统配置表 (system_configs)
- 系统参数配置、动态配置管理

#### 用户会话表 (user_sessions)
- JWT会话管理、安全控制

### 监控指标

#### HTTP指标
- 请求总数、响应时间、错误率
- 并发连接数、请求分布

#### 业务指标
- 任务数量统计、下载速度监控
- 成功率分析、队列长度监控

#### 系统指标
- CPU、内存、磁盘使用率
- Goroutine数量、GC性能

#### 数据库指标
- 连接池状态、查询性能
- 慢查询监控、事务统计

### 部署配置

#### Docker Compose
- 生产环境完整服务栈
- 开发环境调试配置
- 监控栈集成部署

#### Kubernetes
- Namespace资源隔离
- ConfigMap配置管理
- Service服务发现

#### 环境配置
- 多环境变量管理
- 安全配置模板
- 自动化部署脚本

### 文档

#### 用户文档
- README.md - 项目介绍和快速开始
- docs/api.md - 完整API接口文档
- docs/deployment.md - 部署指南

#### 开发文档
- docs/development.md - 开发指南
- 代码注释和示例
- 架构设计文档

### 依赖项

#### 后端依赖
- Go 1.21+
- Gin Web框架
- GORM ORM框架
- PostgreSQL驱动
- Redis客户端
- JWT认证库
- Prometheus客户端
- robfig/cron调度器

#### 前端依赖
- React 18
- TypeScript
- Ant Design
- Axios HTTP客户端
- React Router

#### 基础设施
- PostgreSQL 12+
- Redis 6+
- aria2c下载引擎
- Nginx反向代理

### 已知问题
- 无已知重大问题

### 安全更新
- 所有依赖项已更新到最新稳定版本
- 实施了安全的认证和授权机制
- 配置了适当的CORS和安全头

---

## 版本说明

### 版本号规则
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 发布周期
- **主版本**: 根据重大功能更新发布
- **次版本**: 每月发布，包含新功能和改进
- **修订版**: 根据bug修复需要随时发布

### 支持政策
- **当前版本**: 完全支持，包含新功能和bug修复
- **前一个主版本**: 安全更新和重要bug修复
- **更早版本**: 仅提供安全更新

---

## 贡献者

感谢所有为本项目做出贡献的开发者！

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。
