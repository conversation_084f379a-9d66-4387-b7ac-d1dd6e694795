package main

import (
	"fmt"
	"os"
	"path/filepath"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/fileprocessor"
)

func main() {
	fmt.Println("🧪 测试自动上传服务配置")
	fmt.Println("========================")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	// 检查自动上传配置
	fmt.Println("⚙️ 自动上传配置:")
	fmt.Printf("   启用状态: %t\n", cfg.FileProcessing.AutoUpload.Enabled)
	fmt.Printf("   扫描间隔: %d 分钟\n", cfg.FileProcessing.AutoUpload.ScanInterval)
	fmt.Printf("   最大并发: %d\n", cfg.FileProcessing.AutoUpload.MaxConcurrentUploads)
	fmt.Printf("   最小文件大小: %.2f MB\n", float64(cfg.FileProcessing.AutoUpload.MinFileSize)/(1024*1024))
	fmt.Printf("   跳过已存在文件: %t\n", cfg.FileProcessing.AutoUpload.SkipExistingFiles)
	fmt.Printf("   上传后删除: %t\n", cfg.FileProcessing.AutoUpload.DeleteAfterUpload)

	// 检查上传提供商
	fmt.Printf("\n📤 上传提供商: %s\n", cfg.FileProcessing.UploadProvider)

	if cfg.FileProcessing.UploadProvider == "doodstream" {
		fmt.Println("🎬 DoodStream配置:")
		fmt.Printf("   API地址: %s\n", cfg.FileProcessing.DoodStream.BaseURL)
		fmt.Printf("   超时时间: %d 秒\n", cfg.FileProcessing.DoodStream.Timeout)
		fmt.Printf("   最大重试: %d 次\n", cfg.FileProcessing.DoodStream.MaxRetries)
		
		if cfg.FileProcessing.DoodStream.APIKey != "" {
			fmt.Printf("   API密钥: %s...%s (已配置)\n", 
				cfg.FileProcessing.DoodStream.APIKey[:8], 
				cfg.FileProcessing.DoodStream.APIKey[len(cfg.FileProcessing.DoodStream.APIKey)-4:])
		} else {
			fmt.Printf("   API密钥: ❌ 未配置\n")
		}
	}

	// 检查下载目录
	downloadDir := "/www/wwwroot/JAVAPI.COM/downloads"
	if envDir := os.Getenv("DOWNLOAD_DIR"); envDir != "" {
		downloadDir = envDir
	}

	fmt.Printf("\n📁 下载目录: %s\n", downloadDir)

	// 检查目录是否存在
	if _, err := os.Stat(downloadDir); os.IsNotExist(err) {
		fmt.Printf("   状态: ❌ 目录不存在\n")
		fmt.Printf("   建议: 创建目录 mkdir -p %s\n", downloadDir)
	} else {
		fmt.Printf("   状态: ✅ 目录存在\n")
		
		// 扫描现有文件
		videoSelector := fileprocessor.NewVideoSelector()
		videos, err := videoSelector.SelectVideosFromDirectory(downloadDir)
		if err != nil {
			fmt.Printf("   扫描失败: %v\n", err)
		} else {
			fmt.Printf("   发现视频: %d 个\n", len(videos))
			
			if len(videos) > 0 {
				fmt.Println("\n🎬 发现的视频文件:")
				for i, video := range videos {
					fmt.Printf("   %d. %s\n", i+1, video.Name)
					fmt.Printf("      路径: %s\n", video.Path)
					fmt.Printf("      大小: %.2f MB\n", float64(video.Size)/(1024*1024))
					fmt.Printf("      任务目录: %s\n", video.TaskDir)
					if video.IsSegment {
						fmt.Printf("      类型: 分段文件 (索引: %d)\n", video.SegmentIndex)
					} else {
						fmt.Printf("      类型: 单个文件\n")
					}
					fmt.Println()
				}
			}
		}
	}

	// 检查必要的目录结构
	fmt.Println("📂 目录结构检查:")
	
	requiredDirs := []string{
		downloadDir,
		"logs",
		"config.yaml",
	}

	for _, item := range requiredDirs {
		if item == "config.yaml" {
			if _, err := os.Stat(item); err == nil {
				fmt.Printf("   ✅ %s\n", item)
			} else {
				fmt.Printf("   ❌ %s (文件不存在)\n", item)
			}
		} else {
			if _, err := os.Stat(item); err == nil {
				fmt.Printf("   ✅ %s/\n", item)
			} else {
				fmt.Printf("   ❌ %s/ (目录不存在)\n", item)
			}
		}
	}

	// 生成启动建议
	fmt.Println("\n🚀 启动建议:")
	
	if !cfg.FileProcessing.AutoUpload.Enabled {
		fmt.Println("   ❌ 自动上传未启用，请在config.yaml中设置:")
		fmt.Println("      file_processing:")
		fmt.Println("        auto_upload:")
		fmt.Println("          enabled: true")
	}

	if cfg.FileProcessing.UploadProvider != "doodstream" {
		fmt.Println("   ❌ 上传提供商不是DoodStream，请设置:")
		fmt.Println("      file_processing:")
		fmt.Println("        upload_provider: \"doodstream\"")
	}

	if cfg.FileProcessing.DoodStream.APIKey == "" {
		fmt.Println("   ❌ DoodStream API密钥未配置，请设置:")
		fmt.Println("      export DOODSTREAM_API_KEY=\"your_api_key\"")
		fmt.Println("      或在config.yaml中配置")
	}

	if _, err := os.Stat(downloadDir); os.IsNotExist(err) {
		fmt.Printf("   ❌ 下载目录不存在，请创建: mkdir -p %s\n", downloadDir)
	}

	// 检查是否可以启动
	canStart := cfg.FileProcessing.AutoUpload.Enabled && 
		cfg.FileProcessing.UploadProvider == "doodstream" &&
		cfg.FileProcessing.DoodStream.APIKey != ""

	fmt.Println("\n📊 启动检查结果:")
	if canStart {
		fmt.Println("   ✅ 配置完整，可以启动自动上传服务")
		fmt.Println("   🚀 运行命令: ./scripts/start_auto_upload.sh")
	} else {
		fmt.Println("   ❌ 配置不完整，请先完成上述配置")
	}

	fmt.Println("\n💡 使用提示:")
	fmt.Printf("   1. 将下载的视频文件放入 %s 的子目录中\n", downloadDir)
	fmt.Println("   2. 系统会每5分钟自动扫描一次")
	fmt.Println("   3. 发现视频文件后自动上传到DoodStream")
	fmt.Println("   4. 上传完成后自动删除原文件夹")
	fmt.Println("   5. 通过API或WebSocket监控上传状态")

	fmt.Println("\n✅ 配置检查完成！")
}

// createTestFiles 创建测试文件（如果需要）
func createTestFiles(downloadDir string) {
	testDir := filepath.Join(downloadDir, "test_movie")
	if err := os.MkdirAll(testDir, 0755); err != nil {
		return
	}

	// 创建一个测试视频文件
	testFile := filepath.Join(testDir, "test_video.mp4")
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		// 创建一个15MB的测试文件
		file, err := os.Create(testFile)
		if err != nil {
			return
		}
		defer file.Close()

		// 写入15MB的数据
		data := make([]byte, 1024*1024) // 1MB
		for i := 0; i < 15; i++ {
			file.Write(data)
		}

		fmt.Printf("   📁 创建测试文件: %s (15MB)\n", testFile)
	}
}