<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Imgur认证成功</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success-icon {
            font-size: 48px;
            color: #28a745;
            margin-bottom: 20px;
        }
        .token-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
        .token-value {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
        .close-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
        .close-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        <h1>{{.message}}</h1>
        <p>您的Imgur应用已成功授权，现在可以开始上传文件了。</p>
        
        <div class="token-info">
            <h3>访问令牌信息：</h3>
            <p><strong>访问令牌:</strong></p>
            <div class="token-value">{{.access_token}}</div>
            <p><strong>令牌类型:</strong> {{.token_type}}</p>
            <p><strong>有效期:</strong> {{.expires_in}} 秒</p>
        </div>
        
        <p><strong>下一步操作：</strong></p>
        <ol style="text-align: left;">
            <li>复制上面的访问令牌</li>
            <li>在您的应用中配置此令牌</li>
            <li>开始使用Imgur上传服务</li>
        </ol>
        
        <button class="close-btn" onclick="window.close()">关闭窗口</button>
    </div>
    
    <script>
        // 自动复制访问令牌到剪贴板
        function copyToken() {
            const token = "{{.access_token}}";
            navigator.clipboard.writeText(token).then(function() {
                alert('访问令牌已复制到剪贴板！');
            });
        }
        
        // 页面加载完成后自动复制令牌
        window.onload = function() {
            setTimeout(copyToken, 1000);
        };
    </script>
</body>
</html>