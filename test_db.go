package main

import (
	"fmt"
	"log"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/database"
)

func main() {
	fmt.Println("🧪 测试数据库连接和迁移")
	fmt.Println("========================")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	fmt.Printf("📊 数据库配置:\n")
	fmt.Printf("   主机: %s:%d\n", cfg.Database.Host, cfg.Database.Port)
	fmt.Printf("   数据库: %s\n", cfg.Database.DBName)
	fmt.Printf("   用户: %s\n", cfg.Database.User)

	// 初始化数据库
	fmt.Println("\n🔌 连接数据库...")
	if err := database.Init(&cfg.Database); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}
	fmt.Println("✅ 数据库连接成功")

	// 获取数据库实例
	db := database.GetDB()

	// 检查现有表
	fmt.Println("\n📋 检查现有表...")
	var tables []string
	db.Raw("SELECT tablename FROM pg_tables WHERE schemaname = 'public'").Scan(&tables)
	fmt.Printf("   现有表: %v\n", tables)

	// 尝试手动迁移每个模型
	fmt.Println("\n🔄 开始数据库迁移...")

	// 迁移User模型
	fmt.Println("   迁移User模型...")
	if err := db.AutoMigrate(&model.User{}); err != nil {
		fmt.Printf("   ❌ User模型迁移失败: %v\n", err)
	} else {
		fmt.Println("   ✅ User模型迁移成功")
	}

	// 迁移DownloadTask模型
	fmt.Println("   迁移DownloadTask模型...")
	if err := db.AutoMigrate(&model.DownloadTask{}); err != nil {
		fmt.Printf("   ❌ DownloadTask模型迁移失败: %v\n", err)
	} else {
		fmt.Println("   ✅ DownloadTask模型迁移成功")
	}

	// 迁移SystemConfig模型
	fmt.Println("   迁移SystemConfig模型...")
	if err := db.AutoMigrate(&model.SystemConfig{}); err != nil {
		fmt.Printf("   ❌ SystemConfig模型迁移失败: %v\n", err)
	} else {
		fmt.Println("   ✅ SystemConfig模型迁移成功")
	}

	// 检查迁移后的表
	fmt.Println("\n📋 检查迁移后的表...")
	var tablesAfter []string
	db.Raw("SELECT tablename FROM pg_tables WHERE schemaname = 'public'").Scan(&tablesAfter)
	fmt.Printf("   迁移后的表: %v\n", tablesAfter)

	// 测试创建默认用户
	fmt.Println("\n👤 检查默认用户...")
	var userCount int64
	db.Model(&model.User{}).Count(&userCount)
	fmt.Printf("   用户数量: %d\n", userCount)

	if userCount == 0 {
		fmt.Println("   创建默认管理员用户...")
		admin := &model.User{
			Username: "admin",
			Email:    "<EMAIL>",
			Role:     model.UserRoleAdmin,
			Status:   model.UserStatusActive,
		}
		
		if err := admin.SetPassword("admin123"); err != nil {
			fmt.Printf("   ❌ 设置密码失败: %v\n", err)
		} else if err := db.Create(admin).Error; err != nil {
			fmt.Printf("   ❌ 创建用户失败: %v\n", err)
		} else {
			fmt.Println("   ✅ 默认管理员用户创建成功")
			fmt.Println("      用户名: admin")
			fmt.Println("      密码: admin123")
		}
	}

	// 测试查询
	fmt.Println("\n🔍 测试数据库查询...")
	var users []model.User
	if err := db.Find(&users).Error; err != nil {
		fmt.Printf("   ❌ 查询用户失败: %v\n", err)
	} else {
		fmt.Printf("   ✅ 查询成功，找到 %d 个用户\n", len(users))
		for _, user := range users {
			fmt.Printf("      - %s (%s)\n", user.Username, user.Role)
		}
	}

	fmt.Println("\n✅ 数据库测试完成！")
}