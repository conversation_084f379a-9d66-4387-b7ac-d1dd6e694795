{"dashboard": {"id": null, "title": "磁力下载器监控仪表板", "tags": ["magnet-downloader", "monitoring"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "系统概览", "type": "stat", "targets": [{"expr": "up{job=\"magnet-downloader\"}", "legendFormat": "应用状态"}, {"expr": "system_uptime_seconds", "legendFormat": "运行时间"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "HTTP请求QPS", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{method}} {{endpoint}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "HTTP响应时间", "type": "graph", "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "P50"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "P95"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "P99"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "任务统计", "type": "stat", "targets": [{"expr": "tasks_active", "legendFormat": "活跃任务"}, {"expr": "tasks_queue_length", "legendFormat": "队列长度"}, {"expr": "task_success_rate", "legendFormat": "成功率"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "下载速度", "type": "graph", "targets": [{"expr": "download_speed_bytes_per_second", "legendFormat": "下载速度"}, {"expr": "upload_speed_bytes_per_second", "legendFormat": "上传速度"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "系统资源", "type": "graph", "targets": [{"expr": "cpu_usage_percent", "legendFormat": "CPU使用率"}, {"expr": "(memory_usage_bytes / memory_total_bytes) * 100", "legendFormat": "内存使用率"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "数据库性能", "type": "graph", "targets": [{"expr": "rate(database_queries_total[5m])", "legendFormat": "{{operation}} QPS"}, {"expr": "histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m]))", "legendFormat": "P95响应时间"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 8, "title": "WebSocket连接", "type": "graph", "targets": [{"expr": "websocket_connections", "legendFormat": "活跃连接"}, {"expr": "rate(websocket_messages_total[5m])", "legendFormat": "消息速率"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 9, "title": "错误率", "type": "graph", "targets": [{"expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100", "legendFormat": "HTTP错误率"}, {"expr": "rate(tasks_total{status=\"failed\"}[5m]) / rate(tasks_total[5m]) * 100", "legendFormat": "任务失败率"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}]}}