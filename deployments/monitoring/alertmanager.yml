global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        severity: warning
      receiver: 'warning-alerts'
    - match:
        severity: info
      receiver: 'info-alerts'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://127.0.0.1:5001/'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] 磁力下载器告警'
        body: |
          告警名称: {{ .GroupLabels.alertname }}
          告警级别: {{ .CommonLabels.severity }}
          告警时间: {{ .CommonAnnotations.summary }}
          告警详情: {{ .CommonAnnotations.description }}
    webhook_configs:
      - url: 'http://127.0.0.1:5001/critical'
        send_resolved: true

  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[WARNING] 磁力下载器告警'
        body: |
          告警名称: {{ .GroupLabels.alertname }}
          告警级别: {{ .CommonLabels.severity }}
          告警时间: {{ .CommonAnnotations.summary }}
          告警详情: {{ .CommonAnnotations.description }}

  - name: 'info-alerts'
    webhook_configs:
      - url: 'http://127.0.0.1:5001/info'
        send_resolved: true

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
