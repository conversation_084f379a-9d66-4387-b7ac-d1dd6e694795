groups:
  - name: magnet-downloader-alerts
    rules:
      # 应用程序告警
      - alert: ApplicationDown
        expr: up{job="magnet-downloader"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "磁力下载器应用程序宕机"
          description: "磁力下载器应用程序已经宕机超过1分钟"

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "HTTP错误率过高"
          description: "HTTP 5xx错误率超过10%，当前值: {{ $value | humanizePercentage }}"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "HTTP响应时间过长"
          description: "95%的HTTP请求响应时间超过2秒，当前值: {{ $value }}s"

      # 任务相关告警
      - alert: HighTaskFailureRate
        expr: rate(tasks_total{status="failed"}[10m]) / rate(tasks_total[10m]) > 0.2
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "任务失败率过高"
          description: "任务失败率超过20%，当前值: {{ $value | humanizePercentage }}"

      - alert: TaskQueueTooLong
        expr: tasks_queue_length > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "任务队列过长"
          description: "任务队列长度超过1000，当前值: {{ $value }}"

      - alert: LowDownloadSpeed
        expr: download_speed_bytes_per_second < 1048576  # 1MB/s
        for: 10m
        labels:
          severity: info
        annotations:
          summary: "下载速度过低"
          description: "平均下载速度低于1MB/s，当前值: {{ $value | humanize1024 }}B/s"

      # 系统资源告警
      - alert: HighCPUUsage
        expr: cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ $value }}%"

      - alert: HighMemoryUsage
        expr: (memory_usage_bytes / memory_total_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率超过85%，当前值: {{ $value }}%"

      - alert: TooManyGoroutines
        expr: goroutines_count > 10000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Goroutine数量过多"
          description: "Goroutine数量超过10000，当前值: {{ $value }}"

      # 数据库告警
      - alert: DatabaseConnectionsHigh
        expr: database_connections{state="active"} > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
          description: "活跃数据库连接数超过80，当前值: {{ $value }}"

      - alert: SlowDatabaseQueries
        expr: histogram_quantile(0.95, rate(database_query_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库查询过慢"
          description: "95%的数据库查询时间超过1秒，当前值: {{ $value }}s"

      # Redis告警
      - alert: RedisConnectionsHigh
        expr: redis_connections > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis连接数过高"
          description: "Redis连接数超过100，当前值: {{ $value }}"

      # WebSocket告警
      - alert: WebSocketConnectionsHigh
        expr: websocket_connections > 1000
        for: 5m
        labels:
          severity: info
        annotations:
          summary: "WebSocket连接数过高"
          description: "WebSocket连接数超过1000，当前值: {{ $value }}"

  - name: system-alerts
    rules:
      # 系统级告警
      - alert: DiskSpaceHigh
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘可用空间少于10%，当前值: {{ $value }}%"

      - alert: SystemLoadHigh
        expr: node_load1 > 4
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "系统负载过高"
          description: "1分钟平均负载超过4，当前值: {{ $value }}"
