apiVersion: v1
kind: ConfigMap
metadata:
  name: magnet-downloader-config
  namespace: magnet-downloader
data:
  config.yaml: |
    app:
      name: "磁力下载器"
      version: "1.0.0"
      env: "production"
      debug: false

    server:
      host: "0.0.0.0"
      port: 8080
      mode: "release"
      read_timeout: 30
      write_timeout: 30

    database:
      host: "postgres-service"
      port: 5432
      name: "magnet_downloader"
      user: "postgres"
      password: "postgres123"
      ssl_mode: "disable"
      max_open_conns: 25
      max_idle_conns: 5
      conn_max_lifetime: 300

    redis:
      host: "redis-service"
      port: 6379
      password: "redis123"
      db: 0
      pool_size: 10

    aria2:
      host: "aria2-service"
      port: 6800
      secret: "aria2secret"
      max_concurrent_downloads: 5
      max_connection_per_server: 16
      split: 16
      min_split_size: "10M"
      download_path: "/downloads"

    log:
      level: "info"
      format: "json"

    jwt:
      secret: "your-jwt-secret-key-change-this-in-production"
      expire_time: 86400

    scheduler:
      enabled: true
      timezone: "Asia/Shanghai"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: magnet-downloader
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 100M;

        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        upstream magnet_downloader {
            server magnet-downloader-service:8080;
            keepalive 32;
        }

        server {
            listen 80;
            server_name _;
            root /usr/share/nginx/html;
            index index.html index.htm;

            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            location / {
                try_files $uri $uri/ /index.html;
                expires 1h;
                add_header Cache-Control "public, immutable";
            }

            location /api/ {
                proxy_pass http://magnet_downloader;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            location /api/v1/ws {
                proxy_pass http://magnet_downloader;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                proxy_read_timeout 86400;
                proxy_send_timeout 86400;
            }
        }
    }
