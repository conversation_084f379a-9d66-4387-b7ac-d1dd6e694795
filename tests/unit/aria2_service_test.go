package unit

import (
	"fmt"
	"path/filepath"
	"strconv"
	"testing"

	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/aria2"
)

// TestGetActualDownloadedFiles 测试获取实际下载文件列表
func TestGetActualDownloadedFiles(t *testing.T) {
	// 创建模拟的aria2服务
	mockAria2Service := &MockAria2Service{}

	tests := []struct {
		name           string
		gid            string
		mockResponse   *aria2.DownloadInfo
		expectedFiles  int
		expectedError  bool
		expectedSelected int
	}{
		{
			name: "单个文件下载",
			gid:  "test-gid-1",
			mockResponse: &aria2.DownloadInfo{
				GID:    "test-gid-1",
				Status: aria2.StatusComplete,
				Files: []aria2.FileInfo{
					{
						Index:           "0",
						Path:            "/downloads/movie.mp4",
						Length:          "1073741824",
						CompletedLength: "1073741824",
						Selected:        "true",
					},
				},
			},
			expectedFiles:    1,
			expectedSelected: 1,
			expectedError:    false,
		},
		{
			name: "多文件下载（部分选中）",
			gid:  "test-gid-2",
			mockResponse: &aria2.DownloadInfo{
				GID:    "test-gid-2",
				Status: aria2.StatusComplete,
				Files: []aria2.FileInfo{
					{
						Index:           "0",
						Path:            "/downloads/movie.mp4",
						Length:          "1073741824",
						CompletedLength: "1073741824",
						Selected:        "true",
					},
					{
						Index:           "1",
						Path:            "/downloads/subtitle.srt",
						Length:          "2048",
						CompletedLength: "2048",
						Selected:        "true",
					},
					{
						Index:           "2",
						Path:            "/downloads/readme.txt",
						Length:          "1024",
						CompletedLength: "1024",
						Selected:        "false",
					},
				},
			},
			expectedFiles:    3,
			expectedSelected: 2,
			expectedError:    false,
		},
		{
			name: "空文件列表",
			gid:  "test-gid-3",
			mockResponse: &aria2.DownloadInfo{
				GID:    "test-gid-3",
				Status: aria2.StatusComplete,
				Files:  []aria2.FileInfo{},
			},
			expectedFiles:    0,
			expectedSelected: 0,
			expectedError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置模拟响应
			mockAria2Service.SetMockResponse(tt.gid, tt.mockResponse)

			// 调用被测试的方法
			actualFiles, err := mockAria2Service.GetActualDownloadedFiles(tt.gid)

			// 验证错误
			if tt.expectedError && err == nil {
				t.Errorf("Expected error but got nil")
			}
			if !tt.expectedError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			// 验证文件数量
			if len(actualFiles) != tt.expectedFiles {
				t.Errorf("Expected %d files, got %d", tt.expectedFiles, len(actualFiles))
			}

			// 验证选中文件数量
			selectedCount := 0
			for _, file := range actualFiles {
				if file.Selected {
					selectedCount++
				}
			}
			if selectedCount != tt.expectedSelected {
				t.Errorf("Expected %d selected files, got %d", tt.expectedSelected, selectedCount)
			}

			// 验证文件信息解析
			for i, file := range actualFiles {
				if i < len(tt.mockResponse.Files) {
					expectedFile := tt.mockResponse.Files[i]
					
					if file.Path != expectedFile.Path {
						t.Errorf("File %d: expected path %s, got %s", i, expectedFile.Path, file.Path)
					}
					
					if file.Selected != (expectedFile.Selected == "true") {
						t.Errorf("File %d: expected selected %v, got %v", i, expectedFile.Selected == "true", file.Selected)
					}
				}
			}
		})
	}
}

// TestActualFileInfoMethods 测试ActualFileInfo相关方法
func TestActualFileInfoMethods(t *testing.T) {
	// 创建测试任务
	task := &model.DownloadTask{
		TaskName: "test-task",
	}

	// 测试空的ActualFiles
	t.Run("EmptyActualFiles", func(t *testing.T) {
		if task.HasActualFiles() {
			t.Error("Expected HasActualFiles to return false for empty task")
		}

		if len(task.GetActualFiles()) != 0 {
			t.Error("Expected GetActualFiles to return empty slice")
		}

		if len(task.GetSelectedFiles()) != 0 {
			t.Error("Expected GetSelectedFiles to return empty slice")
		}

		if task.GetPrimaryFile() != nil {
			t.Error("Expected GetPrimaryFile to return nil")
		}
	})

	// 设置测试文件
	actualFiles := []model.ActualFileInfo{
		{
			Index:           0,
			Path:            "/downloads/movie.mp4",
			Name:            "movie.mp4",
			Size:            1073741824,
			CompletedLength: 1073741824,
			Selected:        true,
		},
		{
			Index:           1,
			Path:            "/downloads/subtitle.srt",
			Name:            "subtitle.srt",
			Size:            2048,
			CompletedLength: 2048,
			Selected:        true,
		},
		{
			Index:           2,
			Path:            "/downloads/readme.txt",
			Name:            "readme.txt",
			Size:            1024,
			CompletedLength: 1024,
			Selected:        false,
		},
	}

	task.SetActualFiles(actualFiles)

	// 测试有文件的情况
	t.Run("WithActualFiles", func(t *testing.T) {
		if !task.HasActualFiles() {
			t.Error("Expected HasActualFiles to return true")
		}

		allFiles := task.GetActualFiles()
		if len(allFiles) != 3 {
			t.Errorf("Expected 3 files, got %d", len(allFiles))
		}

		selectedFiles := task.GetSelectedFiles()
		if len(selectedFiles) != 2 {
			t.Errorf("Expected 2 selected files, got %d", len(selectedFiles))
		}

		primaryFile := task.GetPrimaryFile()
		if primaryFile == nil {
			t.Error("Expected GetPrimaryFile to return non-nil")
		} else {
			if primaryFile.Name != "movie.mp4" {
				t.Errorf("Expected primary file name 'movie.mp4', got '%s'", primaryFile.Name)
			}
		}
	})
}

// TestActualFilesJSONSerialization 测试ActualFiles的JSON序列化
func TestActualFilesJSONSerialization(t *testing.T) {
	files := model.ActualFiles{
		{
			Index:           0,
			Path:            "/downloads/test.mp4",
			Name:            "test.mp4",
			Size:            1024000,
			CompletedLength: 1024000,
			Selected:        true,
		},
	}

	// 测试Value方法（序列化）
	value, err := files.Value()
	if err != nil {
		t.Fatalf("Failed to serialize ActualFiles: %v", err)
	}

	if value == nil {
		t.Error("Expected non-nil value from serialization")
	}

	// 测试Scan方法（反序列化）
	var scannedFiles model.ActualFiles
	err = scannedFiles.Scan(value)
	if err != nil {
		t.Fatalf("Failed to deserialize ActualFiles: %v", err)
	}

	if len(scannedFiles) != 1 {
		t.Errorf("Expected 1 file after deserialization, got %d", len(scannedFiles))
	}

	if scannedFiles[0].Name != "test.mp4" {
		t.Errorf("Expected file name 'test.mp4', got '%s'", scannedFiles[0].Name)
	}

	// 测试nil值处理
	var nilFiles model.ActualFiles
	nilValue, err := nilFiles.Value()
	if err != nil {
		t.Errorf("Expected no error for nil ActualFiles, got %v", err)
	}
	if nilValue != nil {
		t.Error("Expected nil value for nil ActualFiles")
	}

	// 测试Scan nil值
	var scanNilFiles model.ActualFiles
	err = scanNilFiles.Scan(nil)
	if err != nil {
		t.Errorf("Expected no error when scanning nil, got %v", err)
	}
	if scanNilFiles != nil {
		t.Error("Expected nil result when scanning nil")
	}
}

// MockAria2Service 模拟aria2服务
type MockAria2Service struct {
	mockResponses map[string]*aria2.DownloadInfo
}

func (m *MockAria2Service) SetMockResponse(gid string, response *aria2.DownloadInfo) {
	if m.mockResponses == nil {
		m.mockResponses = make(map[string]*aria2.DownloadInfo)
	}
	m.mockResponses[gid] = response
}

func (m *MockAria2Service) GetTaskStatus(gid string) (*aria2.DownloadInfo, error) {
	if response, exists := m.mockResponses[gid]; exists {
		return response, nil
	}
	return nil, fmt.Errorf("task not found")
}

func (m *MockAria2Service) GetActualDownloadedFiles(gid string) ([]model.ActualFileInfo, error) {
	info, err := m.GetTaskStatus(gid)
	if err != nil {
		return nil, err
	}

	// 模拟service中的GetActualDownloadedFiles逻辑
	var actualFiles []model.ActualFileInfo
	
	for _, file := range info.Files {
		index := 0
		if idx, err := strconv.Atoi(file.Index); err == nil {
			index = idx
		}

		size := int64(0)
		if s, err := strconv.ParseInt(file.Length, 10, 64); err == nil {
			size = s
		}

		completedLength := int64(0)
		if cl, err := strconv.ParseInt(file.CompletedLength, 10, 64); err == nil {
			completedLength = cl
		}

		selected := file.Selected == "true"
		fileName := filepath.Base(file.Path)

		actualFile := model.ActualFileInfo{
			Index:           index,
			Path:            file.Path,
			Name:            fileName,
			Size:            size,
			CompletedLength: completedLength,
			Selected:        selected,
		}

		actualFiles = append(actualFiles, actualFile)
	}

	return actualFiles, nil
}

// 实现其他必要的接口方法（简化版）
func (m *MockAria2Service) AddMagnet(magnetURI string, options map[string]string) (string, error) {
	return "mock-gid", nil
}

func (m *MockAria2Service) GetActiveDownloads() ([]*aria2.DownloadInfo, error) {
	return nil, nil
}

func (m *MockAria2Service) GetWaitingDownloads(offset, limit int) ([]*aria2.DownloadInfo, error) {
	return nil, nil
}

func (m *MockAria2Service) GetStoppedDownloads(offset, limit int) ([]*aria2.DownloadInfo, error) {
	return nil, nil
}

func (m *MockAria2Service) PauseDownload(gid string) error {
	return nil
}

func (m *MockAria2Service) ResumeDownload(gid string) error {
	return nil
}

func (m *MockAria2Service) RemoveDownload(gid string) error {
	return nil
}