package mixfile_test

import (
	"bytes"
	"crypto/rand"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"magnet-downloader/pkg/mixfile"
	"magnet-downloader/pkg/steganography"
)

func TestMixFileEndToEndFlow(t *testing.T) {
	// 创建临时目录
	tempDir := t.TempDir()

	// 测试数据
	testData := []byte("Hello, MixFile integration test! This is a complete end-to-end test.")
	testFileName := "test_file.txt"

	t.Run("CompleteFlow", func(t *testing.T) {
		// 步骤1：创建测试文件
		testFilePath := filepath.Join(tempDir, testFileName)
		err := os.WriteFile(testFilePath, testData, 0644)
		if err != nil {
			t.Fatalf("Failed to create test file: %v", err)
		}

		// 步骤2：模拟文件分片和加密
		chunks := []mixfile.ChunkInfo{
			{Index: 0, URL: "https://example.com/chunk0.png", Hash: "hash0", Size: int64(len(testData))},
		}

		metadata := map[string]interface{}{
			"filename":           testFileName,
			"chunk_size":         1024,
			"encryption_key":     "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef",
			"original_file_hash": "original_hash_here",
		}

		// 步骤3：创建索引文件
		indexManager := mixfile.NewIndexManager()
		index, err := indexManager.CreateIndex(chunks, metadata)
		if err != nil {
			t.Fatalf("Failed to create index: %v", err)
		}

		// 步骤4：序列化索引文件
		indexData, err := indexManager.SerializeIndex(index)
		if err != nil {
			t.Fatalf("Failed to serialize index: %v", err)
		}

		// 步骤5：压缩索引文件
		compressedIndex, err := indexManager.CompressIndex(indexData)
		if err != nil {
			t.Fatalf("Failed to compress index: %v", err)
		}

		// 步骤6：加密索引文件
		encryptionKey := []byte("0123456789abcdef0123456789abcdef")
		encryptedIndex, err := indexManager.EncryptIndex(compressedIndex, encryptionKey)
		if err != nil {
			t.Fatalf("Failed to encrypt index: %v", err)
		}

		// 步骤7：隐写术处理索引文件
		steganographer := steganography.NewSteganographer(steganography.DefaultConfig())
		indexPNG, err := steganographer.HideDataInPNG(encryptedIndex)
		if err != nil {
			t.Fatalf("Failed to hide index in PNG: %v", err)
		}

		// 步骤8：生成分享码
		shareCodeProcessor := mixfile.NewShareCodeProcessor(true)
		shareCode, err := shareCodeProcessor.IndexToShareCode(index, "https://example.com/index.png")
		if err != nil {
			t.Fatalf("Failed to generate share code: %v", err)
		}

		// 步骤9：解析分享码
		downloadInfo, err := shareCodeProcessor.ShareCodeToDownloadInfo(shareCode)
		if err != nil {
			t.Fatalf("Failed to parse share code: %v", err)
		}

		// 验证分享码信息
		if downloadInfo.FileName != testFileName {
			t.Errorf("Filename mismatch: expected %s, got %s", testFileName, downloadInfo.FileName)
		}

		// 步骤10：模拟下载和解析索引文件
		extractedIndex, err := steganographer.ExtractDataFromPNG(indexPNG)
		if err != nil {
			t.Fatalf("Failed to extract index from PNG: %v", err)
		}

		decryptedIndex, err := indexManager.DecryptIndex(extractedIndex, encryptionKey)
		if err != nil {
			t.Fatalf("Failed to decrypt index: %v", err)
		}

		decompressedIndex, err := indexManager.DecompressIndex(decryptedIndex)
		if err != nil {
			t.Fatalf("Failed to decompress index: %v", err)
		}

		parsedIndex, err := indexManager.ParseIndex(decompressedIndex)
		if err != nil {
			t.Fatalf("Failed to parse index: %v", err)
		}

		// 验证索引文件
		if parsedIndex.FileName != index.FileName {
			t.Errorf("Index filename mismatch: expected %s, got %s", index.FileName, parsedIndex.FileName)
		}

		if len(parsedIndex.Chunks) != len(index.Chunks) {
			t.Errorf("Chunk count mismatch: expected %d, got %d", len(index.Chunks), len(parsedIndex.Chunks))
		}

		t.Logf("End-to-end test completed successfully")
		t.Logf("Original data: %d bytes", len(testData))
		t.Logf("Index data: %d bytes", len(indexData))
		t.Logf("Compressed index: %d bytes", len(compressedIndex))
		t.Logf("Encrypted index: %d bytes", len(encryptedIndex))
		t.Logf("PNG data: %d bytes", len(indexPNG))
		t.Logf("Share code: %d characters", len(shareCode))
	})
}

func TestMixFileIndexManagement(t *testing.T) {
	indexManager := mixfile.NewIndexManager()

	t.Run("IndexCreationAndValidation", func(t *testing.T) {
		chunks := []mixfile.ChunkInfo{
			{Index: 0, URL: "https://example.com/chunk0.png", Hash: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456", Size: 1024},
			{Index: 1, URL: "https://example.com/chunk1.png", Hash: "b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a", Size: 1024},
			{Index: 2, URL: "https://example.com/chunk2.png", Hash: "c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567ab2", Size: 512},
		}

		metadata := map[string]interface{}{
			"filename":           "test.txt",
			"chunk_size":         1024,
			"encryption_key":     "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef",
			"original_file_hash": "original_hash_here",
		}

		// 创建索引
		index, err := indexManager.CreateIndex(chunks, metadata)
		if err != nil {
			t.Fatalf("Failed to create index: %v", err)
		}

		// 验证索引
		if index.GetChunkCount() != len(chunks) {
			t.Errorf("Chunk count mismatch: expected %d, got %d", len(chunks), index.GetChunkCount())
		}

		if index.FileSize != 2560 { // 1024 + 1024 + 512
			t.Errorf("File size mismatch: expected 2560, got %d", index.FileSize)
		}

		// 测试序列化和反序列化
		data, err := indexManager.SerializeIndex(index)
		if err != nil {
			t.Fatalf("Failed to serialize index: %v", err)
		}

		parsedIndex, err := indexManager.ParseIndex(data)
		if err != nil {
			t.Fatalf("Failed to parse index: %v", err)
		}

		if parsedIndex.FileName != index.FileName {
			t.Errorf("Filename mismatch after serialization")
		}
	})

	t.Run("IndexCompressionAndEncryption", func(t *testing.T) {
		// 创建测试索引
		chunks := []mixfile.ChunkInfo{
			{Index: 0, URL: "https://example.com/chunk0.png", Hash: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456", Size: 1024},
		}

		metadata := map[string]interface{}{
			"filename": "test.txt",
		}

		index, err := indexManager.CreateIndex(chunks, metadata)
		if err != nil {
			t.Fatalf("Failed to create index: %v", err)
		}

		data, err := indexManager.SerializeIndex(index)
		if err != nil {
			t.Fatalf("Failed to serialize index: %v", err)
		}

		// 测试压缩
		compressed, err := indexManager.CompressIndex(data)
		if err != nil {
			t.Fatalf("Failed to compress index: %v", err)
		}

		decompressed, err := indexManager.DecompressIndex(compressed)
		if err != nil {
			t.Fatalf("Failed to decompress index: %v", err)
		}

		if !bytes.Equal(data, decompressed) {
			t.Error("Compression/decompression data mismatch")
		}

		// 测试加密
		key := []byte("0123456789abcdef0123456789abcdef")
		encrypted, err := indexManager.EncryptIndex(data, key)
		if err != nil {
			t.Fatalf("Failed to encrypt index: %v", err)
		}

		decrypted, err := indexManager.DecryptIndex(encrypted, key)
		if err != nil {
			t.Fatalf("Failed to decrypt index: %v", err)
		}

		if !bytes.Equal(data, decrypted) {
			t.Error("Encryption/decryption data mismatch")
		}

		t.Logf("Original: %d bytes, Compressed: %d bytes (%.1f%%)", 
			len(data), len(compressed), float64(len(compressed))/float64(len(data))*100)
	})
}

func TestMixFileShareCodeGeneration(t *testing.T) {
	shareCodeProcessor := mixfile.NewShareCodeProcessor(true)

	t.Run("ShareCodeGenerationAndParsing", func(t *testing.T) {
		// 创建测试索引
		indexManager := mixfile.NewIndexManager()
		chunks := []mixfile.ChunkInfo{
			{Index: 0, URL: "https://example.com/chunk0.png", Hash: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456", Size: 1024},
		}

		metadata := map[string]interface{}{
			"filename":    "test.txt",
			"filesize":    int64(1024),
			"chunk_count": 1,
		}

		index, err := indexManager.CreateIndex(chunks, metadata)
		if err != nil {
			t.Fatalf("Failed to create index: %v", err)
		}

		// 生成分享码
		indexURL := "https://example.com/index.png"
		shareCode, err := shareCodeProcessor.IndexToShareCode(index, indexURL)
		if err != nil {
			t.Fatalf("Failed to generate share code: %v", err)
		}

		// 验证分享码格式
		if !mixfile.IsValidProtocol(shareCode) {
			t.Error("Invalid share code protocol")
		}

		// 解析分享码
		downloadInfo, err := shareCodeProcessor.ShareCodeToDownloadInfo(shareCode)
		if err != nil {
			t.Fatalf("Failed to parse share code: %v", err)
		}

		// 验证解析结果
		if downloadInfo.FileName != "test.txt" {
			t.Errorf("Filename mismatch: expected test.txt, got %s", downloadInfo.FileName)
		}

		if downloadInfo.IndexURL != indexURL {
			t.Errorf("Index URL mismatch: expected %s, got %s", indexURL, downloadInfo.IndexURL)
		}

		t.Logf("Share code generated: %d characters", len(shareCode))
		t.Logf("Share code prefix: %s", shareCode[:20]+"...")
	})

	t.Run("ShareCodeValidation", func(t *testing.T) {
		validShareCode := "mf://eyJ2IjoiMS4wIiwiZmlsZW5hbWUiOiJ0ZXN0LnR4dCJ9"
		invalidShareCodes := []string{
			"invalid://test",
			"mf://",
			"mf://invalid_base64!@#",
			"http://example.com",
			"",
		}

		// 验证有效分享码格式
		if !mixfile.IsValidProtocol(validShareCode) {
			t.Error("Valid share code should pass protocol validation")
		}

		// 验证无效分享码
		for _, invalidCode := range invalidShareCodes {
			if mixfile.IsValidProtocol(invalidCode) {
				t.Errorf("Invalid share code should fail validation: %s", invalidCode)
			}
		}
	})
}

func TestMixFileDownloader(t *testing.T) {
	t.Run("DownloaderConfiguration", func(t *testing.T) {
		config := mixfile.DefaultDownloaderConfig()
		downloader := mixfile.NewMixFileDownloader(config)

		// 验证配置
		if config.MaxConcurrentDownloads <= 0 {
			t.Error("MaxConcurrentDownloads should be positive")
		}

		if config.ChunkTimeout <= 0 {
			t.Error("ChunkTimeout should be positive")
		}

		if config.RetryAttempts <= 0 {
			t.Error("RetryAttempts should be positive")
		}

		// 测试文件完整性验证
		tempFile := filepath.Join(t.TempDir(), "test.txt")
		testData := []byte("test data")
		err := os.WriteFile(tempFile, testData, 0644)
		if err != nil {
			t.Fatalf("Failed to create test file: %v", err)
		}

		// 计算正确的哈希
		expectedHash := "916f0027a575074ce72a331777c3478d6513f786a591bd892da1a577bf2335f9"
		
		// 验证文件完整性（应该失败，因为哈希不匹配）
		err = downloader.VerifyFileIntegrity(tempFile, expectedHash)
		if err == nil {
			t.Error("File integrity verification should fail for mismatched hash")
		}
	})

	t.Run("ProgressCallback", func(t *testing.T) {
		progressCalled := false
		progressCallback := func(progress *mixfile.DownloadProgress) {
			progressCalled = true
			
			// 验证进度数据
			if progress.Progress < 0 || progress.Progress > 100 {
				t.Errorf("Invalid progress value: %f", progress.Progress)
			}
			
			if progress.OverallProgress < 0 || progress.OverallProgress > 100 {
				t.Errorf("Invalid overall progress value: %f", progress.OverallProgress)
			}
			
			if progress.Stage == "" {
				t.Error("Stage should not be empty")
			}
		}

		// 模拟进度回调
		progress := &mixfile.DownloadProgress{
			Stage:           "downloading_chunks",
			Progress:        50.0,
			OverallProgress: 75.0,
			ChunkIndex:      2,
			ChunkCount:      4,
			DownloadedSize:  1024,
			TotalSize:       2048,
			Speed:           1024,
			ETA:             1,
			Message:         "Downloading chunk 2/4",
		}

		progressCallback(progress)

		if !progressCalled {
			t.Error("Progress callback was not called")
		}
	})
}

func TestMixFileCompatibility(t *testing.T) {
	t.Run("BackwardCompatibility", func(t *testing.T) {
		// 测试向后兼容性
		// 这里可以测试新版本是否能处理旧版本的数据格式
		
		// 模拟旧版本的索引数据
		oldVersionData := `{
			"version": "1.0.0",
			"filename": "old_file.txt",
			"filesize": 1024,
			"chunks": [
				{"index": 0, "url": "https://example.com/chunk0.png", "hash": "hash0", "size": 1024}
			]
		}`

		indexManager := mixfile.NewIndexManager()
		_, err := indexManager.ParseIndex([]byte(oldVersionData))
		if err != nil {
			t.Errorf("Failed to parse old version index: %v", err)
		}
	})

	t.Run("ConfigurationToggle", func(t *testing.T) {
		// 测试配置开关
		// 这里可以测试MixFile功能的启用/禁用
		
		// 模拟配置
		mixFileEnabled := true
		steganographyEnabled := true
		compressionEnabled := true
		encryptionEnabled := true

		if !mixFileEnabled {
			t.Skip("MixFile functionality is disabled")
		}

		// 根据配置测试相应功能
		if steganographyEnabled {
			config := steganography.DefaultConfig()
			stego := steganography.NewSteganographer(config)
			testData := []byte("test")
			
			_, err := stego.HideDataInPNG(testData)
			if err != nil {
				t.Errorf("Steganography should work when enabled: %v", err)
			}
		}

		if compressionEnabled {
			indexManager := mixfile.NewIndexManager()
			testData := []byte("test data for compression")
			
			_, err := indexManager.CompressIndex(testData)
			if err != nil {
				t.Errorf("Compression should work when enabled: %v", err)
			}
		}

		if encryptionEnabled {
			indexManager := mixfile.NewIndexManager()
			testData := []byte("test data for encryption")
			key := []byte("0123456789abcdef0123456789abcdef")
			
			_, err := indexManager.EncryptIndex(testData, key)
			if err != nil {
				t.Errorf("Encryption should work when enabled: %v", err)
			}
		}
	})
}