package mixfile_test

import (
	"crypto/rand"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"magnet-downloader/pkg/mixfile"
	"magnet-downloader/pkg/steganography"
)

func TestMixFilePerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance tests in short mode")
	}

	t.Run("SteganographyPerformance", func(t *testing.T) {
		config := steganography.DefaultConfig()
		stego := steganography.NewSteganographer(config)

		// 测试不同大小的数据
		testSizes := []int{
			1024,      // 1KB
			10240,     // 10KB
			102400,    // 100KB
			1048576,   // 1MB
		}

		for _, size := range testSizes {
			t.Run(fmt.Sprintf("Size_%dBytes", size), func(t *testing.T) {
				testData := make([]byte, size)
				rand.Read(testData)

				// 测试隐藏性能
				start := time.Now()
				pngData, err := stego.HideDataInPNG(testData)
				hideTime := time.Since(start)

				if err != nil {
					t.Fatalf("Failed to hide %d bytes: %v", size, err)
				}

				// 测试提取性能
				start = time.Now()
				extractedData, err := stego.ExtractDataFromPNG(pngData)
				extractTime := time.Since(start)

				if err != nil {
					t.Fatalf("Failed to extract %d bytes: %v", size, err)
				}

				// 验证数据一致性
				if len(extractedData) != len(testData) {
					t.Errorf("Data length mismatch: expected %d, got %d", len(testData), len(extractedData))
				}

				// 计算性能指标
				hideSpeed := float64(size) / hideTime.Seconds() / 1024 / 1024 // MB/s
				extractSpeed := float64(size) / extractTime.Seconds() / 1024 / 1024 // MB/s
				compressionRatio := float64(len(pngData)) / float64(size)

				t.Logf("Size: %d bytes", size)
				t.Logf("Hide: %.2f MB/s (%v)", hideSpeed, hideTime)
				t.Logf("Extract: %.2f MB/s (%v)", extractSpeed, extractTime)
				t.Logf("PNG size: %d bytes (%.2fx)", len(pngData), compressionRatio)

				// 性能要求
				minSpeed := 0.1 // 至少0.1 MB/s
				if hideSpeed < minSpeed {
					t.Errorf("Hide speed too slow: %.2f MB/s < %.2f MB/s", hideSpeed, minSpeed)
				}
				if extractSpeed < minSpeed {
					t.Errorf("Extract speed too slow: %.2f MB/s < %.2f MB/s", extractSpeed, minSpeed)
				}
			})
		}
	})

	t.Run("IndexManagementPerformance", func(t *testing.T) {
		indexManager := mixfile.NewIndexManager()

		// 测试不同数量的分片
		chunkCounts := []int{10, 100, 1000}

		for _, chunkCount := range chunkCounts {
			t.Run(fmt.Sprintf("Chunks_%d", chunkCount), func(t *testing.T) {
				// 生成测试分片
				chunks := make([]mixfile.ChunkInfo, chunkCount)
				for i := 0; i < chunkCount; i++ {
					chunks[i] = mixfile.ChunkInfo{
						Index: i,
						URL:   fmt.Sprintf("https://example.com/chunk%d.png", i),
						Hash:  fmt.Sprintf("hash%d", i),
						Size:  1024,
					}
				}

				metadata := map[string]interface{}{
					"filename": "test.txt",
				}

				// 测试索引创建性能
				start := time.Now()
				index, err := indexManager.CreateIndex(chunks, metadata)
				createTime := time.Since(start)

				if err != nil {
					t.Fatalf("Failed to create index with %d chunks: %v", chunkCount, err)
				}

				// 测试序列化性能
				start = time.Now()
				data, err := indexManager.SerializeIndex(index)
				serializeTime := time.Since(start)

				if err != nil {
					t.Fatalf("Failed to serialize index: %v", err)
				}

				// 测试反序列化性能
				start = time.Now()
				_, err = indexManager.ParseIndex(data)
				parseTime := time.Since(start)

				if err != nil {
					t.Fatalf("Failed to parse index: %v", err)
				}

				// 测试压缩性能
				start = time.Now()
				compressed, err := indexManager.CompressIndex(data)
				compressTime := time.Since(start)

				if err != nil {
					t.Fatalf("Failed to compress index: %v", err)
				}

				// 测试解压缩性能
				start = time.Now()
				_, err = indexManager.DecompressIndex(compressed)
				decompressTime := time.Since(start)

				if err != nil {
					t.Fatalf("Failed to decompress index: %v", err)
				}

				compressionRatio := float64(len(compressed)) / float64(len(data)) * 100

				t.Logf("Chunks: %d", chunkCount)
				t.Logf("Create: %v", createTime)
				t.Logf("Serialize: %v (%d bytes)", serializeTime, len(data))
				t.Logf("Parse: %v", parseTime)
				t.Logf("Compress: %v (%.1f%%)", compressTime, compressionRatio)
				t.Logf("Decompress: %v", decompressTime)

				// 性能要求：每个操作应该在合理时间内完成
				maxTime := time.Second
				if createTime > maxTime {
					t.Errorf("Index creation too slow: %v > %v", createTime, maxTime)
				}
				if serializeTime > maxTime {
					t.Errorf("Serialization too slow: %v > %v", serializeTime, maxTime)
				}
				if parseTime > maxTime {
					t.Errorf("Parsing too slow: %v > %v", parseTime, maxTime)
				}
			})
		}
	})

	t.Run("ShareCodePerformance", func(t *testing.T) {
		shareCodeProcessor := mixfile.NewShareCodeProcessor(true)
		indexManager := mixfile.NewIndexManager()

		// 创建测试索引
		chunks := []mixfile.ChunkInfo{
			{Index: 0, URL: "https://example.com/chunk0.png", Hash: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456", Size: 1024},
		}

		metadata := map[string]interface{}{
			"filename":    "test.txt",
			"filesize":    int64(1024),
			"chunk_count": 1,
		}

		index, err := indexManager.CreateIndex(chunks, metadata)
		if err != nil {
			t.Fatalf("Failed to create index: %v", err)
		}

		indexURL := "https://example.com/index.png"

		// 测试分享码生成性能
		start := time.Now()
		shareCode, err := shareCodeProcessor.IndexToShareCode(index, indexURL)
		generateTime := time.Since(start)

		if err != nil {
			t.Fatalf("Failed to generate share code: %v", err)
		}

		// 测试分享码解析性能
		start = time.Now()
		_, err = shareCodeProcessor.ShareCodeToDownloadInfo(shareCode)
		parseTime := time.Since(start)

		if err != nil {
			t.Fatalf("Failed to parse share code: %v", err)
		}

		t.Logf("Share code length: %d characters", len(shareCode))
		t.Logf("Generate: %v", generateTime)
		t.Logf("Parse: %v", parseTime)

		// 性能要求
		maxTime := 100 * time.Millisecond
		if generateTime > maxTime {
			t.Errorf("Share code generation too slow: %v > %v", generateTime, maxTime)
		}
		if parseTime > maxTime {
			t.Errorf("Share code parsing too slow: %v > %v", parseTime, maxTime)
		}
	})
}

func TestMixFileConcurrency(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping concurrency tests in short mode")
	}

	t.Run("ConcurrentSteganography", func(t *testing.T) {
		config := steganography.DefaultConfig()
		stego := steganography.NewSteganographer(config)

		concurrency := runtime.NumCPU()
		testData := make([]byte, 1024)
		rand.Read(testData)

		var wg sync.WaitGroup
		errors := make(chan error, concurrency)

		start := time.Now()

		// 并发隐藏数据
		for i := 0; i < concurrency; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				pngData, err := stego.HideDataInPNG(testData)
				if err != nil {
					errors <- fmt.Errorf("goroutine %d hide failed: %w", id, err)
					return
				}

				extractedData, err := stego.ExtractDataFromPNG(pngData)
				if err != nil {
					errors <- fmt.Errorf("goroutine %d extract failed: %w", id, err)
					return
				}

				if len(extractedData) != len(testData) {
					errors <- fmt.Errorf("goroutine %d data length mismatch", id)
					return
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		totalTime := time.Since(start)

		// 检查错误
		for err := range errors {
			t.Error(err)
		}

		t.Logf("Concurrent steganography: %d goroutines in %v", concurrency, totalTime)
	})

	t.Run("ConcurrentIndexOperations", func(t *testing.T) {
		indexManager := mixfile.NewIndexManager()
		concurrency := runtime.NumCPU()

		var wg sync.WaitGroup
		errors := make(chan error, concurrency)

		start := time.Now()

		// 并发索引操作
		for i := 0; i < concurrency; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				chunks := []mixfile.ChunkInfo{
					{Index: 0, URL: fmt.Sprintf("https://example.com/chunk%d.png", id), Hash: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456", Size: 1024},
				}

				metadata := map[string]interface{}{
					"filename": fmt.Sprintf("test%d.txt", id),
				}

				index, err := indexManager.CreateIndex(chunks, metadata)
				if err != nil {
					errors <- fmt.Errorf("goroutine %d create failed: %w", id, err)
					return
				}

				data, err := indexManager.SerializeIndex(index)
				if err != nil {
					errors <- fmt.Errorf("goroutine %d serialize failed: %w", id, err)
					return
				}

				_, err = indexManager.ParseIndex(data)
				if err != nil {
					errors <- fmt.Errorf("goroutine %d parse failed: %w", id, err)
					return
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		totalTime := time.Since(start)

		// 检查错误
		for err := range errors {
			t.Error(err)
		}

		t.Logf("Concurrent index operations: %d goroutines in %v", concurrency, totalTime)
	})
}

func TestMixFileMemoryUsage(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping memory tests in short mode")
	}

	t.Run("MemoryUsageAnalysis", func(t *testing.T) {
		var m1, m2 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&m1)

		// 执行MixFile操作
		config := steganography.DefaultConfig()
		stego := steganography.NewSteganographer(config)

		// 处理多个文件
		for i := 0; i < 10; i++ {
			testData := make([]byte, 100*1024) // 100KB
			rand.Read(testData)

			pngData, err := stego.HideDataInPNG(testData)
			if err != nil {
				t.Fatalf("Failed to hide data: %v", err)
			}

			_, err = stego.ExtractDataFromPNG(pngData)
			if err != nil {
				t.Fatalf("Failed to extract data: %v", err)
			}
		}

		runtime.GC()
		runtime.ReadMemStats(&m2)

		allocatedMB := float64(m2.TotalAlloc-m1.TotalAlloc) / 1024 / 1024
		currentMB := float64(m2.Alloc) / 1024 / 1024

		t.Logf("Memory allocated: %.2f MB", allocatedMB)
		t.Logf("Current memory: %.2f MB", currentMB)

		// 内存使用应该在合理范围内
		maxMemoryMB := 100.0 // 最大100MB
		if currentMB > maxMemoryMB {
			t.Errorf("Memory usage too high: %.2f MB > %.2f MB", currentMB, maxMemoryMB)
		}
	})
}

// 基准测试
func BenchmarkMixFileSteganography(b *testing.B) {
	config := steganography.DefaultConfig()
	stego := steganography.NewSteganographer(config)
	testData := make([]byte, 1024)
	rand.Read(testData)

	b.Run("Hide", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := stego.HideDataInPNG(testData)
			if err != nil {
				b.Fatalf("Hide failed: %v", err)
			}
		}
	})

	pngData, _ := stego.HideDataInPNG(testData)

	b.Run("Extract", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := stego.ExtractDataFromPNG(pngData)
			if err != nil {
				b.Fatalf("Extract failed: %v", err)
			}
		}
	})
}

func BenchmarkMixFileIndexOperations(b *testing.B) {
	indexManager := mixfile.NewIndexManager()
	chunks := []mixfile.ChunkInfo{
		{Index: 0, URL: "https://example.com/chunk0.png", Hash: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456", Size: 1024},
	}
	metadata := map[string]interface{}{"filename": "test.txt"}

	b.Run("CreateIndex", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := indexManager.CreateIndex(chunks, metadata)
			if err != nil {
				b.Fatalf("Create failed: %v", err)
			}
		}
	})

	index, _ := indexManager.CreateIndex(chunks, metadata)

	b.Run("SerializeIndex", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := indexManager.SerializeIndex(index)
			if err != nil {
				b.Fatalf("Serialize failed: %v", err)
			}
		}
	})

	data, _ := indexManager.SerializeIndex(index)

	b.Run("ParseIndex", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := indexManager.ParseIndex(data)
			if err != nil {
				b.Fatalf("Parse failed: %v", err)
			}
		}
	})
}

func BenchmarkMixFileShareCode(b *testing.B) {
	shareCodeProcessor := mixfile.NewShareCodeProcessor(true)
	indexManager := mixfile.NewIndexManager()

	chunks := []mixfile.ChunkInfo{
		{Index: 0, URL: "https://example.com/chunk0.png", Hash: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456", Size: 1024},
	}
	metadata := map[string]interface{}{
		"filename":    "test.txt",
		"filesize":    int64(1024),
		"chunk_count": 1,
	}

	index, _ := indexManager.CreateIndex(chunks, metadata)
	indexURL := "https://example.com/index.png"

	b.Run("GenerateShareCode", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := shareCodeProcessor.IndexToShareCode(index, indexURL)
			if err != nil {
				b.Fatalf("Generate failed: %v", err)
			}
		}
	})

	shareCode, _ := shareCodeProcessor.IndexToShareCode(index, indexURL)

	b.Run("ParseShareCode", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := shareCodeProcessor.ShareCodeToDownloadInfo(shareCode)
			if err != nil {
				b.Fatalf("Parse failed: %v", err)
			}
		}
	})
}