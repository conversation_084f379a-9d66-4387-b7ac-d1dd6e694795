package mixfile_test

import (
	"bytes"
	"crypto/rand"
	"fmt"
	"testing"
	"time"

	"magnet-downloader/pkg/steganography"
)

func TestSteganographyBasicFunctionality(t *testing.T) {
	config := steganography.DefaultConfig()
	stego := steganography.NewSteganographer(config)

	// 测试数据
	testData := []byte("Hello, MixFile steganography test!")

	t.Run("HideAndExtractData", func(t *testing.T) {
		// 隐藏数据到PNG
		pngData, err := stego.HideDataInPNG(testData)
		if err != nil {
			t.Fatalf("Failed to hide data in PNG: %v", err)
		}

		// 验证PNG格式
		if !isPNGFormat(pngData) {
			t.Error("Generated data is not valid PNG format")
		}

		// 提取数据
		extractedData, err := stego.ExtractDataFromPNG(pngData)
		if err != nil {
			t.Fatalf("Failed to extract data from PNG: %v", err)
		}

		// 验证数据一致性
		if !bytes.Equal(testData, extractedData) {
			t.<PERSON><PERSON>rf("Data mismatch: expected %s, got %s", testData, extractedData)
		}
	})

	t.Run("CalculateImageSize", func(t *testing.T) {
		dataSize := len(testData)
		width, height := stego.CalculateRequiredImageSize(dataSize)

		if width <= 0 || height <= 0 {
			t.Errorf("Invalid image size: %dx%d", width, height)
		}

		// 验证图片大小足够容纳数据
		maxCapacity := width * height * 3 / 8 // 每个像素3位，8位一字节
		if maxCapacity < dataSize+4 { // +4字节长度前缀
			t.Errorf("Image size too small: capacity %d < required %d", maxCapacity, dataSize+4)
		}
	})

	t.Run("EmptyData", func(t *testing.T) {
		emptyData := []byte{}
		
		pngData, err := stego.HideDataInPNG(emptyData)
		if err != nil {
			t.Fatalf("Failed to hide empty data: %v", err)
		}

		extractedData, err := stego.ExtractDataFromPNG(pngData)
		if err != nil {
			t.Fatalf("Failed to extract empty data: %v", err)
		}

		if len(extractedData) != 0 {
			t.Errorf("Expected empty data, got %d bytes", len(extractedData))
		}
	})

	t.Run("LargeData", func(t *testing.T) {
		// 生成1KB随机数据
		largeData := make([]byte, 1024)
		rand.Read(largeData)

		pngData, err := stego.HideDataInPNG(largeData)
		if err != nil {
			t.Fatalf("Failed to hide large data: %v", err)
		}

		extractedData, err := stego.ExtractDataFromPNG(pngData)
		if err != nil {
			t.Fatalf("Failed to extract large data: %v", err)
		}

		if !bytes.Equal(largeData, extractedData) {
			t.Error("Large data mismatch")
		}
	})
}

func TestSteganographyPerformance(t *testing.T) {
	config := steganography.DefaultConfig()
	stego := steganography.NewSteganographer(config)

	// 性能测试数据大小
	testSizes := []int{100, 1024, 10240, 102400} // 100B, 1KB, 10KB, 100KB

	for _, size := range testSizes {
		t.Run(fmt.Sprintf("Performance_%dBytes", size), func(t *testing.T) {
			testData := make([]byte, size)
			rand.Read(testData)

			// 测试隐藏性能
			start := time.Now()
			pngData, err := stego.HideDataInPNG(testData)
			hideTime := time.Since(start)

			if err != nil {
				t.Fatalf("Failed to hide %d bytes: %v", size, err)
			}

			// 测试提取性能
			start = time.Now()
			extractedData, err := stego.ExtractDataFromPNG(pngData)
			extractTime := time.Since(start)

			if err != nil {
				t.Fatalf("Failed to extract %d bytes: %v", size, err)
			}

			// 验证数据一致性
			if !bytes.Equal(testData, extractedData) {
				t.Errorf("Data mismatch for %d bytes", size)
			}

			// 性能指标
			hideSpeed := float64(size) / hideTime.Seconds() / 1024 // KB/s
			extractSpeed := float64(size) / extractTime.Seconds() / 1024 // KB/s

			t.Logf("Size: %d bytes, Hide: %.2f KB/s, Extract: %.2f KB/s, PNG: %d bytes", 
				size, hideSpeed, extractSpeed, len(pngData))

			// 性能要求：至少1KB/s
			if hideSpeed < 1.0 {
				t.Errorf("Hide speed too slow: %.2f KB/s", hideSpeed)
			}
			if extractSpeed < 1.0 {
				t.Errorf("Extract speed too slow: %.2f KB/s", extractSpeed)
			}
		})
	}
}

func TestSteganographyErrorHandling(t *testing.T) {
	config := steganography.DefaultConfig()
	stego := steganography.NewSteganographer(config)

	t.Run("InvalidPNGData", func(t *testing.T) {
		invalidPNG := []byte("not a png file")
		_, err := stego.ExtractDataFromPNG(invalidPNG)
		if err == nil {
			t.Error("Expected error for invalid PNG data")
		}
	})

	t.Run("CorruptedPNGData", func(t *testing.T) {
		// 创建有效PNG然后破坏它
		testData := []byte("test")
		pngData, err := stego.HideDataInPNG(testData)
		if err != nil {
			t.Fatalf("Failed to create PNG: %v", err)
		}

		// 破坏PNG数据
		if len(pngData) > 10 {
			pngData[10] = ^pngData[10] // 翻转一个字节
		}

		_, err = stego.ExtractDataFromPNG(pngData)
		if err == nil {
			t.Error("Expected error for corrupted PNG data")
		}
	})

	t.Run("ExcessiveDataSize", func(t *testing.T) {
		// 尝试隐藏过大的数据
		excessiveData := make([]byte, 10*1024*1024) // 10MB
		_, err := stego.HideDataInPNG(excessiveData)
		if err == nil {
			t.Error("Expected error for excessive data size")
		}
	})
}

func TestSteganographyCompatibility(t *testing.T) {
	config := steganography.DefaultConfig()
	stego := steganography.NewSteganographer(config)

	t.Run("BinaryData", func(t *testing.T) {
		// 测试二进制数据
		binaryData := make([]byte, 256)
		for i := 0; i < 256; i++ {
			binaryData[i] = byte(i)
		}

		pngData, err := stego.HideDataInPNG(binaryData)
		if err != nil {
			t.Fatalf("Failed to hide binary data: %v", err)
		}

		extractedData, err := stego.ExtractDataFromPNG(pngData)
		if err != nil {
			t.Fatalf("Failed to extract binary data: %v", err)
		}

		if !bytes.Equal(binaryData, extractedData) {
			t.Error("Binary data mismatch")
		}
	})

	t.Run("UTF8Text", func(t *testing.T) {
		// 测试UTF-8文本
		utf8Text := []byte("Hello 世界 🌍 MixFile测试")

		pngData, err := stego.HideDataInPNG(utf8Text)
		if err != nil {
			t.Fatalf("Failed to hide UTF-8 text: %v", err)
		}

		extractedData, err := stego.ExtractDataFromPNG(pngData)
		if err != nil {
			t.Fatalf("Failed to extract UTF-8 text: %v", err)
		}

		if !bytes.Equal(utf8Text, extractedData) {
			t.Error("UTF-8 text mismatch")
		}
	})
}

// isPNGFormat 检查是否为PNG格式
func isPNGFormat(data []byte) bool {
	pngSignature := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A}
	if len(data) < len(pngSignature) {
		return false
	}
	return bytes.Equal(data[:len(pngSignature)], pngSignature)
}

// BenchmarkSteganography 基准测试
func BenchmarkSteganographyHide(b *testing.B) {
	config := steganography.DefaultConfig()
	stego := steganography.NewSteganographer(config)
	testData := make([]byte, 1024) // 1KB数据
	rand.Read(testData)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := stego.HideDataInPNG(testData)
		if err != nil {
			b.Fatalf("Hide failed: %v", err)
		}
	}
}

func BenchmarkSteganographyExtract(b *testing.B) {
	config := steganography.DefaultConfig()
	stego := steganography.NewSteganographer(config)
	testData := make([]byte, 1024) // 1KB数据
	rand.Read(testData)

	pngData, err := stego.HideDataInPNG(testData)
	if err != nil {
		b.Fatalf("Setup failed: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := stego.ExtractDataFromPNG(pngData)
		if err != nil {
			b.Fatalf("Extract failed: %v", err)
		}
	}
}