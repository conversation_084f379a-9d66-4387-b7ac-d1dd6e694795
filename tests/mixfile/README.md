# MixFile测试套件

这是MixFile系统的完整测试套件，包含功能测试、性能测试、集成测试等多种测试类型。

## 📋 测试概览

### 测试文件结构
```
tests/mixfile/
├── README.md                 # 本文件
├── steganography_test.go     # 隐写术功能测试
├── integration_test.go       # MixFile集成测试
└── performance_test.go       # 性能和基准测试
```

### 测试覆盖范围

#### 🔐 隐写术测试 (`steganography_test.go`)
- **基础功能测试**
  - 数据隐藏和提取
  - 图片大小计算
  - 空数据处理
  - 大数据处理
- **性能测试**
  - 不同数据大小的处理性能
  - 隐藏和提取速度测试
- **错误处理测试**
  - 无效PNG数据处理
  - 损坏数据处理
  - 过大数据处理
- **兼容性测试**
  - 二进制数据支持
  - UTF-8文本支持

#### 🔗 集成测试 (`integration_test.go`)
- **端到端流程测试**
  - 完整的MixFile工作流验证
  - 文件分片→加密→隐写→索引→分享码→解析→下载→重组
- **索引管理测试**
  - 索引创建和验证
  - 序列化和反序列化
  - 压缩和加密
- **分享码测试**
  - 分享码生成和解析
  - 格式验证
- **下载器测试**
  - 配置验证
  - 进度回调
- **兼容性测试**
  - 向后兼容性
  - 配置开关测试

#### ⚡ 性能测试 (`performance_test.go`)
- **性能基准测试**
  - 隐写术性能测试
  - 索引管理性能测试
  - 分享码性能测试
- **并发测试**
  - 并发隐写术操作
  - 并发索引操作
- **内存使用测试**
  - 内存使用分析
  - 内存泄漏检测
- **基准测试**
  - 各组件的基准性能测试

## 🚀 运行测试

### 快速运行
使用提供的测试脚本运行完整测试套件：
```bash
./scripts/run_mixfile_tests.sh
```

### 单独运行测试

#### 运行隐写术测试
```bash
go test -v ./tests/mixfile/steganography_test.go
```

#### 运行集成测试
```bash
go test -v ./tests/mixfile/integration_test.go
```

#### 运行性能测试
```bash
go test -v ./tests/mixfile/performance_test.go -timeout=5m
```

#### 运行基准测试
```bash
go test -bench=. ./tests/mixfile/ -benchmem
```

#### 运行竞态条件测试
```bash
go test -race -v ./tests/mixfile/
```

#### 生成覆盖率报告
```bash
go test -coverprofile=coverage.out ./pkg/mixfile/ ./pkg/steganography/
go tool cover -html=coverage.out -o coverage.html
```

### 包级别测试
```bash
# 测试隐写术包
go test -v ./pkg/steganography

# 测试MixFile包
go test -v ./pkg/mixfile
```

## 📊 测试结果

### 性能指标
- **隐写术性能**: 目标 > 0.1 MB/s
- **索引操作**: 目标 < 1秒
- **分享码操作**: 目标 < 100ms
- **内存使用**: 目标 < 100MB

### 覆盖率目标
- **代码覆盖率**: 目标 > 80%
- **功能覆盖率**: 目标 > 95%

## 🎯 演示程序

### 编译演示程序
```bash
go build -o mixfile_demo ./cmd/mixfile_demo/
```

### 运行演示程序
```bash
./mixfile_demo
```

演示程序提供以下功能：
1. 创建测试文件并生成分享码
2. 从分享码下载文件
3. 隐写术演示
4. 性能测试
5. 系统信息

## 🔧 测试配置

### 环境要求
- Go 1.19+
- 足够的磁盘空间（用于临时文件）
- 网络连接（用于模拟下载测试）

### 测试参数
可以通过环境变量调整测试参数：
```bash
# 设置测试超时
export TEST_TIMEOUT=10m

# 设置临时目录
export TEST_TEMP_DIR=/tmp/mixfile_test

# 启用详细日志
export TEST_VERBOSE=true
```

## 🐛 故障排除

### 常见问题

#### 测试超时
如果性能测试超时，可以增加超时时间：
```bash
go test -v ./tests/mixfile/performance_test.go -timeout=10m
```

#### 内存不足
如果内存测试失败，可能是系统内存不足：
- 关闭其他应用程序
- 减少测试数据大小
- 增加系统虚拟内存

#### 权限问题
如果临时文件创建失败：
```bash
# 确保临时目录权限
chmod 755 /tmp/mixfile_test
```

### 调试技巧

#### 启用详细日志
```bash
go test -v -args -log.level=debug ./tests/mixfile/
```

#### 运行特定测试
```bash
go test -v -run=TestSpecificFunction ./tests/mixfile/
```

#### 生成CPU性能分析
```bash
go test -cpuprofile=cpu.prof -bench=. ./tests/mixfile/
go tool pprof cpu.prof
```

## 📈 持续集成

### GitHub Actions
可以将测试集成到CI/CD流程中：
```yaml
name: MixFile Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-go@v2
      with:
        go-version: 1.19
    - run: ./scripts/run_mixfile_tests.sh
```

### 测试报告
测试脚本会自动生成：
- 测试日志文件
- 覆盖率报告
- 性能基准报告
- Markdown格式的测试报告

## 🤝 贡献指南

### 添加新测试
1. 在相应的测试文件中添加测试函数
2. 遵循Go测试命名规范（`TestXxx`）
3. 添加适当的错误处理和断言
4. 更新本README文件

### 测试最佳实践
- 使用表驱动测试处理多个测试用例
- 使用`t.TempDir()`创建临时目录
- 使用`testing.Short()`跳过长时间运行的测试
- 添加适当的基准测试
- 确保测试的独立性和可重复性

## 📚 参考资料

- [Go Testing Package](https://golang.org/pkg/testing/)
- [Go Benchmark Guide](https://golang.org/pkg/testing/#hdr-Benchmarks)
- [MixFile系统文档](../../docs/mixfile.md)
- [隐写术技术文档](../../docs/steganography.md)