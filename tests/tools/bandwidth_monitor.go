package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"magnet-downloader/pkg/logger"
)

// BandwidthStats 带宽统计
type BandwidthStats struct {
	Timestamp    time.Time `json:"timestamp"`
	UploadMBps   float64   `json:"upload_mbps"`
	DownloadMBps float64   `json:"download_mbps"`
	TotalMBps    float64   `json:"total_mbps"`
	Utilization  float64   `json:"utilization_percent"`
}

// NetworkMonitor 网络监控器
type NetworkMonitor struct {
	maxBandwidthMBps float64
	stats            []BandwidthStats
	running          bool
}

const (
	// 2.5G带宽 = 312.5 MB/s
	MaxBandwidthMBps = 312.5
	MonitorInterval  = 1 * time.Second
)

func main() {
	fmt.Println("📊 DoodStream带宽监控工具")
	fmt.Println("=========================")
	fmt.Printf("🌐 监控带宽: %.1f MB/s (%.1f Mbps)\n", MaxBandwidthMBps, MaxBandwidthMBps*8)
	fmt.Printf("⏱️  监控间隔: %v\n", MonitorInterval)
	fmt.Println("💡 按 Ctrl+C 停止监控")
	fmt.Println()

	// 创建网络监控器
	monitor := &NetworkMonitor{
		maxBandwidthMBps: MaxBandwidthMBps,
		running:          true,
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动监控
	go monitor.startMonitoring()

	// 等待停止信号
	<-sigChan
	fmt.Println("\n🛑 停止监控...")
	monitor.stop()

	// 生成监控报告
	monitor.generateReport()
}

// startMonitoring 开始监控
func (m *NetworkMonitor) startMonitoring() {
	fmt.Println("🚀 开始监控网络带宽...")
	fmt.Printf("%-20s %-12s %-12s %-12s %-15s\n", 
		"时间", "上传(MB/s)", "下载(MB/s)", "总计(MB/s)", "利用率")
	fmt.Println("-" * 75)

	ticker := time.NewTicker(MonitorInterval)
	defer ticker.Stop()

	for m.running {
		select {
		case <-ticker.C:
			stats := m.collectStats()
			m.stats = append(m.stats, stats)
			m.printStats(stats)
		}
	}
}

// collectStats 收集网络统计数据
func (m *NetworkMonitor) collectStats() BandwidthStats {
	// 这里应该实现实际的网络统计收集
	// 由于这是演示代码，我们模拟一些数据
	
	// 模拟网络使用情况
	// 在实际应用中，可以通过读取 /proc/net/dev 或使用系统API获取真实数据
	uploadMBps := m.simulateNetworkUsage("upload")
	downloadMBps := m.simulateNetworkUsage("download")
	totalMBps := uploadMBps + downloadMBps
	utilization := (totalMBps / m.maxBandwidthMBps) * 100

	return BandwidthStats{
		Timestamp:    time.Now(),
		UploadMBps:   uploadMBps,
		DownloadMBps: downloadMBps,
		TotalMBps:    totalMBps,
		Utilization:  utilization,
	}
}

// simulateNetworkUsage 模拟网络使用情况
func (m *NetworkMonitor) simulateNetworkUsage(direction string) float64 {
	// 模拟不同时间段的网络使用模式
	now := time.Now()
	second := now.Second()
	
	// 模拟上传活动（DoodStream上传）
	if direction == "upload" {
		// 模拟周期性的上传活动
		if second%10 < 5 {
			// 上传期间：模拟高上传带宽使用
			return float64(second%30) * 8.0 // 0-232 MB/s
		} else {
			// 空闲期间：低上传带宽
			return float64(second%5) * 2.0 // 0-8 MB/s
		}
	} else {
		// 模拟下载活动（较少）
		return float64(second%10) * 1.5 // 0-13.5 MB/s
	}
}

// printStats 打印统计数据
func (m *NetworkMonitor) printStats(stats BandwidthStats) {
	timeStr := stats.Timestamp.Format("15:04:05")
	
	// 根据利用率选择颜色指示
	utilizationIndicator := "🟢" // 绿色：正常
	if stats.Utilization > 80 {
		utilizationIndicator = "🔴" // 红色：高利用率
	} else if stats.Utilization > 60 {
		utilizationIndicator = "🟡" // 黄色：中等利用率
	}

	fmt.Printf("%-20s %-12.2f %-12.2f %-12.2f %s%-13.1f%%\n",
		timeStr,
		stats.UploadMBps,
		stats.DownloadMBps,
		stats.TotalMBps,
		utilizationIndicator,
		stats.Utilization,
	)

	// 记录高利用率警告
	if stats.Utilization > 90 {
		logger.Warnf("High bandwidth utilization: %.1f%%", stats.Utilization)
	}
}

// stop 停止监控
func (m *NetworkMonitor) stop() {
	m.running = false
}

// generateReport 生成监控报告
func (m *NetworkMonitor) generateReport() {
	if len(m.stats) == 0 {
		fmt.Println("📊 没有收集到监控数据")
		return
	}

	fmt.Println("\n📈 带宽监控报告")
	fmt.Println("================")

	// 计算统计数据
	var totalUpload, totalDownload, totalBandwidth float64
	var maxUpload, maxDownload, maxTotal float64
	var highUtilizationCount int

	for _, stat := range m.stats {
		totalUpload += stat.UploadMBps
		totalDownload += stat.DownloadMBps
		totalBandwidth += stat.TotalMBps

		if stat.UploadMBps > maxUpload {
			maxUpload = stat.UploadMBps
		}
		if stat.DownloadMBps > maxDownload {
			maxDownload = stat.DownloadMBps
		}
		if stat.TotalMBps > maxTotal {
			maxTotal = stat.TotalMBps
		}

		if stat.Utilization > 80 {
			highUtilizationCount++
		}
	}

	sampleCount := len(m.stats)
	avgUpload := totalUpload / float64(sampleCount)
	avgDownload := totalDownload / float64(sampleCount)
	avgTotal := totalBandwidth / float64(sampleCount)
	avgUtilization := (avgTotal / m.maxBandwidthMBps) * 100

	fmt.Printf("📊 监控时长: %v\n", time.Duration(sampleCount)*MonitorInterval)
	fmt.Printf("📈 采样次数: %d\n", sampleCount)
	fmt.Println()

	fmt.Println("📤 上传带宽统计:")
	fmt.Printf("   平均: %.2f MB/s\n", avgUpload)
	fmt.Printf("   峰值: %.2f MB/s\n", maxUpload)
	fmt.Printf("   利用率: %.1f%%\n", (avgUpload/m.maxBandwidthMBps)*100)
	fmt.Println()

	fmt.Println("📥 下载带宽统计:")
	fmt.Printf("   平均: %.2f MB/s\n", avgDownload)
	fmt.Printf("   峰值: %.2f MB/s\n", maxDownload)
	fmt.Printf("   利用率: %.1f%%\n", (avgDownload/m.maxBandwidthMBps)*100)
	fmt.Println()

	fmt.Println("🌐 总带宽统计:")
	fmt.Printf("   平均: %.2f MB/s\n", avgTotal)
	fmt.Printf("   峰值: %.2f MB/s\n", maxTotal)
	fmt.Printf("   平均利用率: %.1f%%\n", avgUtilization)
	fmt.Printf("   高利用率时段: %d/%d (%.1f%%)\n", 
		highUtilizationCount, sampleCount, 
		float64(highUtilizationCount)/float64(sampleCount)*100)
	fmt.Println()

	// 性能建议
	fmt.Println("💡 性能建议:")
	if avgUtilization < 30 {
		fmt.Println("   📈 带宽利用率较低，可以考虑:")
		fmt.Println("      - 增加并发上传数")
		fmt.Println("      - 优化速率限制参数")
		fmt.Println("      - 检查是否有其他性能瓶颈")
	} else if avgUtilization > 80 {
		fmt.Println("   ⚠️  带宽利用率较高，建议:")
		fmt.Println("      - 监控网络稳定性")
		fmt.Println("      - 适当降低并发数避免拥塞")
		fmt.Println("      - 考虑错峰上传")
	} else {
		fmt.Println("   ✅ 带宽利用率良好")
		fmt.Println("      - 当前配置较为合理")
		fmt.Println("      - 可以根据实际需求微调")
	}

	// 保存报告到文件
	reportFile := fmt.Sprintf("logs/bandwidth_report_%s.txt", 
		time.Now().Format("20060102_150405"))
	
	fmt.Printf("\n💾 详细报告已保存到: %s\n", reportFile)
	
	// 这里可以实现将报告保存到文件的逻辑
	// saveReportToFile(reportFile, m.stats)
}