# 部署指南

本文档详细介绍了磁力下载器的各种部署方式。

## 部署方式概览

- **Docker Compose** (推荐): 适合单机部署，包含所有依赖服务
- **Kubernetes**: 适合集群部署，支持高可用和自动扩缩容
- **手动部署**: 适合开发环境或自定义部署需求

## Docker Compose 部署

### 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 2GB+ 内存
- 10GB+ 磁盘空间

### 快速部署

1. **下载项目**
```bash
git clone https://github.com/your-username/magnet-downloader.git
cd magnet-downloader
```

2. **配置环境变量**
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```bash
# 数据库配置
DB_PASSWORD=your-secure-password
REDIS_PASSWORD=your-redis-password

# JWT密钥
JWT_SECRET=your-jwt-secret-key

# Aria2密钥
ARIA2_SECRET=your-aria2-secret

# 监控配置
GRAFANA_PASSWORD=your-grafana-password
```

3. **启动服务**
```bash
# 生产环境
./scripts/deploy.sh prod up

# 或直接使用docker-compose
docker-compose up -d
```

4. **验证部署**
```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f magnet-downloader
```

### 服务访问

- **Web界面**: http://localhost
- **API接口**: http://localhost/api/v1
- **Grafana监控**: http://localhost:3000
- **Prometheus**: http://localhost:9090

### 数据持久化

数据卷映射：
```yaml
volumes:
  - postgres_data:/var/lib/postgresql/data  # 数据库数据
  - redis_data:/data                        # Redis数据
  - ./downloads:/app/downloads              # 下载文件
  - ./logs:/app/logs                        # 应用日志
```

### 备份与恢复

#### 数据备份
```bash
# 自动备份
./scripts/deploy.sh prod backup

# 手动备份数据库
docker-compose exec postgres pg_dump -U postgres magnet_downloader > backup.sql

# 备份下载文件
tar -czf downloads-backup.tar.gz downloads/
```

#### 数据恢复
```bash
# 恢复数据库
docker-compose exec -T postgres psql -U postgres magnet_downloader < backup.sql

# 恢复下载文件
tar -xzf downloads-backup.tar.gz
```

## Kubernetes 部署

### 系统要求

- Kubernetes 1.20+
- kubectl 配置完成
- 4GB+ 内存
- 20GB+ 磁盘空间

### 部署步骤

1. **创建命名空间**
```bash
kubectl apply -f deployments/k8s/namespace.yaml
```

2. **创建配置**
```bash
kubectl apply -f deployments/k8s/configmap.yaml
kubectl apply -f deployments/k8s/secrets.yaml
```

3. **部署数据库**
```bash
kubectl apply -f deployments/k8s/postgres.yaml
kubectl apply -f deployments/k8s/redis.yaml
```

4. **部署应用**
```bash
kubectl apply -f deployments/k8s/magnet-downloader.yaml
kubectl apply -f deployments/k8s/nginx.yaml
```

5. **创建服务**
```bash
kubectl apply -f deployments/k8s/services.yaml
kubectl apply -f deployments/k8s/ingress.yaml
```

### 扩缩容

```bash
# 扩展应用实例
kubectl scale deployment magnet-downloader --replicas=3

# 自动扩缩容
kubectl apply -f deployments/k8s/hpa.yaml
```

### 监控部署

```bash
# 部署监控栈
kubectl apply -f deployments/k8s/monitoring/
```

## 手动部署

### 系统要求

- Go 1.21+
- PostgreSQL 12+
- Redis 6+
- aria2c
- Node.js 16+ (前端)

### 后端部署

1. **安装依赖**
```bash
go mod download
```

2. **配置文件**
```bash
cp config.example.yaml config.yaml
# 编辑配置文件
```

3. **数据库初始化**
```bash
# 创建数据库
createdb magnet_downloader

# 运行迁移
psql magnet_downloader < deployments/docker/init-db.sql
```

4. **构建应用**
```bash
go build -o bin/magnet-downloader cmd/server/main.go
```

5. **启动服务**
```bash
./bin/magnet-downloader
```

### 前端部署

1. **安装依赖**
```bash
cd web
npm install
```

2. **构建前端**
```bash
npm run build
```

3. **配置Nginx**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /path/to/web/build;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 系统服务配置

创建systemd服务文件 `/etc/systemd/system/magnet-downloader.service`：

```ini
[Unit]
Description=Magnet Downloader
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=magnet
WorkingDirectory=/opt/magnet-downloader
ExecStart=/opt/magnet-downloader/bin/magnet-downloader
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable magnet-downloader
sudo systemctl start magnet-downloader
```

## 生产环境优化

### 性能调优

1. **数据库优化**
```sql
-- PostgreSQL配置优化
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
SELECT pg_reload_conf();
```

2. **Redis优化**
```bash
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

3. **应用配置**
```yaml
database:
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300

redis:
  pool_size: 10
  min_idle_conns: 5

aria2:
  max_concurrent_downloads: 10
  max_connection_per_server: 16
```

### 安全配置

1. **HTTPS配置**
```bash
# 使用Let's Encrypt
certbot --nginx -d your-domain.com
```

2. **防火墙设置**
```bash
# 只开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 22/tcp
ufw enable
```

3. **数据库安全**
```sql
-- 创建专用用户
CREATE USER magnet_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE magnet_downloader TO magnet_user;
GRANT USAGE ON SCHEMA public TO magnet_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO magnet_user;
```

### 监控配置

1. **日志配置**
```yaml
log:
  level: "info"
  format: "json"
  output: "/var/log/magnet-downloader/app.log"
  max_size: 100
  max_backups: 10
  max_age: 30
```

2. **监控告警**
```bash
# 配置Prometheus告警规则
cp deployments/monitoring/alert_rules.yml /etc/prometheus/
```

## 故障排除

### 常见问题

1. **数据库连接失败**
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready

# 查看连接配置
docker-compose logs postgres
```

2. **aria2连接失败**
```bash
# 检查aria2状态
curl http://localhost:6800/jsonrpc

# 重启aria2服务
docker-compose restart aria2
```

3. **前端无法访问**
```bash
# 检查Nginx配置
docker-compose exec nginx nginx -t

# 查看Nginx日志
docker-compose logs nginx
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f magnet-downloader

# 查看最近的错误日志
docker-compose logs --tail=100 magnet-downloader | grep ERROR
```

### 性能监控

```bash
# 查看资源使用情况
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

## 升级指南

### Docker Compose升级

1. **备份数据**
```bash
./scripts/deploy.sh prod backup
```

2. **拉取新版本**
```bash
git pull origin main
```

3. **重新构建镜像**
```bash
docker-compose build --no-cache
```

4. **重启服务**
```bash
docker-compose up -d
```

### 滚动升级

```bash
# Kubernetes滚动升级
kubectl set image deployment/magnet-downloader magnet-downloader=new-image:tag
kubectl rollout status deployment/magnet-downloader
```

## 扩展配置

### 多实例部署

```yaml
# docker-compose.yml
services:
  magnet-downloader:
    deploy:
      replicas: 3
    environment:
      - INSTANCE_ID=${HOSTNAME}
```

### 负载均衡

```nginx
upstream magnet_backend {
    server magnet-downloader-1:8080;
    server magnet-downloader-2:8080;
    server magnet-downloader-3:8080;
}

server {
    location /api/ {
        proxy_pass http://magnet_backend;
    }
}
```

### 数据库集群

```yaml
# 主从复制配置
postgres-master:
  image: postgres:15
  environment:
    - POSTGRES_REPLICATION_MODE=master
    
postgres-slave:
  image: postgres:15
  environment:
    - POSTGRES_REPLICATION_MODE=slave
    - POSTGRES_MASTER_HOST=postgres-master
```
