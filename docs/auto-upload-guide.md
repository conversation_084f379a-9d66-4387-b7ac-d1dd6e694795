# 自动上传功能使用指南

## 📖 概述

自动上传功能是一个智能的视频文件管理系统，能够自动扫描aria2下载目录，识别视频文件，并将其上传到DoodStream。该功能特别适用于自动化的视频内容管理工作流程。

## 🎯 功能特性

### ✅ 智能文件选择
- **单文件优选**：每个文件夹中自动选择最大的视频文件
- **分段文件支持**：自动识别并上传所有分段文件（如 .part1.mp4, .part2.mp4）
- **格式支持**：支持主流视频格式（mp4, avi, mkv, mov, wmv, flv, webm, m4v, 3gp, ts, m2ts, vob）
- **大小过滤**：自动过滤小于10MB的文件

### 🔄 自动化工作流程
- **定时扫描**：每30分钟自动扫描下载目录
- **并发上传**：支持最多3个并发上传任务
- **重复检测**：自动跳过已上传的文件
- **实时监控**：WebSocket实时推送上传进度

### 🛡️ 错误处理
- **智能重试**：上传失败自动重试
- **详细日志**：完整的操作日志记录
- **状态跟踪**：实时跟踪上传状态和进度

## ⚙️ 配置说明

### 基础配置

在 `config.yaml` 中添加以下配置：

```yaml
file_processing:
  # 设置上传提供商为DoodStream
  upload_provider: "doodstream"
  
  # 自动上传配置
  auto_upload:
    enabled: true                    # 启用自动上传
    scan_interval: 30               # 扫描间隔(分钟)
    max_concurrent_uploads: 3       # 最大并发上传数
    min_file_size: 10485760        # 最小文件大小(10MB)
    skip_existing_files: true      # 跳过已存在文件
    delete_after_upload: false     # 上传后不删除原文件
  
  # DoodStream配置
  doodstream:
    api_key: "your_doodstream_api_key"
    base_url: "https://doodapi.co"
    timeout: 300
    max_retries: 3
```

### 环境变量配置

可以通过环境变量覆盖默认设置：

```bash
# 设置下载目录
export DOWNLOAD_DIR="/path/to/your/downloads"

# 设置DoodStream API密钥
export DOODSTREAM_API_KEY="your_api_key"
```

## 📁 目录结构要求

自动上传功能期望以下目录结构：

```
/downloads/                    # 主下载目录
├── movie1/                   # 任务目录1
│   ├── video.mp4            # 主视频文件 ✅ 会被上传
│   ├── subtitle.srt         # 字幕文件 ❌ 会被忽略
│   └── poster.jpg           # 海报图片 ❌ 会被忽略
├── movie2/                   # 任务目录2
│   ├── movie.part1.mp4      # 分段文件1 ✅ 会被上传
│   ├── movie.part2.mp4      # 分段文件2 ✅ 会被上传
│   └── movie.part3.mp4      # 分段文件3 ✅ 会被上传
└── series1/                  # 任务目录3
    ├── episode01.mp4         # 系列文件1 ✅ 会被上传
    ├── episode02.mp4         # 系列文件2 ✅ 会被上传
    └── episode03.mp4         # 系列文件3 ✅ 会被上传
```

### 文件选择规则

1. **单个文件模式**：
   - 如果文件夹中有多个非分段视频文件，选择最大的文件
   - 忽略字幕、图片等非视频文件

2. **分段文件模式**：
   - 自动识别分段文件模式：
     - `filename.part1.mp4`, `filename.part2.mp4`
     - `filename.001.mp4`, `filename.002.mp4`
     - `filename_1.mp4`, `filename_2.mp4`
     - `filename.cd1.mp4`, `filename.cd2.mp4`
   - 上传同一系列的所有分段文件

3. **文件大小过滤**：
   - 自动跳过小于10MB的文件
   - 可通过配置调整最小文件大小

## 🚀 使用方法

### 1. 启动服务

```bash
# 启动主服务（自动上传服务会随主服务启动）
go run cmd/server/main.go
```

### 2. 通过API管理

#### 获取服务状态
```bash
curl -X GET http://localhost:8080/api/v1/auto-upload/status \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 手动触发扫描
```bash
curl -X POST http://localhost:8080/api/v1/auto-upload/scan \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 获取上传统计
```bash
curl -X GET http://localhost:8080/api/v1/auto-upload/stats \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 启动/停止服务（管理员）
```bash
# 启动服务
curl -X POST http://localhost:8080/api/v1/admin/auto-upload/start \
  -H "Authorization: Bearer ADMIN_TOKEN"

# 停止服务
curl -X POST http://localhost:8080/api/v1/admin/auto-upload/stop \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3. 测试功能

运行测试工具验证配置：

```bash
# 测试视频文件选择功能
go run cmd/auto_upload_test/main.go

# 测试DoodStream连接
go run tests/tools/error_handling_demo.go
```

## 📊 监控和统计

### 实时状态监控

通过WebSocket连接可以实时监控上传状态：

```javascript
const ws = new WebSocket('ws://localhost:8080/ws');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    if (data.type === 'system_notification') {
        if (data.data.title.includes('自动上传')) {
            console.log('自动上传通知:', data.data.message);
        }
    }
};
```

### 统计信息

系统提供详细的上传统计：

- **总扫描次数**：累计扫描次数
- **总上传次数**：累计上传任务数
- **成功上传数**：成功完成的上传数
- **失败上传数**：失败的上传数
- **成功率**：上传成功率百分比
- **总数据量**：累计上传的数据量
- **最后上传时间**：最近一次上传的时间

## 🛠️ 故障排除

### 常见问题

#### 1. 服务未启动

**现象**：API返回"自动上传服务未启用"

**解决方案**：
```yaml
# 确保配置正确
file_processing:
  upload_provider: "doodstream"  # 必须设置为doodstream
  auto_upload:
    enabled: true                # 必须启用
```

#### 2. 找不到视频文件

**现象**：扫描结果显示"没有找到视频文件"

**解决方案**：
1. 检查下载目录路径是否正确
2. 确保视频文件在子目录中（不是直接在根目录）
3. 检查文件格式是否支持
4. 确认文件大小大于10MB

#### 3. 上传失败

**现象**：上传任务显示失败状态

**解决方案**：
1. 检查DoodStream API密钥是否正确
2. 验证网络连接
3. 检查文件是否损坏
4. 查看详细错误日志

#### 4. 重复上传

**现象**：同一文件被重复上传

**解决方案**：
```yaml
auto_upload:
  skip_existing_files: true  # 确保启用跳过已存在文件
```

### 日志分析

#### 启用详细日志
```yaml
logging:
  level: "debug"  # 启用调试日志
```

#### 关键日志信息
```
INFO  开始扫描下载目录寻找新视频
INFO  找到 3 个视频文件需要上传
INFO  发现 2 个新视频需要上传
INFO  开始上传视频: movie.mp4
INFO  视频上传成功: movie.mp4 -> https://doodstream.com/e/abc123
```

## 🔧 高级配置

### 自定义文件选择规则

可以通过修改 `pkg/fileprocessor/video_selector.go` 来自定义文件选择规则：

```go
// 自定义最小文件大小
videoSelector := fileprocessor.NewVideoSelector()
videoSelector.SetMinFileSize(50 * 1024 * 1024) // 50MB

// 自定义支持的视频格式
videoSelector.AddVideoExtension(".mkv")
videoSelector.AddVideoExtension(".avi")
```

### 性能调优

根据网络环境调整并发参数：

```yaml
auto_upload:
  max_concurrent_uploads: 5    # 高带宽环境可增加并发数
  scan_interval: 15           # 高频扫描可减少间隔
```

### 集成外部存储

可以扩展支持其他存储服务：

1. 实现新的上传客户端
2. 在配置中添加新的提供商选项
3. 更新自动上传服务以支持新提供商

## 📚 API参考

### 自动上传API端点

| 端点 | 方法 | 描述 | 权限 |
|------|------|------|------|
| `/api/v1/auto-upload/status` | GET | 获取服务状态 | 用户 |
| `/api/v1/auto-upload/stats` | GET | 获取统计信息 | 用户 |
| `/api/v1/auto-upload/info` | GET | 获取服务信息 | 用户 |
| `/api/v1/auto-upload/queue` | GET | 获取上传队列 | 用户 |
| `/api/v1/auto-upload/history` | GET | 获取上传历史 | 用户 |
| `/api/v1/auto-upload/scan` | POST | 手动触发扫描 | 用户 |
| `/api/v1/admin/auto-upload/start` | POST | 启动服务 | 管理员 |
| `/api/v1/admin/auto-upload/stop` | POST | 停止服务 | 管理员 |

### 响应格式

#### 服务状态响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "running": true,
    "last_scan_time": "2025-06-26T10:00:00Z",
    "next_scan_time": "2025-06-26T10:30:00Z",
    "scan_interval_minutes": 30,
    "pending_uploads": 2,
    "active_uploads": 1
  }
}
```

#### 统计信息响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_scans": 48,
    "total_uploads": 25,
    "successful_uploads": 23,
    "failed_uploads": 2,
    "total_data_uploaded": 5368709120,
    "last_upload_time": "2025-06-26T09:45:00Z",
    "upload_success_rate": 92.0
  }
}
```

## 🔄 工作流程集成

### 与aria2集成

自动上传功能可以与aria2下载器无缝集成：

1. aria2完成下载后，文件保存到指定目录
2. 自动上传服务定期扫描该目录
3. 发现新文件后自动开始上传
4. 上传完成后更新数据库记录

### 与Web界面集成

前端可以通过API和WebSocket实时监控：

1. 显示当前上传状态和进度
2. 提供手动触发扫描的按钮
3. 展示上传历史和统计信息
4. 实时接收上传通知

## 📈 性能指标

### 预期性能

在2.5G带宽环境下：

- **扫描速度**：1000个文件/秒
- **上传速度**：根据文件大小和网络条件
- **并发处理**：最多3个同时上传
- **内存使用**：< 100MB
- **CPU使用**：< 5%

### 优化建议

1. **网络优化**：确保稳定的网络连接
2. **存储优化**：使用SSD存储提高文件读取速度
3. **并发调优**：根据网络带宽调整并发数
4. **定时优化**：根据使用模式调整扫描间隔

---

*最后更新: 2025-06-26*
*版本: v1.0*
*作者: 自动上传功能开发团队*