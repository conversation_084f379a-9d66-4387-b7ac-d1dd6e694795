# API文档

## 概述

磁力下载系统提供完整的RESTful API，支持任务管理、文件处理、用户管理等功能。本文档详细介绍了所有可用的API端点。

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 认证

### 登录

```http
POST /auth/login
```

**请求体**:
```json
{
  "username": "admin",
  "password": "password"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "username": "admin",
      "role": "admin"
    }
  }
}
```

## 任务管理

### 获取任务列表

```http
GET /tasks?page=1&limit=10&status=running
```

**查询参数**:
- `page` (int): 页码，默认1
- `limit` (int): 每页数量，默认10，最大100
- `status` (string): 任务状态过滤
- `search` (string): 搜索关键词

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "task_name": "示例视频",
        "magnet_uri": "magnet:?xt=urn:btih:...",
        "status": "completed",
        "progress": 100.0,
        "download_speed": 0,
        "total_size": **********,
        "downloaded_size": **********,
        "play_url": "https://doodstream.com/e/abc123",
        "share_code": "abc123",
        "created_at": "2025-06-26T10:00:00Z",
        "completed_at": "2025-06-26T10:30:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  }
}
```

### 创建下载任务

```http
POST /tasks
```

**请求体**:
```json
{
  "magnet_uri": "magnet:?xt=urn:btih:...",
  "task_name": "示例视频",
  "priority": 2,
  "save_path": "/downloads"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "task_name": "示例视频",
    "status": "pending",
    "created_at": "2025-06-26T10:00:00Z"
  }
}
```

### 获取任务详情

```http
GET /tasks/{id}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "task_name": "示例视频",
    "magnet_uri": "magnet:?xt=urn:btih:...",
    "status": "completed",
    "progress": 100.0,
    "total_size": **********,
    "downloaded_size": **********,
    "play_url": "https://doodstream.com/e/abc123",
    "share_code": "abc123",
    "processing_status": "completed",
    "actual_files": [
      {
        "index": 0,
        "path": "/downloads/video.mp4",
        "name": "video.mp4",
        "size": **********,
        "selected": true
      }
    ],
    "created_at": "2025-06-26T10:00:00Z",
    "completed_at": "2025-06-26T10:30:00Z"
  }
}
```

### 更新任务

```http
PUT /tasks/{id}
```

**请求体**:
```json
{
  "task_name": "新的任务名称",
  "priority": 3
}
```

### 删除任务

```http
DELETE /tasks/{id}
```

### 任务操作

#### 暂停任务
```http
POST /tasks/{id}/pause
```

#### 恢复任务
```http
POST /tasks/{id}/resume
```

#### 取消任务
```http
POST /tasks/{id}/cancel
```

#### 重试任务
```http
POST /tasks/{id}/retry
```

## 文件处理

### 开始文件处理

```http
POST /processing/{task_id}/start
```

**响应**:
```json
{
  "code": 200,
  "message": "文件处理已开始",
  "data": {
    "task_id": 1,
    "status": "processing"
  }
}
```

### 获取处理状态

```http
GET /processing/{task_id}/status
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "task_id": 1,
    "status": "uploading",
    "stage": "DoodStream上传中",
    "progress": 75.5,
    "start_time": "2025-06-26T10:00:00Z",
    "estimated_completion": "2025-06-26T10:05:00Z"
  }
}
```

### 获取处理进度

```http
GET /processing/{task_id}/progress
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "task_id": 1,
    "overall_progress": 75.5,
    "current_stage": "uploading",
    "stage_progress": 75.5,
    "upload_speed": "15.2 MB/s",
    "eta": "00:01:30",
    "details": {
      "provider": "doodstream",
      "file_size": **********,
      "uploaded_size": 810460160,
      "bandwidth_utilization": 68.5
    }
  }
}
```

### 取消文件处理

```http
POST /processing/{task_id}/cancel
```

### 重试文件处理

```http
POST /processing/{task_id}/retry
```

### 获取处理统计

```http
GET /processing/stats
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_tasks": 100,
    "completed_tasks": 95,
    "failed_tasks": 3,
    "processing_tasks": 2,
    "success_rate": 95.0,
    "average_processing_time": "00:05:30",
    "total_data_processed": "500.5 GB",
    "provider_stats": {
      "doodstream": {
        "tasks": 80,
        "success_rate": 97.5,
        "avg_upload_speed": "25.3 MB/s"
      },
      "imgbb": {
        "tasks": 20,
        "success_rate": 85.0,
        "avg_upload_speed": "8.7 MB/s"
      }
    }
  }
}
```

## 配置管理 (管理员)

### 测试DoodStream连接

```http
POST /admin/test/doodstream
```

**请求体**:
```json
{
  "api_key": "your_api_key",
  "base_url": "https://doodapi.co",
  "timeout": 300,
  "max_retries": 3
}
```

**响应**:
```json
{
  "code": 200,
  "message": "DoodStream connection test successful",
  "data": {
    "response_time": "250ms",
    "server_status": "online",
    "api_version": "v1.0"
  }
}
```

### 测试ImgBB连接

```http
POST /admin/test/imgbb
```

**请求体**:
```json
{
  "base_url": "http://localhost:3000",
  "timeout": 30
}
```

### 获取系统配置

```http
GET /admin/config
```

### 更新配置

```http
PUT /admin/config/{key}
```

**请求体**:
```json
{
  "value": "new_value",
  "description": "配置描述"
}
```

### 批量更新配置

```http
PUT /admin/config/batch
```

**请求体**:
```json
{
  "configs": {
    "upload_provider": "doodstream",
    "max_concurrent_uploads": 6,
    "doodstream_timeout": 300
  }
}
```

## 用户管理 (管理员)

### 获取用户列表

```http
GET /admin/users?page=1&limit=10
```

### 创建用户

```http
POST /admin/users
```

**请求体**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

### 更新用户

```http
PUT /admin/users/{id}
```

### 删除用户

```http
DELETE /admin/users/{id}
```

## 监控和统计

### 获取系统统计

```http
GET /admin/stats/system
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "runtime": {
      "uptime": "72h30m15s",
      "goroutines": 45,
      "memory_alloc": 67108864,
      "gc_count": 123
    },
    "database": {
      "connections": 8,
      "max_connections": 25,
      "queries_per_second": 15.7
    },
    "performance": {
      "avg_response_time": "45ms",
      "requests_per_second": 25.3,
      "error_rate": 0.5
    }
  }
}
```

### 获取WebSocket统计

```http
GET /admin/websocket/stats
```

### 获取在线用户

```http
GET /admin/websocket/online-users
```

### 发送广播消息

```http
POST /admin/websocket/broadcast
```

**请求体**:
```json
{
  "title": "系统通知",
  "message": "系统将在10分钟后进行维护",
  "level": "warning"
}
```

## WebSocket事件

### 连接

```javascript
const ws = new WebSocket('ws://localhost:8080/ws');
```

### 事件类型

#### 任务状态更新
```json
{
  "type": "task_status",
  "data": {
    "task_id": 1,
    "status": "downloading",
    "progress": 45.5,
    "download_speed": 1048576
  }
}
```

#### 文件处理进度
```json
{
  "type": "processing_progress",
  "data": {
    "task_id": 1,
    "stage": "uploading",
    "progress": 75.5,
    "upload_speed": "15.2 MB/s",
    "provider": "doodstream"
  }
}
```

#### 任务完成
```json
{
  "type": "task_completed",
  "data": {
    "task_id": 1,
    "play_url": "https://doodstream.com/e/abc123",
    "share_code": "abc123",
    "processing_time": "00:05:30"
  }
}
```

#### 系统通知
```json
{
  "type": "system_notification",
  "data": {
    "title": "系统通知",
    "message": "新版本已发布",
    "level": "info",
    "timestamp": "2025-06-26T10:00:00Z"
  }
}
```

## 错误处理

### 错误响应格式

```json
{
  "code": 400,
  "message": "参数验证失败",
  "error": "validation_error",
  "details": {
    "field": "magnet_uri",
    "reason": "invalid format"
  },
  "timestamp": "2025-06-26T10:00:00Z"
}
```

### 常见错误码

| 状态码 | 错误类型 | 描述 |
|--------|----------|------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未授权访问 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突 |
| 422 | Unprocessable Entity | 业务逻辑错误 |
| 429 | Too Many Requests | 请求过于频繁 |
| 500 | Internal Server Error | 服务器内部错误 |
| 502 | Bad Gateway | 网关错误 |
| 503 | Service Unavailable | 服务不可用 |

### DoodStream特定错误

| 错误类型 | 描述 | 解决方案 |
|----------|------|----------|
| `auth_error` | API密钥无效 | 检查DoodStream API密钥 |
| `rate_limit_error` | 请求过于频繁 | 降低请求频率或等待 |
| `file_size_error` | 文件大小超限 | 检查文件大小限制 |
| `quota_error` | 存储配额不足 | 联系DoodStream支持 |
| `network_error` | 网络连接异常 | 检查网络连接 |

## 限流规则

- **普通用户**: 100请求/分钟
- **管理员**: 500请求/分钟
- **文件上传**: 10个并发连接
- **WebSocket**: 每用户1个连接

## 版本信息

- **API版本**: v1.0
- **最后更新**: 2025-06-26
- **兼容性**: 向后兼容

---

更多详细信息请参考：
- [DoodStream集成指南](./doodstream-integration.md)
- [性能优化报告](./performance_optimization.md)
- [配置参考](./config.md)