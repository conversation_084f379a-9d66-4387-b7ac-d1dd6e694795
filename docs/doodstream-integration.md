# DoodStream集成使用指南

## 📖 概述

本指南详细介绍了如何在磁力下载系统中集成和使用DoodStream视频上传服务。DoodStream集成提供了直接上传完整视频文件的能力，相比传统的分片上传方式，具有更高的性能和更简单的工作流程。

## 🎯 功能特性

### ✅ 核心功能
- **直接上传**：支持完整视频文件直接上传到DoodStream
- **高性能**：充分利用2.5G带宽，支持并发上传
- **智能重试**：内置重试机制和错误处理
- **配置切换**：支持DoodStream和ImgBB两种上传方式
- **实时监控**：WebSocket实时进度推送
- **错误处理**：完善的错误处理和用户友好提示

### 🔄 工作流程对比

**DoodStream模式（新）**：
```
下载完成 → 直接上传到DoodStream → 获取播放链接 → 保存到数据库 → 完成
```

**传统模式（备用）**：
```
下载完成 → 文件分片 → 加密处理 → 批量上传 → 索引生成 → 分享码生成 → 完成
```

## 🚀 快速开始

### 1. 获取DoodStream API密钥

1. 访问 [DoodStream官网](https://doodstream.com)
2. 注册账户并登录
3. 进入API设置页面
4. 生成API密钥

### 2. 配置系统

编辑 `config.yaml` 文件：

```yaml
file_processing:
  # 设置上传提供商为DoodStream
  upload_provider: "doodstream"
  
  # DoodStream配置
  doodstream:
    api_key: "your_doodstream_api_key_here"
    base_url: "https://doodapi.co"
    timeout: 300  # 5分钟超时
    max_retries: 3
```

### 3. 启动系统

```bash
# 启动主服务
go run cmd/server/main.go

# 或使用Docker
docker-compose up -d
```

### 4. 验证配置

通过Web管理界面验证DoodStream配置：

1. 访问 `http://localhost:8080/admin`
2. 进入"系统设置" → "配置管理"
3. 点击"测试DoodStream连接"按钮
4. 确认连接测试成功

## ⚙️ 详细配置

### 基础配置

```yaml
file_processing:
  upload_provider: "doodstream"  # 上传提供商
  auto_start_processing: true    # 下载完成后自动处理
  max_concurrent_uploads: 6      # 最大并发上传数
  
  doodstream:
    api_key: "${DOODSTREAM_API_KEY}"  # API密钥（支持环境变量）
    base_url: "https://doodapi.co"    # API基础URL
    timeout: 300                      # 上传超时时间（秒）
    max_retries: 3                    # 最大重试次数
```

### 性能优化配置

```yaml
file_processing:
  doodstream:
    # 性能优化参数
    rate_limit_interval: 50        # 速率限制间隔（毫秒）
    connection_pool_size: 10       # 连接池大小
    keep_alive_timeout: 30         # 连接保持时间
    
    # 文件大小分级配置
    size_based_concurrency:
      enabled: true
      small_file_threshold: 52428800    # 50MB
      medium_file_threshold: 209715200  # 200MB
      small_file_concurrency: 8         # 小文件并发数
      medium_file_concurrency: 6        # 中等文件并发数
      large_file_concurrency: 4         # 大文件并发数
```

### 监控和告警配置

```yaml
monitoring:
  enabled: true
  metrics_interval: 30
  
  alerts:
    error_rate_threshold: 5.0      # 错误率告警阈值
    response_time_threshold: 10000 # 响应时间告警阈值（毫秒）
    bandwidth_utilization: 90.0    # 带宽利用率告警阈值
```

## 🔧 API使用

### 编程接口

```go
package main

import (
    "magnet-downloader/pkg/doodstream"
    "time"
)

func main() {
    // 创建DoodStream客户端
    config := &doodstream.Config{
        APIKey:     "your_api_key",
        BaseURL:    "https://doodapi.co",
        Timeout:    300 * time.Second,
        MaxRetries: 3,
    }
    
    client := doodstream.NewClient(config)
    
    // 测试连接
    if err := client.TestConnection(); err != nil {
        log.Fatalf("连接测试失败: %v", err)
    }
    
    // 上传文件
    result, err := client.UploadFile("/path/to/video.mp4")
    if err != nil {
        log.Fatalf("上传失败: %v", err)
    }
    
    fmt.Printf("上传成功！播放链接: %s\n", result.PlayURL)
}
```

### REST API

#### 测试DoodStream连接

```bash
curl -X POST http://localhost:8080/api/v1/admin/test/doodstream \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "api_key": "your_api_key",
    "base_url": "https://doodapi.co",
    "timeout": 300,
    "max_retries": 3
  }'
```

#### 获取任务状态

```bash
curl -X GET http://localhost:8080/api/v1/tasks/123 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

响应示例：
```json
{
  "id": 123,
  "task_name": "示例视频",
  "status": "completed",
  "play_url": "https://doodstream.com/e/abc123",
  "share_code": "abc123",
  "file_size": 104857600,
  "processing_status": "completed"
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. 连接测试失败

**问题**：DoodStream连接测试失败
```
❌ DoodStream连接失败: auth_error: API密钥无效
```

**解决方案**：
1. 检查API密钥是否正确
2. 确认API密钥是否已激活
3. 检查网络连接是否正常
4. 验证DoodStream服务状态

#### 2. 上传超时

**问题**：大文件上传超时
```
❌ 上传失败: timeout_error: 上传超时
```

**解决方案**：
1. 增加超时时间：
   ```yaml
   doodstream:
     timeout: 600  # 增加到10分钟
   ```
2. 检查网络带宽
3. 降低并发上传数

#### 3. 速率限制错误

**问题**：请求过于频繁
```
❌ 上传失败: rate_limit_error: 请求过于频繁
```

**解决方案**：
1. 增加速率限制间隔：
   ```yaml
   doodstream:
     rate_limit_interval: 100  # 增加到100ms
   ```
2. 降低并发数
3. 等待一段时间后重试

#### 4. 文件大小限制

**问题**：文件大小超出限制
```
❌ 上传失败: file_size_error: 文件大小超出限制
```

**解决方案**：
1. 检查文件大小是否超过5GB
2. 压缩视频文件
3. 联系DoodStream支持提高限制

### 错误代码参考

| 错误类型 | 错误代码 | 描述 | 解决方案 |
|----------|----------|------|----------|
| `network_error` | NET001 | 网络连接异常 | 检查网络设置 |
| `timeout_error` | TMO001 | 上传超时 | 增加超时时间 |
| `auth_error` | AUTH001 | API密钥无效 | 检查API密钥 |
| `rate_limit_error` | RATE001 | 请求过于频繁 | 降低请求频率 |
| `file_size_error` | FILE001 | 文件大小超限 | 检查文件大小 |
| `server_error` | SRV001 | 服务器错误 | 稍后重试 |

### 日志分析

#### 启用详细日志

```yaml
logging:
  level: "debug"  # 启用调试日志
  format: "json"
  output: "stdout"
```

#### 关键日志示例

**成功上传**：
```json
{
  "level": "info",
  "msg": "DoodStream upload completed successfully",
  "file_name": "video.mp4",
  "file_size": 104857600,
  "play_url": "https://doodstream.com/e/abc123",
  "duration": "45.2s"
}
```

**上传失败**：
```json
{
  "level": "error",
  "msg": "DoodStream upload failed",
  "error_type": "timeout_error",
  "error_message": "上传超时",
  "file_name": "video.mp4",
  "attempt": 3
}
```

## 📊 性能监控

### 关键指标

1. **上传成功率**：目标 >95%
2. **平均上传时间**：根据文件大小而定
3. **带宽利用率**：目标 60-80%
4. **错误率**：目标 <5%

### 监控工具

#### 1. 实时带宽监控

```bash
# 启动带宽监控
go run tests/bandwidth_monitor.go
```

#### 2. 性能测试

```bash
# 运行性能测试
./scripts/performance_test.sh
```

#### 3. 并发测试

```bash
# 运行并发测试
go run tests/concurrent_benchmark.go
```

### 性能优化建议

#### 1. 并发数优化

根据文件大小动态调整：
- 小文件（<50MB）：8个并发
- 中等文件（50-200MB）：6个并发
- 大文件（>200MB）：4个并发

#### 2. 网络优化

```yaml
# 网络优化配置
doodstream:
  connection_pool_size: 10
  keep_alive_timeout: 30
  tcp_keep_alive: true
```

#### 3. 重试策略优化

```yaml
# 智能重试配置
doodstream:
  max_retries: 3
  retry_backoff: "exponential"  # 指数退避
  retry_jitter: true            # 添加随机抖动
```

## 🔐 安全考虑

### API密钥管理

1. **环境变量**：
   ```bash
   export DOODSTREAM_API_KEY="your_api_key"
   ```

2. **配置文件加密**：
   ```yaml
   doodstream:
     api_key: "${DOODSTREAM_API_KEY}"  # 使用环境变量
   ```

3. **密钥轮换**：定期更换API密钥

### 网络安全

1. **HTTPS通信**：确保所有API调用使用HTTPS
2. **防火墙配置**：限制出站连接到DoodStream域名
3. **访问控制**：限制管理员权限

## 📚 相关资源

### 官方文档
- [DoodStream API文档](https://doodapi.co/docs)
- [DoodStream开发者指南](https://doodstream.com/developers)

### 项目文档
- [性能优化报告](./performance_optimization.md)
- [API文档](../api/README.md)
- [配置参考](../config/README.md)

### 工具和脚本
- [性能测试脚本](../scripts/performance_test.sh)
- [监控工具](../tests/bandwidth_monitor.go)
- [配置验证工具](../scripts/validate_config.sh)

## 🆘 技术支持

### 社区支持
- GitHub Issues: [项目Issues页面]
- 讨论区: [项目Discussions页面]

### 商业支持
- 邮箱: <EMAIL>
- 电话: +86-xxx-xxxx-xxxx

---

*最后更新: 2025-06-26*
*版本: v1.0*
*作者: DoodStream集成团队*