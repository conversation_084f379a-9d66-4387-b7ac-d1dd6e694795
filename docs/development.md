# 开发指南

本文档为磁力下载器项目的开发者提供详细的开发指南。

## 开发环境搭建

### 系统要求

- Go 1.21+
- Node.js 16+
- Docker & Docker Compose
- Git
- IDE (推荐 VS Code 或 GoLand)

### 环境配置

1. **克隆项目**
```bash
git clone https://github.com/your-username/magnet-downloader.git
cd magnet-downloader
```

2. **安装Go依赖**
```bash
go mod download
go mod tidy
```

3. **安装前端依赖**
```bash
cd web
npm install
cd ..
```

4. **启动开发环境**
```bash
# 启动基础服务（数据库、Redis等）
./scripts/deploy.sh dev up

# 或者使用docker-compose
docker-compose -f docker-compose.dev.yml up -d
```

### IDE配置

#### VS Code 推荐插件

```json
{
  "recommendations": [
    "golang.go",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-azuretools.vscode-docker"
  ]
}
```

#### Go开发配置

`.vscode/settings.json`:
```json
{
  "go.useLanguageServer": true,
  "go.formatTool": "goimports",
  "go.lintTool": "golangci-lint",
  "go.testFlags": ["-v"],
  "go.buildTags": "integration",
  "editor.formatOnSave": true
}
```

## 项目结构详解

```
magnet-downloader/
├── cmd/                    # 应用程序入口点
│   └── server/            # 主服务器应用
├── internal/              # 私有应用代码
│   ├── api/               # API层
│   │   ├── handler/       # HTTP处理器
│   │   ├── middleware/    # 中间件
│   │   └── router/        # 路由配置
│   ├── service/           # 业务逻辑层
│   ├── repository/        # 数据访问层
│   ├── model/             # 数据模型
│   ├── config/            # 配置管理
│   └── scheduler/         # 任务调度
├── pkg/                   # 公共库代码
│   ├── logger/            # 日志工具
│   ├── database/          # 数据库工具
│   ├── redis/             # Redis工具
│   ├── auth/              # 认证工具
│   └── utils/             # 通用工具
├── web/                   # 前端React应用
│   ├── src/
│   │   ├── components/    # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   ├── types/         # TypeScript类型
│   │   └── utils/         # 工具函数
│   ├── public/            # 静态资源
│   └── package.json       # 前端依赖
├── deployments/           # 部署配置
│   ├── docker/            # Docker配置
│   ├── k8s/               # Kubernetes配置
│   └── monitoring/        # 监控配置
├── scripts/               # 构建和部署脚本
├── docs/                  # 项目文档
└── tests/                 # 测试文件
```

## 开发工作流

### 后端开发

1. **启动开发服务器**
```bash
# 使用Air热重载
air

# 或直接运行
go run cmd/server/main.go
```

2. **代码格式化**
```bash
# 格式化代码
go fmt ./...

# 使用goimports
goimports -w .
```

3. **代码检查**
```bash
# 使用golangci-lint
golangci-lint run

# 或使用go vet
go vet ./...
```

4. **运行测试**
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/service

# 运行测试并生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### 前端开发

1. **启动开发服务器**
```bash
cd web
npm start
```

2. **代码检查**
```bash
# ESLint检查
npm run lint

# TypeScript类型检查
npm run type-check
```

3. **运行测试**
```bash
# 运行单元测试
npm test

# 运行E2E测试
npm run test:e2e
```

4. **构建生产版本**
```bash
npm run build
```

## 编码规范

### Go代码规范

1. **命名规范**
```go
// 包名使用小写
package service

// 接口名使用名词，通常以er结尾
type TaskManager interface {
    CreateTask(task *model.Task) error
}

// 结构体使用大驼峰
type TaskService struct {
    repo repository.TaskRepository
}

// 方法名使用大驼峰（公开）或小驼峰（私有）
func (s *TaskService) CreateTask(task *model.Task) error {
    return s.repo.Create(task)
}

// 常量使用大驼峰或全大写
const (
    DefaultTimeout = 30 * time.Second
    MAX_RETRIES    = 3
)
```

2. **错误处理**
```go
// 使用errors.New或fmt.Errorf创建错误
func (s *TaskService) GetTask(id uint) (*model.Task, error) {
    task, err := s.repo.GetByID(id)
    if err != nil {
        return nil, fmt.Errorf("failed to get task %d: %w", id, err)
    }
    
    if task == nil {
        return nil, errors.New("task not found")
    }
    
    return task, nil
}
```

3. **结构体标签**
```go
type Task struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    MagnetURI   string    `json:"magnet_uri" gorm:"not null" validate:"required"`
    TaskName    string    `json:"task_name" gorm:"size:255"`
    Status      string    `json:"status" gorm:"default:pending"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### TypeScript代码规范

1. **接口定义**
```typescript
// 使用PascalCase命名接口
interface DownloadTask {
  id: number;
  magnetUri: string;
  taskName: string;
  status: TaskStatus;
  progress: number;
  createdAt: string;
}

// 使用enum定义常量
enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
}
```

2. **组件定义**
```typescript
// 使用函数组件和TypeScript
interface TaskListProps {
  tasks: DownloadTask[];
  onTaskSelect: (task: DownloadTask) => void;
}

const TaskList: React.FC<TaskListProps> = ({ tasks, onTaskSelect }) => {
  return (
    <div>
      {tasks.map(task => (
        <TaskItem 
          key={task.id} 
          task={task} 
          onClick={() => onTaskSelect(task)} 
        />
      ))}
    </div>
  );
};
```

## 测试指南

### 后端测试

1. **单元测试**
```go
// service_test.go
func TestTaskService_CreateTask(t *testing.T) {
    // 准备测试数据
    mockRepo := &mocks.TaskRepository{}
    service := NewTaskService(mockRepo)
    
    task := &model.Task{
        MagnetURI: "magnet:?xt=urn:btih:test",
        TaskName:  "Test Task",
    }
    
    // 设置mock期望
    mockRepo.On("Create", task).Return(nil)
    
    // 执行测试
    err := service.CreateTask(task)
    
    // 验证结果
    assert.NoError(t, err)
    mockRepo.AssertExpectations(t)
}
```

2. **集成测试**
```go
// integration_test.go
func TestTaskAPI_Integration(t *testing.T) {
    // 设置测试数据库
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    // 创建测试服务器
    router := setupTestRouter(db)
    server := httptest.NewServer(router)
    defer server.Close()
    
    // 执行API测试
    resp, err := http.Post(server.URL+"/api/v1/tasks", "application/json", 
        strings.NewReader(`{"magnet_uri":"magnet:?xt=urn:btih:test"}`))
    
    assert.NoError(t, err)
    assert.Equal(t, http.StatusCreated, resp.StatusCode)
}
```

### 前端测试

1. **组件测试**
```typescript
// TaskList.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import TaskList from './TaskList';

describe('TaskList', () => {
  const mockTasks = [
    { id: 1, taskName: 'Test Task', status: 'running' },
  ];

  it('renders task list correctly', () => {
    render(<TaskList tasks={mockTasks} onTaskSelect={jest.fn()} />);
    
    expect(screen.getByText('Test Task')).toBeInTheDocument();
  });

  it('calls onTaskSelect when task is clicked', () => {
    const mockOnSelect = jest.fn();
    render(<TaskList tasks={mockTasks} onTaskSelect={mockOnSelect} />);
    
    fireEvent.click(screen.getByText('Test Task'));
    expect(mockOnSelect).toHaveBeenCalledWith(mockTasks[0]);
  });
});
```

## 数据库开发

### 迁移管理

1. **创建迁移**
```bash
# 创建新的迁移文件
migrate create -ext sql -dir migrations add_user_table
```

2. **迁移文件示例**
```sql
-- 001_create_users_table.up.sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 001_create_users_table.down.sql
DROP TABLE users;
```

3. **执行迁移**
```bash
# 应用迁移
migrate -path migrations -database "postgres://user:pass@localhost/db?sslmode=disable" up

# 回滚迁移
migrate -path migrations -database "postgres://user:pass@localhost/db?sslmode=disable" down 1
```

### GORM模型

```go
type User struct {
    ID        uint      `gorm:"primaryKey"`
    Username  string    `gorm:"uniqueIndex;size:50;not null"`
    Email     string    `gorm:"uniqueIndex;size:100;not null"`
    Password  string    `gorm:"size:255;not null"`
    Role      string    `gorm:"size:20;default:user"`
    CreatedAt time.Time
    UpdatedAt time.Time
    
    // 关联关系
    Tasks []Task `gorm:"foreignKey:UserID"`
}

// 表名
func (User) TableName() string {
    return "users"
}

// 钩子函数
func (u *User) BeforeCreate(tx *gorm.DB) error {
    // 密码加密等逻辑
    return nil
}
```

## API开发

### 路由定义

```go
// router/router.go
func SetupRoutes(r *gin.Engine, handlers *handler.Handlers) {
    api := r.Group("/api/v1")
    {
        // 认证路由
        auth := api.Group("/auth")
        {
            auth.POST("/login", handlers.Auth.Login)
            auth.POST("/logout", middleware.AuthRequired(), handlers.Auth.Logout)
        }
        
        // 任务路由
        tasks := api.Group("/tasks")
        tasks.Use(middleware.AuthRequired())
        {
            tasks.GET("", handlers.Task.List)
            tasks.POST("", handlers.Task.Create)
            tasks.GET("/:id", handlers.Task.Get)
            tasks.PUT("/:id", handlers.Task.Update)
            tasks.DELETE("/:id", handlers.Task.Delete)
        }
    }
}
```

### 处理器实现

```go
// handler/task_handler.go
func (h *TaskHandler) Create(c *gin.Context) {
    var req CreateTaskRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        middleware.ValidationErrorResponse(c, err.Error())
        return
    }
    
    userID := middleware.GetUserID(c)
    task := &model.Task{
        MagnetURI: req.MagnetURI,
        TaskName:  req.TaskName,
        UserID:    userID,
    }
    
    if err := h.service.CreateTask(task); err != nil {
        middleware.ErrorResponse(c, http.StatusInternalServerError, "Failed to create task", err)
        return
    }
    
    middleware.SuccessResponse(c, task)
}
```

## 调试技巧

### 后端调试

1. **使用Delve调试器**
```bash
# 安装delve
go install github.com/go-delve/delve/cmd/dlv@latest

# 启动调试
dlv debug cmd/server/main.go
```

2. **日志调试**
```go
import "magnet-downloader/pkg/logger"

func (s *TaskService) CreateTask(task *model.Task) error {
    logger.Infof("Creating task: %+v", task)
    
    if err := s.repo.Create(task); err != nil {
        logger.Errorf("Failed to create task: %v", err)
        return err
    }
    
    logger.Infof("Task created successfully: %d", task.ID)
    return nil
}
```

### 前端调试

1. **浏览器开发者工具**
```typescript
// 使用console.log调试
console.log('Task data:', task);

// 使用debugger断点
debugger;

// 使用React DevTools
```

2. **网络请求调试**
```typescript
// 在API服务中添加请求日志
const apiService = axios.create({
  baseURL: '/api/v1',
});

apiService.interceptors.request.use(request => {
  console.log('Starting Request:', request);
  return request;
});

apiService.interceptors.response.use(
  response => {
    console.log('Response:', response);
    return response;
  },
  error => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);
```

## 性能优化

### 后端优化

1. **数据库查询优化**
```go
// 使用预加载避免N+1查询
var tasks []model.Task
db.Preload("User").Find(&tasks)

// 使用索引
type Task struct {
    UserID uint   `gorm:"index"`
    Status string `gorm:"index"`
}

// 分页查询
func (r *taskRepository) List(offset, limit int) ([]*model.Task, error) {
    var tasks []*model.Task
    err := r.db.Offset(offset).Limit(limit).Find(&tasks).Error
    return tasks, err
}
```

2. **缓存优化**
```go
// Redis缓存
func (s *TaskService) GetTask(id uint) (*model.Task, error) {
    // 先从缓存获取
    cacheKey := fmt.Sprintf("task:%d", id)
    if cached, err := s.redis.Get(cacheKey); err == nil {
        var task model.Task
        json.Unmarshal([]byte(cached), &task)
        return &task, nil
    }
    
    // 从数据库获取
    task, err := s.repo.GetByID(id)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    data, _ := json.Marshal(task)
    s.redis.Set(cacheKey, data, time.Hour)
    
    return task, nil
}
```

### 前端优化

1. **组件优化**
```typescript
// 使用React.memo避免不必要的重渲染
const TaskItem = React.memo<TaskItemProps>(({ task, onClick }) => {
  return (
    <div onClick={() => onClick(task)}>
      {task.taskName}
    </div>
  );
});

// 使用useMemo缓存计算结果
const TaskList: React.FC<TaskListProps> = ({ tasks, filter }) => {
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => task.status === filter);
  }, [tasks, filter]);
  
  return <div>{/* 渲染逻辑 */}</div>;
};
```

2. **代码分割**
```typescript
// 路由级别的代码分割
const TaskPage = lazy(() => import('./pages/TaskPage'));
const UserPage = lazy(() => import('./pages/UserPage'));

function App() {
  return (
    <Suspense fallback={<Loading />}>
      <Routes>
        <Route path="/tasks" element={<TaskPage />} />
        <Route path="/users" element={<UserPage />} />
      </Routes>
    </Suspense>
  );
}
```

## 贡献指南

### 提交规范

使用Conventional Commits规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型说明：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(api): add task priority support

Add priority field to task model and update API endpoints
to support task priority management.

Closes #123
```

### Pull Request流程

1. Fork项目
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'feat: add amazing feature'`
4. 推送分支：`git push origin feature/amazing-feature`
5. 创建Pull Request

### 代码审查

Pull Request需要满足：
- [ ] 所有测试通过
- [ ] 代码覆盖率不降低
- [ ] 遵循编码规范
- [ ] 包含必要的文档更新
- [ ] 至少一个维护者审查通过
