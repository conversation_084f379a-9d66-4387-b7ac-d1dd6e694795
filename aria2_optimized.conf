# aria2 优化配置 - 大规模种子队列处理
# 适用于几万到几十万种子队列

## 基础设置
dir=/tmp/downloads
log=/tmp/aria2.log
log-level=info

## RPC设置
enable-rpc=true
rpc-listen-all=true
rpc-allow-origin-all=true
rpc-listen-port=6800
rpc-max-request-size=10M

## 并发下载优化 (从5提升到100)
max-concurrent-downloads=100
max-connection-per-server=16
split=16

## 队列容量优化 (从1000提升到100000)
max-download-result=100000
save-session=/tmp/aria2.session
save-session-interval=60

## 文件描述符优化 (利用系统的1048575限制)
rlimit-nofile=65536

## 内存和磁盘优化
disk-cache=1024M
file-allocation=falloc
no-file-allocation-limit=64M
piece-length=4M
min-split-size=1M

## BitTorrent优化
bt-max-peers=1000
bt-max-open-files=1000
bt-request-peer-speed-limit=1M
enable-peer-exchange=true
enable-dht=true
enable-dht6=true
bt-enable-lpd=true
bt-hash-check-seed=true
bt-seed-unverified=false
bt-save-metadata=true
bt-load-saved-metadata=true

## DHT设置
dht-file-path=/tmp/aria2_dht.dat
dht-file-path6=/tmp/aria2_dht6.dat
dht-listen-port=6881-6999

## 网络优化
listen-port=6881-6999
max-overall-download-limit=0
max-overall-upload-limit=0
max-upload-limit=0

## 连接优化
timeout=30
connect-timeout=30
bt-tracker-timeout=30
bt-tracker-connect-timeout=30
retry-wait=3
max-tries=5

## 性能优化
optimize-concurrent-downloads=true
auto-save-interval=60
summary-interval=0
show-console-readout=false

## 其他优化
continue=true
always-resume=true
check-integrity=false
realtime-chunk-checksum=true
